#!/usr/bin/env python3
"""
Diagnostic du problème de réinitialisation
"""

import sys
import os
import sqlite3

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_database_state():
    """Vérifie l'état actuel de la base de données"""
    print("🔍 DIAGNOSTIC DE L'ÉTAT DE LA BASE DE DONNÉES")
    print("=" * 50)
    
    db_path = 'sotramine.db'
    
    if not os.path.exists(db_path):
        print("❌ Base de données non trouvée")
        return
    
    print(f"✓ Base de données trouvée : {db_path}")
    print(f"✓ Taille du fichier : {os.path.getsize(db_path)} bytes")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Lister toutes les tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"\n📋 TABLES DANS LA BASE DE DONNÉES ({len(tables)}) :")
        for table in tables:
            table_name = table[0]
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"   • {table_name} : {count} enregistrement(s)")
        
        # Vérifier les données spécifiques
        print(f"\n📊 DÉTAIL DES DONNÉES :")
        
        # Tâches
        try:
            cursor.execute("SELECT COUNT(*) FROM tasks")
            tasks_count = cursor.fetchone()[0]
            print(f"   • Tâches : {tasks_count}")
            
            if tasks_count > 0:
                cursor.execute("SELECT id, title FROM tasks LIMIT 3")
                tasks = cursor.fetchall()
                for task in tasks:
                    print(f"     - ID {task[0]} : {task[1]}")
        except:
            print("   • Tâches : Table non trouvée")
        
        # Équipements
        try:
            cursor.execute("SELECT COUNT(*) FROM equipment")
            equipment_count = cursor.fetchone()[0]
            print(f"   • Équipements : {equipment_count}")
            
            if equipment_count > 0:
                cursor.execute("SELECT id, name FROM equipment LIMIT 3")
                equipment = cursor.fetchall()
                for eq in equipment:
                    print(f"     - ID {eq[0]} : {eq[1]}")
        except:
            print("   • Équipements : Table non trouvée")
        
        # Personnel
        try:
            cursor.execute("SELECT COUNT(*) FROM persons")
            persons_count = cursor.fetchone()[0]
            print(f"   • Personnel : {persons_count}")
            
            if persons_count > 0:
                cursor.execute("SELECT id, first_name, last_name FROM persons LIMIT 3")
                persons = cursor.fetchall()
                for person in persons:
                    print(f"     - ID {person[0]} : {person[1]} {person[2]}")
        except:
            print("   • Personnel : Table non trouvée")
        
        # Pièces de rechange
        try:
            cursor.execute("SELECT COUNT(*) FROM spare_parts")
            parts_count = cursor.fetchone()[0]
            print(f"   • Pièces de rechange : {parts_count}")
            
            if parts_count > 0:
                cursor.execute("SELECT id, name FROM spare_parts LIMIT 3")
                parts = cursor.fetchall()
                for part in parts:
                    print(f"     - ID {part[0]} : {part[1]}")
        except:
            print("   • Pièces de rechange : Table non trouvée")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Erreur lors de l'analyse : {str(e)}")

def check_reset_methods():
    """Vérifie les méthodes de réinitialisation"""
    print(f"\n🔧 DIAGNOSTIC DES MÉTHODES DE RÉINITIALISATION")
    print("=" * 50)
    
    try:
        from gui.reset_dialog import ResetThread
        from database import Database
        
        print("✓ Modules de réinitialisation importés")
        
        # Créer une instance pour tester
        db = Database()
        reset_thread = ResetThread(db, {})
        
        # Vérifier les méthodes
        methods = [
            '_create_backup',
            '_reset_database', 
            '_reset_exports',
            '_reset_config'
        ]
        
        print(f"\n📋 MÉTHODES DE RÉINITIALISATION :")
        for method in methods:
            if hasattr(reset_thread, method):
                print(f"   ✓ {method}")
            else:
                print(f"   ❌ {method} manquante")
        
        db.close()
        
    except Exception as e:
        print(f"❌ Erreur import modules : {str(e)}")

def test_manual_reset():
    """Test de réinitialisation manuelle"""
    print(f"\n🧪 TEST DE RÉINITIALISATION MANUELLE")
    print("=" * 50)
    
    db_path = 'sotramine.db'
    
    if not os.path.exists(db_path):
        print("❌ Aucune base de données à réinitialiser")
        return
    
    # Sauvegarder d'abord
    backup_path = 'backup_manual_test.db'
    try:
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"✓ Sauvegarde créée : {backup_path}")
    except Exception as e:
        print(f"❌ Erreur sauvegarde : {str(e)}")
        return
    
    # Compter les données avant
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM tasks")
        tasks_before = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM equipment")
        equipment_before = cursor.fetchone()[0]
        
        print(f"📊 AVANT RÉINITIALISATION :")
        print(f"   • Tâches : {tasks_before}")
        print(f"   • Équipements : {equipment_before}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Erreur lecture avant : {str(e)}")
        return
    
    # Tenter la suppression manuelle
    try:
        print(f"\n🗑️ SUPPRESSION MANUELLE DU FICHIER...")
        os.remove(db_path)
        print(f"✓ Fichier {db_path} supprimé")
        
        # Vérifier la suppression
        if os.path.exists(db_path):
            print(f"❌ Le fichier existe encore après suppression !")
        else:
            print(f"✓ Fichier effectivement supprimé")
        
        # Recréer la base
        print(f"\n🔄 RECRÉATION DE LA BASE...")
        from database import Database
        db = Database()
        
        # Vérifier les données après
        cursor = db.cursor
        
        cursor.execute("SELECT COUNT(*) FROM tasks")
        tasks_after = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM equipment")
        equipment_after = cursor.fetchone()[0]
        
        print(f"📊 APRÈS RÉINITIALISATION :")
        print(f"   • Tâches : {tasks_after}")
        print(f"   • Équipements : {equipment_after}")
        
        if tasks_after == 0 and equipment_after == 0:
            print(f"✅ RÉINITIALISATION MANUELLE RÉUSSIE !")
        else:
            print(f"❌ Des données persistent après réinitialisation")
        
        db.close()
        
    except Exception as e:
        print(f"❌ Erreur suppression manuelle : {str(e)}")
        
        # Restaurer la sauvegarde
        try:
            shutil.copy2(backup_path, db_path)
            print(f"✓ Sauvegarde restaurée")
        except:
            print(f"❌ Erreur restauration sauvegarde")

def check_file_permissions():
    """Vérifie les permissions sur les fichiers"""
    print(f"\n🔒 VÉRIFICATION DES PERMISSIONS")
    print("=" * 50)
    
    files_to_check = ['sotramine.db', 'export', '.']
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            try:
                # Tester la lecture
                readable = os.access(file_path, os.R_OK)
                # Tester l'écriture
                writable = os.access(file_path, os.W_OK)
                # Tester l'exécution (pour dossiers)
                executable = os.access(file_path, os.X_OK)
                
                print(f"📁 {file_path} :")
                print(f"   • Lecture : {'✓' if readable else '❌'}")
                print(f"   • Écriture : {'✓' if writable else '❌'}")
                print(f"   • Exécution : {'✓' if executable else '❌'}")
                
                if not writable:
                    print(f"   ⚠️ PROBLÈME : Pas de permission d'écriture !")
                
            except Exception as e:
                print(f"❌ Erreur vérification {file_path} : {str(e)}")
        else:
            print(f"📁 {file_path} : N'existe pas")

def main():
    """Point d'entrée principal"""
    print("🔍 SOTRAMINE PHOSPHATE - DIAGNOSTIC PROBLÈME RÉINITIALISATION")
    print("Version 2.1 - Diagnostic Complet")
    print("=" * 70)
    
    # Exécuter tous les diagnostics
    check_database_state()
    check_reset_methods()
    check_file_permissions()
    
    # Demander si on veut tester la réinitialisation manuelle
    print(f"\n" + "=" * 70)
    print("🧪 TEST DE RÉINITIALISATION MANUELLE")
    print("=" * 70)
    
    response = input("Voulez-vous tester une réinitialisation manuelle ? (o/N) : ").lower()
    
    if response in ['o', 'oui', 'y', 'yes']:
        test_manual_reset()
    else:
        print("Test manuel ignoré")
    
    print(f"\n" + "=" * 70)
    print("📋 RÉSUMÉ DU DIAGNOSTIC")
    print("=" * 70)
    
    print("🔍 Diagnostic terminé. Vérifiez les points suivants :")
    print("   1. Les données sont-elles encore présentes dans la base ?")
    print("   2. Les méthodes de réinitialisation sont-elles présentes ?")
    print("   3. Y a-t-il des problèmes de permissions ?")
    print("   4. Le fichier de base de données est-il verrouillé ?")
    
    print(f"\n💡 SOLUTIONS POSSIBLES :")
    print("   • Fermer complètement l'application avant réinitialisation")
    print("   • Vérifier les permissions d'écriture sur le dossier")
    print("   • Utiliser la réinitialisation manuelle si nécessaire")
    print("   • Redémarrer l'application après réinitialisation")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Diagnostic interrompu par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur critique : {str(e)}")
        import traceback
        traceback.print_exc()
