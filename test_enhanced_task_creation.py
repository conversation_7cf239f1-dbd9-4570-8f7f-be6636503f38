#!/usr/bin/env python3
"""
Test des fonctionnalités avancées de création de tâche
- Affectation de personne
- Zone de travail
- Pièces de rechange utilisées
"""

import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_enhanced_task_creation():
    """Test des fonctionnalités avancées de création de tâche"""
    print("🎯 TEST DES FONCTIONNALITÉS AVANCÉES DE CRÉATION DE TÂCHE")
    print("=" * 70)
    
    try:
        # Créer l'application Qt
        app = QApplication(sys.argv)
        app.setApplicationName("SOTRAMINE PHOSPHATE - Test Fonctionnalités Avancées")
        
        from database import Database
        from export.excel_export import ExcelExporter
        from main import OptimizedSotramineApp
        
        # Initialiser les composants
        db = Database()
        excel_exporter = ExcelExporter(db)
        main_app = OptimizedSotramineApp(db, excel_exporter)
        
        print("✓ Application principale créée")
        
        # Vérifier que la méthode existe
        if hasattr(main_app, 'create_new_task'):
            print("✓ Méthode create_new_task disponible")
        else:
            print("❌ Méthode create_new_task manquante")
            return False
        
        # Vérifier les données nécessaires
        print("\n📊 VÉRIFICATION DES DONNÉES")
        print("-" * 40)
        
        # Vérifier les catégories
        db.cursor.execute("SELECT COUNT(*) FROM categories")
        category_count = db.cursor.fetchone()[0]
        print(f"✓ Nombre de catégories disponibles : {category_count}")
        
        # Vérifier le personnel
        db.cursor.execute("SELECT COUNT(*) FROM persons")
        person_count = db.cursor.fetchone()[0]
        print(f"✓ Nombre de personnes disponibles : {person_count}")
        
        # Vérifier les pièces de rechange
        db.cursor.execute("SELECT COUNT(*) FROM spare_parts")
        spare_parts_count = db.cursor.fetchone()[0]
        print(f"✓ Nombre de pièces de rechange disponibles : {spare_parts_count}")
        
        # Ajouter des données de test si nécessaire
        if person_count == 0:
            print("⚠️ Aucune personne trouvée, ajout de personnes de test")
            db.add_person("Jean Dupont", "Technicien")
            db.add_person("Marie Martin", "Superviseur")
            person_count = 2
        
        if spare_parts_count == 0:
            print("⚠️ Aucune pièce de rechange trouvée, ajout de pièces de test")
            db.add_spare_part("P001", "Roulement à billes", 10, "Zone A", "Mécanique", 2, 15.50)
            db.add_spare_part("P002", "Joint d'étanchéité", 5, "Zone B", "Hydraulique", 1, 8.75)
            spare_parts_count = 2
        
        # Test de création de tâche avec toutes les fonctionnalités
        print("\n🧪 TEST DE CRÉATION DE TÂCHE AVANCÉE")
        print("-" * 40)
        
        try:
            # Créer une tâche de test avec toutes les fonctionnalités
            task_id = db.add_task(
                category_id=1,
                title="Tâche de test avancée",
                description="Test des nouvelles fonctionnalités\n\nZone de travail: Atelier mécanique",
                due_date="2025-12-31",
                priority="haute",
                status="À faire",
                assigned_to_ids=[1]  # Assigner à la première personne
            )
            
            print(f"✓ Tâche créée avec ID: {task_id}")
            
            # Vérifier l'affectation
            db.cursor.execute("SELECT p.name, p.role FROM task_assignments ta JOIN persons p ON ta.person_id = p.id WHERE ta.task_id = ?", (task_id,))
            assignments = db.cursor.fetchall()
            if assignments:
                print(f"✓ Affectation vérifiée: {assignments[0][0]} ({assignments[0][1]})")
            else:
                print("⚠️ Aucune affectation trouvée")
            
            # Créer une intervention de maintenance
            intervention_id = db.add_maintenance_intervention(
                task_id=task_id,
                details="Test d'utilisation de pièces de rechange",
                technician_id=1
            )
            print(f"✓ Intervention créée avec ID: {intervention_id}")
            
            # Simuler l'utilisation de pièces de rechange
            if spare_parts_count > 0:
                # Récupérer la première pièce
                db.cursor.execute("SELECT id, name, quantity FROM spare_parts LIMIT 1")
                part = db.cursor.fetchone()
                
                if part:
                    part_id, part_name, current_quantity = part
                    quantity_used = 2
                    
                    # Mettre à jour le stock
                    db.cursor.execute("UPDATE spare_parts SET quantity = quantity - ? WHERE id = ?", (quantity_used, part_id))
                    
                    # Enregistrer le mouvement de stock
                    db.add_stock_movement(
                        part_id=part_id,
                        movement_type='sortie',
                        quantity=quantity_used,
                        reason=f"Test d'utilisation pour tâche: {task_id}",
                        user_id=1
                    )
                    
                    print(f"✓ Utilisation de pièce enregistrée: {part_name} (x{quantity_used})")
                    
                    # Vérifier le nouveau stock
                    db.cursor.execute("SELECT quantity FROM spare_parts WHERE id = ?", (part_id,))
                    new_quantity = db.cursor.fetchone()[0]
                    print(f"✓ Nouveau stock: {new_quantity} (était {current_quantity})")
            
            # Vérifier les mouvements de stock
            db.cursor.execute("SELECT COUNT(*) FROM stock_movements")
            movements_count = db.cursor.fetchone()[0]
            print(f"✓ Mouvements de stock enregistrés: {movements_count}")
            
            # Vérifier les interventions
            db.cursor.execute("SELECT COUNT(*) FROM maintenance_interventions WHERE task_id = ?", (task_id,))
            interventions_count = db.cursor.fetchone()[0]
            print(f"✓ Interventions créées: {interventions_count}")
            
        except Exception as e:
            print(f"❌ Erreur lors du test de création avancée: {str(e)}")
            return False
        
        # Afficher l'application brièvement
        main_app.show()
        print("✓ Application affichée")
        
        # Fermer automatiquement après 2 secondes
        QTimer.singleShot(2000, app.quit)
        app.exec_()
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test : {str(e)}")
        return False

def test_database_integration():
    """Test de l'intégration avec la base de données"""
    print("\n🗄️ TEST D'INTÉGRATION BASE DE DONNÉES")
    print("-" * 50)
    
    try:
        from database import Database
        
        db = Database()
        
        # Test des tables nécessaires
        tables_to_check = [
            'tasks',
            'persons', 
            'spare_parts',
            'task_assignments',
            'maintenance_interventions',
            'stock_movements'
        ]
        
        for table in tables_to_check:
            db.cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            if db.cursor.fetchone():
                print(f"✓ Table {table} existe")
            else:
                print(f"❌ Table {table} manquante")
                return False
        
        # Test des relations
        print("\n🔗 TEST DES RELATIONS")
        print("-" * 30)
        
        # Vérifier que les clés étrangères fonctionnent
        try:
            # Créer une tâche de test
            task_id = db.add_task(
                category_id=1,
                title="Test relation",
                description="Test des relations",
                due_date="2025-12-31",
                priority="moyenne",
                status="À faire"
            )
            
            # Créer une affectation
            if db.cursor.execute("SELECT COUNT(*) FROM persons").fetchone()[0] > 0:
                db.cursor.execute("INSERT INTO task_assignments (task_id, person_id) VALUES (?, ?)", (task_id, 1))
                print("✓ Relation task_assignments fonctionnelle")
            
            # Créer une intervention
            intervention_id = db.add_maintenance_intervention(
                task_id=task_id,
                details="Test intervention",
                technician_id=1
            )
            print("✓ Relation maintenance_interventions fonctionnelle")
            
            # Créer un mouvement de stock
            if db.cursor.execute("SELECT COUNT(*) FROM spare_parts").fetchone()[0] > 0:
                db.add_stock_movement(
                    part_id=1,
                    movement_type='sortie',
                    quantity=1,
                    reason="Test mouvement",
                    user_id=1
                )
                print("✓ Relation stock_movements fonctionnelle")
            
            # Nettoyer les données de test
            db.cursor.execute("DELETE FROM task_assignments WHERE task_id = ?", (task_id,))
            db.cursor.execute("DELETE FROM maintenance_interventions WHERE task_id = ?", (task_id,))
            db.cursor.execute("DELETE FROM tasks WHERE id = ?", (task_id,))
            db.conn.commit()
            
        except Exception as e:
            print(f"❌ Erreur lors du test des relations: {str(e)}")
            return False
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test d'intégration: {str(e)}")
        return False

def main():
    """Fonction principale de test"""
    print("🎯 TEST COMPLET DES FONCTIONNALITÉS AVANCÉES")
    print("=" * 70)
    
    # Tests
    tests = [
        ("Création de tâche avancée", test_enhanced_task_creation),
        ("Intégration base de données", test_database_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Test : {test_name}")
        print("-" * 40)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur lors du test {test_name}: {str(e)}")
            results.append((test_name, False))
    
    # Résumé
    print("\n" + "=" * 70)
    print("📋 RÉSUMÉ DES TESTS DE FONCTIONNALITÉS AVANCÉES")
    print("=" * 70)
    
    success_count = 0
    for test_name, result in results:
        status = "✅ SUCCÈS" if result else "❌ ÉCHEC"
        print(f"{status} - {test_name}")
        if result:
            success_count += 1
    
    print(f"\n📊 RÉSULTATS : {success_count}/{len(results)} tests réussis")
    
    if success_count == len(results):
        print("🎉 TOUTES LES FONCTIONNALITÉS AVANCÉES SONT OPÉRATIONNELLES !")
        print("\n✅ Fonctionnalités validées:")
        print("   • Affectation de personne à une tâche")
        print("   • Zone de travail dans la description")
        print("   • Sélection et utilisation de pièces de rechange")
        print("   • Mise à jour automatique du stock")
        print("   • Enregistrement des mouvements de stock")
        print("   • Création d'interventions de maintenance")
    else:
        print("⚠️ Certaines fonctionnalités nécessitent des corrections")
    
    return success_count == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

