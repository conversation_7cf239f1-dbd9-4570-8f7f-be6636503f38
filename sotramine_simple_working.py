#!/usr/bin/env python3
"""
SOTRAMINE PHOSPHATE - VERSION SIMPLIFIÉE ET FONCTIONNELLE
Application de gestion de maintenance industrielle
Version corrigée sans erreurs d'affichage
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QPushButton, QLabel, QFrame, 
                             QSplitter, QMessageBox, QStackedWidget, QScrollArea,
                             QTableWidget, QTableWidgetItem, QTabWidget,
                             QLineEdit, QComboBox, QTextEdit, QDateEdit)
from PyQt5.QtCore import Qt, QTimer, QDate
from PyQt5.QtGui import QFont, QPalette

class SotramineSimpleApp(QMainWindow):
    """Application SOTRAMINE PHOSPHATE simplifiée et fonctionnelle"""
    
    def __init__(self):
        super().__init__()
        self.current_module = None
        self.setup_database()
        self.setup_ui()
        print("✅ APPLICATION SOTRAMINE PHOSPHATE INITIALISÉE")
    
    def setup_database(self):
        """Initialise la base de données de façon sécurisée"""
        try:
            from database import Database
            self.db = Database()
            print("✅ Base de données connectée")
        except Exception as e:
            print(f"⚠️ Base de données non disponible : {e}")
            self.db = None
    
    def setup_ui(self):
        """Configure l'interface utilisateur simplifiée"""
        self.setWindowTitle("SOTRAMINE PHOSPHATE - Système de Gestion v2.1")
        self.setGeometry(100, 100, 1400, 900)
        self.setMinimumSize(1200, 700)
        
        # Style global simple
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QFrame {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 5px;
            }
        """)
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QHBoxLayout(central_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Menu latéral
        self.sidebar = self.create_sidebar()
        layout.addWidget(self.sidebar)
        
        # Zone de contenu
        self.content_area = self.create_content_area()
        layout.addWidget(self.content_area)
        
        # Proportions
        layout.setStretch(0, 0)  # Sidebar fixe
        layout.setStretch(1, 1)  # Contenu extensible
        
        # Afficher l'accueil
        self.show_home()
    
    def create_sidebar(self):
        """Crée le menu latéral simplifié"""
        sidebar = QFrame()
        sidebar.setFixedWidth(300)
        sidebar.setStyleSheet("""
            QFrame {
                background-color: #2c3e50;
                border: none;
                border-radius: 10px;
            }
            QPushButton {
                background-color: transparent;
                color: white;
                border: none;
                padding: 15px 20px;
                text-align: left;
                font-size: 14px;
                font-weight: bold;
                margin: 2px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #34495e;
            }
            QPushButton:pressed {
                background-color: #3498db;
            }
        """)
        
        layout = QVBoxLayout(sidebar)
        layout.setContentsMargins(15, 20, 15, 20)
        layout.setSpacing(5)
        
        # Titre
        title = QLabel("SOTRAMINE\nPHOSPHATE")
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 20px;
                font-weight: bold;
                text-align: center;
                padding: 20px;
                background-color: #3498db;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Boutons du menu
        self.menu_buttons = {}
        menu_items = [
            ('home', '🏠 Tableau de Bord'),
            ('tasks', '📋 Gestion des Tâches'),
            ('equipment', '🔌 Gestion Équipements'),
            ('personnel', '👥 Gestion Personnel'),
            ('attendance', '📊 Pointage'),
            ('reports', '📄 Rapports'),
            ('settings', '⚙️ Paramètres')
        ]
        
        for item_id, title_text in menu_items:
            btn = QPushButton(title_text)
            btn.clicked.connect(lambda checked, id=item_id: self.navigate_to(id))
            self.menu_buttons[item_id] = btn
            layout.addWidget(btn)
        
        layout.addStretch()
        
        # Version
        version = QLabel("Version 2.1 - Simplifiée")
        version.setStyleSheet("""
            QLabel {
                color: #bdc3c7;
                font-size: 10px;
                text-align: center;
                padding: 10px;
            }
        """)
        version.setAlignment(Qt.AlignCenter)
        layout.addWidget(version)
        
        return sidebar
    
    def create_content_area(self):
        """Crée la zone de contenu"""
        content = QStackedWidget()
        content.setStyleSheet("""
            QStackedWidget {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        # Page d'accueil
        self.home_page = self.create_home_page()
        content.addWidget(self.home_page)
        
        return content
    
    def create_home_page(self):
        """Crée la page d'accueil"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)
        
        # Titre de bienvenue
        welcome = QLabel("Bienvenue dans SOTRAMINE PHOSPHATE")
        welcome.setStyleSheet("""
            QLabel {
                font-size: 28px;
                font-weight: bold;
                color: #2c3e50;
                text-align: center;
                padding: 30px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border-radius: 15px;
                margin-bottom: 20px;
            }
        """)
        welcome.setAlignment(Qt.AlignCenter)
        layout.addWidget(welcome)
        
        # Statistiques
        stats_frame = self.create_stats_frame()
        layout.addWidget(stats_frame)
        
        # Actions rapides
        actions_frame = self.create_actions_frame()
        layout.addWidget(actions_frame)
        
        layout.addStretch()
        
        return page
    
    def create_stats_frame(self):
        """Crée le cadre des statistiques"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        title = QLabel("📊 Statistiques Générales")
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # Grille de statistiques
        stats_layout = QHBoxLayout()
        
        stats = [
            ("📋", "Tâches", "25", "#3498db"),
            ("🔌", "Équipements", "12", "#e67e22"),
            ("👥", "Personnel", "8", "#27ae60"),
            ("📊", "Présences", "95%", "#9b59b6")
        ]
        
        for icon, label, value, color in stats:
            stat_widget = self.create_stat_widget(icon, label, value, color)
            stats_layout.addWidget(stat_widget)
        
        layout.addLayout(stats_layout)
        
        return frame
    
    def create_stat_widget(self, icon, label, value, color):
        """Crée un widget de statistique"""
        widget = QFrame()
        widget.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 2px solid {color};
                border-radius: 10px;
                padding: 20px;
                margin: 5px;
                min-width: 150px;
                min-height: 100px;
            }}
        """)
        
        layout = QVBoxLayout(widget)
        layout.setAlignment(Qt.AlignCenter)
        
        # Icône
        icon_label = QLabel(icon)
        icon_label.setStyleSheet(f"""
            QLabel {{
                font-size: 30px;
                color: {color};
            }}
        """)
        icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_label)
        
        # Valeur
        value_label = QLabel(value)
        value_label.setStyleSheet(f"""
            QLabel {{
                font-size: 24px;
                font-weight: bold;
                color: {color};
            }}
        """)
        value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(value_label)
        
        # Label
        label_widget = QLabel(label)
        label_widget.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #7f8c8d;
                font-weight: bold;
            }
        """)
        label_widget.setAlignment(Qt.AlignCenter)
        layout.addWidget(label_widget)
        
        return widget
    
    def create_actions_frame(self):
        """Crée le cadre des actions rapides"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        title = QLabel("⚡ Actions Rapides")
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # Boutons d'actions
        actions_layout = QHBoxLayout()
        
        actions = [
            ("📋 Nouvelle Tâche", "#3498db", lambda: self.navigate_to('tasks')),
            ("🔌 Nouvel Équipement", "#e67e22", lambda: self.navigate_to('equipment')),
            ("👤 Nouveau Personnel", "#27ae60", lambda: self.navigate_to('personnel')),
            ("📊 Voir Rapports", "#9b59b6", lambda: self.navigate_to('reports'))
        ]
        
        for text, color, action in actions:
            btn = QPushButton(text)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 15px 20px;
                    font-size: 14px;
                    font-weight: bold;
                    min-width: 150px;
                }}
                QPushButton:hover {{
                    background-color: {color}dd;
                }}
            """)
            btn.clicked.connect(action)
            actions_layout.addWidget(btn)
        
        layout.addLayout(actions_layout)
        
        return frame
    
    def navigate_to(self, section_id):
        """Navigation vers une section"""
        try:
            # Réinitialiser les boutons
            for btn in self.menu_buttons.values():
                btn.setStyleSheet(btn.styleSheet().replace("background-color: #3498db;", ""))
            
            # Marquer le bouton actuel
            if section_id in self.menu_buttons:
                current_btn = self.menu_buttons[section_id]
                current_btn.setStyleSheet(current_btn.styleSheet() + "background-color: #3498db;")
            
            # Navigation
            if section_id == 'home':
                self.show_home()
            elif section_id == 'tasks':
                self.show_tasks()
            elif section_id == 'equipment':
                self.show_equipment()
            elif section_id == 'personnel':
                self.show_personnel()
            elif section_id == 'attendance':
                self.show_attendance()
            elif section_id == 'reports':
                self.show_reports()
            elif section_id == 'settings':
                self.show_settings()
            else:
                QMessageBox.information(self, "Module", f"Module '{section_id}' en cours de développement")
                
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur de navigation : {str(e)}")
    
    def show_home(self):
        """Affiche la page d'accueil"""
        self.content_area.setCurrentWidget(self.home_page)
        self.setWindowTitle("SOTRAMINE PHOSPHATE - Tableau de Bord")
    
    def show_tasks(self):
        """Affiche le module des tâches"""
        if not hasattr(self, 'tasks_page'):
            self.tasks_page = self.create_tasks_page()
            self.content_area.addWidget(self.tasks_page)
        
        self.content_area.setCurrentWidget(self.tasks_page)
        self.setWindowTitle("SOTRAMINE PHOSPHATE - Gestion des Tâches")
    
    def show_equipment(self):
        """Affiche le module des équipements"""
        if not hasattr(self, 'equipment_page'):
            self.equipment_page = self.create_equipment_page()
            self.content_area.addWidget(self.equipment_page)
        
        self.content_area.setCurrentWidget(self.equipment_page)
        self.setWindowTitle("SOTRAMINE PHOSPHATE - Gestion des Équipements")
    
    def show_personnel(self):
        """Affiche le module du personnel"""
        if not hasattr(self, 'personnel_page'):
            self.personnel_page = self.create_personnel_page()
            self.content_area.addWidget(self.personnel_page)
        
        self.content_area.setCurrentWidget(self.personnel_page)
        self.setWindowTitle("SOTRAMINE PHOSPHATE - Gestion du Personnel")
    
    def show_attendance(self):
        """Affiche le module de pointage"""
        if not hasattr(self, 'attendance_page'):
            self.attendance_page = self.create_attendance_page()
            self.content_area.addWidget(self.attendance_page)
        
        self.content_area.setCurrentWidget(self.attendance_page)
        self.setWindowTitle("SOTRAMINE PHOSPHATE - Pointage")
    
    def show_reports(self):
        """Affiche le module des rapports"""
        if not hasattr(self, 'reports_page'):
            self.reports_page = self.create_reports_page()
            self.content_area.addWidget(self.reports_page)
        
        self.content_area.setCurrentWidget(self.reports_page)
        self.setWindowTitle("SOTRAMINE PHOSPHATE - Rapports")
    
    def show_settings(self):
        """Affiche les paramètres"""
        if not hasattr(self, 'settings_page'):
            self.settings_page = self.create_settings_page()
            self.content_area.addWidget(self.settings_page)
        
        self.content_area.setCurrentWidget(self.settings_page)
        self.setWindowTitle("SOTRAMINE PHOSPHATE - Paramètres")

    def create_tasks_page(self):
        """Crée la page de gestion des tâches"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(20, 20, 20, 20)

        # Titre
        title = QLabel("📋 Gestion des Tâches")
        title.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 20px;
                padding: 15px;
                background-color: #3498db;
                color: white;
                border-radius: 10px;
            }
        """)
        layout.addWidget(title)

        # Barre d'outils
        toolbar = QHBoxLayout()

        # Bouton nouvelle tâche
        btn_new = QPushButton("➕ Nouvelle Tâche")
        btn_new.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        btn_new.clicked.connect(self.new_task)
        toolbar.addWidget(btn_new)

        # Recherche
        search = QLineEdit()
        search.setPlaceholderText("🔍 Rechercher une tâche...")
        search.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                font-size: 14px;
            }
        """)
        toolbar.addWidget(search)

        # Filtre statut
        status_filter = QComboBox()
        status_filter.addItems(["Tous", "En attente", "En cours", "Terminée", "Annulée"])
        status_filter.setStyleSheet("""
            QComboBox {
                padding: 10px;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                font-size: 14px;
                min-width: 120px;
            }
        """)
        toolbar.addWidget(status_filter)

        layout.addLayout(toolbar)

        # Table des tâches
        self.tasks_table = QTableWidget()
        self.tasks_table.setColumnCount(6)
        self.tasks_table.setHorizontalHeaderLabels([
            "ID", "Titre", "Statut", "Priorité", "Assigné à", "Échéance"
        ])

        self.tasks_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #ddd;
                background-color: white;
                alternate-background-color: #f8f9fa;
                border: 1px solid #ddd;
                border-radius: 5px;
            }
            QTableWidget::item {
                padding: 8px;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

        self.tasks_table.setAlternatingRowColors(True)
        self.tasks_table.horizontalHeader().setStretchLastSection(True)

        # Données d'exemple
        self.load_sample_tasks()

        layout.addWidget(self.tasks_table)

        return page

    def create_equipment_page(self):
        """Crée la page de gestion des équipements"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(20, 20, 20, 20)

        # Titre
        title = QLabel("🔌 Gestion des Équipements")
        title.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: white;
                margin-bottom: 20px;
                padding: 15px;
                background-color: #e67e22;
                border-radius: 10px;
            }
        """)
        layout.addWidget(title)

        # Onglets
        tabs = QTabWidget()
        tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #ddd;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                padding: 10px 20px;
                margin-right: 2px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #e67e22;
                color: white;
            }
        """)

        # Onglet liste des équipements
        equipment_list = self.create_equipment_list()
        tabs.addTab(equipment_list, "📋 Liste des Équipements")

        # Onglet maintenance
        maintenance_tab = self.create_maintenance_tab()
        tabs.addTab(maintenance_tab, "🔧 Maintenance")

        layout.addWidget(tabs)

        return page

    def create_personnel_page(self):
        """Crée la page de gestion du personnel"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(20, 20, 20, 20)

        # Titre
        title = QLabel("👥 Gestion du Personnel")
        title.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: white;
                margin-bottom: 20px;
                padding: 15px;
                background-color: #27ae60;
                border-radius: 10px;
            }
        """)
        layout.addWidget(title)

        # Contenu du personnel
        content = QLabel("Module de gestion du personnel\n\nFonctionnalités :\n• Fiches personnel\n• Compétences\n• Formations\n• Évaluations")
        content.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #2c3e50;
                padding: 30px;
                background-color: #f8f9fa;
                border: 2px solid #27ae60;
                border-radius: 10px;
            }
        """)
        layout.addWidget(content)

        layout.addStretch()

        return page

    def create_attendance_page(self):
        """Crée la page de pointage"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(20, 20, 20, 20)

        # Titre
        title = QLabel("📊 Pointage et Présences")
        title.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: white;
                margin-bottom: 20px;
                padding: 15px;
                background-color: #9b59b6;
                border-radius: 10px;
            }
        """)
        layout.addWidget(title)

        # Contenu du pointage
        content = QLabel("Module de pointage\n\nFonctionnalités :\n• Pointage temps réel\n• Calendrier des présences\n• Rapports mensuels\n• Gestion des absences")
        content.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #2c3e50;
                padding: 30px;
                background-color: #f8f9fa;
                border: 2px solid #9b59b6;
                border-radius: 10px;
            }
        """)
        layout.addWidget(content)

        layout.addStretch()

        return page

    def create_reports_page(self):
        """Crée la page des rapports"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(20, 20, 20, 20)

        # Titre
        title = QLabel("📄 Rapports et Analyses")
        title.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: white;
                margin-bottom: 20px;
                padding: 15px;
                background-color: #8e44ad;
                border-radius: 10px;
            }
        """)
        layout.addWidget(title)

        # Contenu des rapports
        content = QLabel("Module de rapports\n\nFonctionnalités :\n• Rapports de production\n• Analyses de maintenance\n• Statistiques personnel\n• Export Excel/PDF")
        content.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #2c3e50;
                padding: 30px;
                background-color: #f8f9fa;
                border: 2px solid #8e44ad;
                border-radius: 10px;
            }
        """)
        layout.addWidget(content)

        layout.addStretch()

        return page

    def create_settings_page(self):
        """Crée la page des paramètres"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(20, 20, 20, 20)

        # Titre
        title = QLabel("⚙️ Paramètres")
        title.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: white;
                margin-bottom: 20px;
                padding: 15px;
                background-color: #34495e;
                border-radius: 10px;
            }
        """)
        layout.addWidget(title)

        # Contenu des paramètres
        content = QLabel("Configuration système\n\nOptions :\n• Paramètres généraux\n• Base de données\n• Utilisateurs\n• Sauvegarde")
        content.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #2c3e50;
                padding: 30px;
                background-color: #f8f9fa;
                border: 2px solid #34495e;
                border-radius: 10px;
            }
        """)
        layout.addWidget(content)

        layout.addStretch()

        return page

    def create_equipment_list(self):
        """Crée la liste des équipements"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Barre d'outils
        toolbar = QHBoxLayout()

        btn_new = QPushButton("➕ Nouvel Équipement")
        btn_new.setStyleSheet("""
            QPushButton {
                background-color: #e67e22;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #f39c12;
            }
        """)
        toolbar.addWidget(btn_new)

        toolbar.addStretch()
        layout.addLayout(toolbar)

        # Table des équipements
        equipment_table = QTableWidget()
        equipment_table.setColumnCount(5)
        equipment_table.setHorizontalHeaderLabels([
            "ID", "Nom", "Type", "Statut", "Dernière Maintenance"
        ])

        equipment_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #ddd;
                background-color: white;
                alternate-background-color: #f8f9fa;
                border: 1px solid #ddd;
                border-radius: 5px;
            }
            QHeaderView::section {
                background-color: #e67e22;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

        equipment_table.setAlternatingRowColors(True)
        equipment_table.horizontalHeader().setStretchLastSection(True)

        # Données d'exemple
        self.load_sample_equipment(equipment_table)

        layout.addWidget(equipment_table)

        return widget

    def create_maintenance_tab(self):
        """Crée l'onglet maintenance"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        content = QLabel("Planification de la maintenance\n\nFonctionnalités :\n• Maintenance préventive\n• Interventions\n• Historique\n• Pièces de rechange")
        content.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #2c3e50;
                padding: 30px;
                background-color: #f8f9fa;
                border: 2px solid #e67e22;
                border-radius: 10px;
            }
        """)
        layout.addWidget(content)

        layout.addStretch()

        return widget

    def load_sample_tasks(self):
        """Charge des tâches d'exemple"""
        tasks_data = [
            ("001", "Maintenance pompe A1", "En cours", "Haute", "Jean Dupont", "2025-08-15"),
            ("002", "Inspection ligne B", "En attente", "Normale", "Marie Martin", "2025-08-20"),
            ("003", "Réparation moteur C3", "Terminée", "Critique", "Pierre Durand", "2025-08-10"),
            ("004", "Nettoyage réservoir D", "En attente", "Faible", "Sophie Bernard", "2025-08-25"),
            ("005", "Calibrage instruments", "En cours", "Normale", "Luc Moreau", "2025-08-18")
        ]

        self.tasks_table.setRowCount(len(tasks_data))

        for row, task in enumerate(tasks_data):
            for col, value in enumerate(task):
                item = QTableWidgetItem(str(value))

                # Colorer selon le statut
                if col == 2:  # Colonne statut
                    if value == "Terminée":
                        item.setBackground(Qt.green)
                    elif value == "En cours":
                        item.setBackground(Qt.yellow)
                    elif value == "En attente":
                        item.setBackground(Qt.lightGray)

                self.tasks_table.setItem(row, col, item)

        self.tasks_table.resizeColumnsToContents()

    def load_sample_equipment(self, table):
        """Charge des équipements d'exemple"""
        equipment_data = [
            ("EQ001", "Pompe centrifuge A1", "Pompe", "Opérationnel", "2025-07-15"),
            ("EQ002", "Moteur électrique B2", "Moteur", "Maintenance", "2025-08-01"),
            ("EQ003", "Compresseur C3", "Compresseur", "Opérationnel", "2025-06-20"),
            ("EQ004", "Réservoir D4", "Réservoir", "Opérationnel", "2025-05-10"),
            ("EQ005", "Convoyeur E5", "Convoyeur", "Arrêt", "2025-08-05")
        ]

        table.setRowCount(len(equipment_data))

        for row, equipment in enumerate(equipment_data):
            for col, value in enumerate(equipment):
                item = QTableWidgetItem(str(value))

                # Colorer selon le statut
                if col == 3:  # Colonne statut
                    if value == "Opérationnel":
                        item.setBackground(Qt.green)
                    elif value == "Maintenance":
                        item.setBackground(Qt.yellow)
                    elif value == "Arrêt":
                        item.setBackground(Qt.red)

                table.setItem(row, col, item)

        table.resizeColumnsToContents()

    def new_task(self):
        """Crée une nouvelle tâche"""
        QMessageBox.information(self, "Nouvelle Tâche",
                               "📋 Création d'une nouvelle tâche\n\n"
                               "Fonctionnalité en cours de développement.\n"
                               "Cette fenêtre permettra de créer une nouvelle tâche "
                               "avec tous les détails nécessaires.")

def main():
    """Point d'entrée principal"""
    print("🚀 LANCEMENT SOTRAMINE PHOSPHATE - VERSION SIMPLIFIÉE")
    print("=" * 60)

    # Créer l'application Qt
    app = QApplication(sys.argv)

    # Style simple et propre
    app.setStyle('Fusion')

    print("✅ Application Qt créée")

    # Créer la fenêtre principale
    window = SotramineSimpleApp()
    print("✅ Interface utilisateur créée")

    # Afficher la fenêtre
    window.show()
    print("✅ Application affichée")

    print("\n🎉 APPLICATION SIMPLIFIÉE LANCÉE AVEC SUCCÈS !")
    print("📋 MODULES DISPONIBLES :")
    print("   🏠 Tableau de bord avec statistiques")
    print("   📋 Gestion des tâches avec table fonctionnelle")
    print("   🔌 Gestion des équipements avec onglets")
    print("   👥 Gestion du personnel")
    print("   📊 Pointage et présences")
    print("   📄 Rapports et analyses")
    print("   ⚙️ Paramètres système")

    print("\n✨ INTERFACE SIMPLIFIÉE ET FONCTIONNELLE")
    print("🎯 SANS ERREURS D'AFFICHAGE")
    print("🔧 NAVIGATION FLUIDE")

    # Lancer la boucle d'événements
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
