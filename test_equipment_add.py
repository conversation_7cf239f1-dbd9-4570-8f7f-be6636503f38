#!/usr/bin/env python3
"""
Test de l'ajout d'équipement après correction de l'erreur QDialog
"""

import sys
import os

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_equipment_dialog_import():
    """Test de l'import du dialog d'équipement"""
    print("🔧 TEST IMPORT DIALOG ÉQUIPEMENT")
    print("-" * 40)
    
    try:
        from PyQt5.QtWidgets import QApplication, QDialog
        from gui.equipment_manager import EquipmentDialog
        from database import Database
        
        print("✓ Imports réussis")
        
        # Créer une application Qt
        app = QApplication.instance()
        if not app:
            app = QApplication(sys.argv)
        
        # Créer une base de données
        db = Database()
        print("✓ Base de données créée")
        
        # Créer le dialog
        dialog = EquipmentDialog(None, db)
        print("✓ Dialog d'équipement créé")
        
        # Vérifier les méthodes
        methods_to_check = [
            'get_equipment_data',
            'setup_ui',
            'exec_'
        ]
        
        for method in methods_to_check:
            if hasattr(dialog, method):
                print(f"✓ Méthode {method} présente")
            else:
                print(f"❌ Méthode {method} manquante")
                return False
        
        # Vérifier les constantes QDialog
        if hasattr(QDialog, 'Accepted'):
            print("✓ QDialog.Accepted disponible")
        else:
            print("❌ QDialog.Accepted manquant")
            return False
        
        if hasattr(QDialog, 'Rejected'):
            print("✓ QDialog.Rejected disponible")
        else:
            print("❌ QDialog.Rejected manquant")
            return False
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur : {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_main_create_equipment():
    """Test de la méthode create_new_equipment dans main.py"""
    print("\n📋 TEST MÉTHODE CREATE_NEW_EQUIPMENT")
    print("-" * 40)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from database import Database
        import main
        
        print("✓ Imports réussis")
        
        # Créer une application Qt
        app = QApplication.instance()
        if not app:
            app = QApplication(sys.argv)
        
        # Créer une base de données
        db = Database()
        
        # Créer l'exporteur Excel
        from export.excel_export import ExcelExporter
        excel_exporter = ExcelExporter(db)
        
        # Créer l'application principale
        main_app = main.OptimizedSotramineApp(db, excel_exporter)
        print("✓ Application principale créée")
        
        # Vérifier que la méthode existe
        if hasattr(main_app, 'create_new_equipment'):
            print("✓ Méthode create_new_equipment présente")
        else:
            print("❌ Méthode create_new_equipment manquante")
            return False
        
        # Vérifier les imports dans le code de la méthode
        import inspect
        source = inspect.getsource(main_app.create_new_equipment)
        
        if 'from PyQt5.QtWidgets import QDialog' in source:
            print("✓ Import QDialog présent dans create_new_equipment")
        else:
            print("❌ Import QDialog manquant dans create_new_equipment")
            return False
        
        if 'from gui.equipment_manager import EquipmentDialog' in source:
            print("✓ Import EquipmentDialog présent")
        else:
            print("❌ Import EquipmentDialog manquant")
            return False
        
        if 'QDialog.Accepted' in source:
            print("✓ Référence QDialog.Accepted présente")
        else:
            print("❌ Référence QDialog.Accepted manquante")
            return False
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur : {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_database_add_equipment():
    """Test de l'ajout d'équipement dans la base"""
    print("\n🗄️ TEST AJOUT ÉQUIPEMENT BASE DE DONNÉES")
    print("-" * 40)
    
    try:
        from database import Database
        
        db = Database()
        print("✓ Base de données créée")
        
        # Compter les équipements avant
        equipment_before = db.get_all_equipment()
        count_before = len(equipment_before)
        print(f"✓ Équipements avant : {count_before}")
        
        # Ajouter un équipement de test
        test_equipment = {
            'name': 'Équipement Test Correction',
            'code': 'TEST-CORR-001',
            'model': 'Test Model',
            'manufacturer': 'Test Manufacturer',
            'location': 'Test Location',
            'status': 'En service',
            'notes': 'Équipement de test après correction QDialog'
        }
        
        equipment_id = db.add_equipment(**test_equipment)
        print(f"✓ Équipement ajouté avec ID : {equipment_id}")
        
        # Compter après
        equipment_after = db.get_all_equipment()
        count_after = len(equipment_after)
        print(f"✓ Équipements après : {count_after}")
        
        # Vérifier l'ajout
        if count_after == count_before + 1:
            print("✅ Ajout d'équipement réussi")
            
            # Vérifier les données
            added_equipment = db.get_equipment(equipment_id)
            if added_equipment and added_equipment[2] == test_equipment['name']:
                print("✓ Données correctement enregistrées")
                return True
            else:
                print("❌ Données incorrectes")
                return False
        else:
            print("❌ Ajout d'équipement échoué")
            return False
        
        db.close()
        
    except Exception as e:
        print(f"❌ Erreur : {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Point d'entrée principal"""
    print("🔧 SOTRAMINE PHOSPHATE - TEST CORRECTION AJOUT ÉQUIPEMENT")
    print("Version 2.1 - Correction Erreur QDialog")
    print("=" * 65)
    
    tests = [
        ("Import dialog équipement", test_equipment_dialog_import),
        ("Méthode create_new_equipment", test_main_create_equipment),
        ("Ajout équipement base de données", test_database_add_equipment)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\n🎯 {test_name.upper()}")
            success = test_func()
            results.append((test_name, success))
            
            if success:
                print(f"✅ {test_name} - SUCCÈS")
            else:
                print(f"❌ {test_name} - ÉCHEC")
                
        except Exception as e:
            print(f"❌ Erreur critique dans {test_name} : {str(e)}")
            results.append((test_name, False))
    
    # Résumé final
    print("\n" + "=" * 65)
    print("📋 RÉSUMÉ DES TESTS CORRECTION")
    print("=" * 65)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ SUCCÈS" if success else "❌ ÉCHEC"
        print(f"{status:<12} {test_name}")
    
    print(f"\n📊 RÉSULTATS FINAUX :")
    print(f"✅ Tests réussis : {passed}/{total}")
    print(f"📈 Taux de réussite : {(passed/total*100):.1f}%")
    
    if passed == total:
        print("\n🎉 CORRECTION RÉUSSIE !")
        print("✅ L'erreur QDialog a été corrigée")
        print("✅ L'ajout d'équipement fonctionne maintenant")
        
        print("\n📝 CORRECTIONS APPORTÉES :")
        print("   • Import QDialog ajouté dans create_new_equipment")
        print("   • Toutes les références QDialog.Accepted fonctionnelles")
        print("   • Dialog d'équipement opérationnel")
        print("   • Base de données fonctionnelle")
        
        print("\n🚀 VOUS POUVEZ MAINTENANT :")
        print("   • Ajouter des équipements via l'interface")
        print("   • Utiliser tous les dialogs sans erreur")
        print("   • Profiter de l'application complètement fonctionnelle")
    else:
        print(f"\n⚠️ {total - passed} test(s) ont échoué")
        print("🔧 Des corrections supplémentaires peuvent être nécessaires")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        print(f"\n👋 Test correction terminé - {'Succès' if success else 'Échec'}")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 Test interrompu par l'utilisateur")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Erreur critique : {str(e)}")
        sys.exit(1)
