"""
Gestionnaire principal de maintenance professionnel
Système complet de gestion de maintenance industrielle
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, 
                             QLabel, QPushButton, QFrame, QSplitter, QScrollArea,
                             QGridLayout, QGroupBox, QTableWidget, QTableWidgetItem,
                             QHeaderView, QMessageBox, QDialog, QComboBox, QLineEdit)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QDate
from PyQt5.QtGui import QFont, QPixmap, QIcon
from utils.data_refresh_manager import RefreshableWidget, notify_data_changed

class MaintenanceManager(QWidget, RefreshableWidget):
    """Gestionnaire principal de maintenance avec toutes les fonctionnalités"""
    
    # Signaux pour communication avec l'application principale
    section_requested = pyqtSignal(str)
    equipment_selected = pyqtSignal(int)
    intervention_created = pyqtSignal(int)
    
    def __init__(self, db, parent=None):
        super().__init__(parent)
        RefreshableWidget.__init__(self)
        self.db = db
        self.current_equipment = None
        self.current_intervention = None
        
        self.setup_ui()
        self.load_data()
        self.enable_auto_refresh(['equipment', 'maintenance'])
        
        # Timer pour actualisation automatique
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_data)
        self.refresh_timer.start(30000)  # Actualisation toutes les 30 secondes
    
    def setup_ui(self):
        """Configure l'interface utilisateur principale"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # En-tête de la section maintenance
        header = self.create_maintenance_header()
        layout.addWidget(header)
        
        # Tableau de bord maintenance
        dashboard = self.create_maintenance_dashboard()
        layout.addWidget(dashboard)
        
        # Onglets principaux de maintenance
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dcdde1;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f8f9fa;
                border: 1px solid #dcdde1;
                padding: 12px 20px;
                margin-right: 2px;
                font-weight: bold;
                min-width: 120px;
            }
            QTabBar::tab:selected {
                background-color: #f39c12;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #e67e22;
                color: white;
            }
        """)
        
        # Onglet Équipements
        self.equipment_tab = self.create_equipment_tab()
        self.tab_widget.addTab(self.equipment_tab, "🔌 Équipements")
        
        # Onglet Interventions
        self.interventions_tab = self.create_interventions_tab()
        self.tab_widget.addTab(self.interventions_tab, "🔧 Interventions")
        
        # Onglet Planification Préventive
        self.preventive_tab = self.create_preventive_tab()
        self.tab_widget.addTab(self.preventive_tab, "📅 Planification")
        
        # Onglet Pièces de Rechange
        self.spare_parts_tab = self.create_spare_parts_tab()
        self.tab_widget.addTab(self.spare_parts_tab, "🔧 Pièces")
        
        # Onglet Techniciens
        self.technicians_tab = self.create_technicians_tab()
        self.tab_widget.addTab(self.technicians_tab, "👷 Techniciens")
        
        # Onglet Historique & Indicateurs
        self.history_tab = self.create_history_tab()
        self.tab_widget.addTab(self.history_tab, "📊 Historique & KPI")
        
        layout.addWidget(self.tab_widget)
    
    def create_maintenance_header(self):
        """Crée l'en-tête de la section maintenance"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f39c12, stop:1 #e67e22);
                border-radius: 0;
                padding: 20px;
                margin: 0;
            }
        """)
        header_frame.setFixedHeight(80)
        
        layout = QHBoxLayout(header_frame)
        layout.setContentsMargins(20, 15, 20, 15)
        
        # Titre et icône
        title_layout = QHBoxLayout()
        
        icon_label = QLabel("🔧")
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 32px;
                color: white;
                margin-right: 15px;
            }
        """)
        title_layout.addWidget(icon_label)
        
        title_text_layout = QVBoxLayout()
        title_text_layout.setSpacing(2)
        
        title_label = QLabel("MAINTENANCE INDUSTRIELLE")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: white;
                margin: 0;
            }
        """)
        title_text_layout.addWidget(title_label)
        
        subtitle_label = QLabel("Gestion complète du parc technique")
        subtitle_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: rgba(255, 255, 255, 0.9);
                margin: 0;
            }
        """)
        title_text_layout.addWidget(subtitle_label)
        
        title_layout.addLayout(title_text_layout)
        layout.addLayout(title_layout)
        
        layout.addStretch()
        
        # Boutons d'actions rapides
        actions_layout = QHBoxLayout()
        actions_layout.setSpacing(10)
        
        quick_actions = [
            ("🚨 Intervention Urgente", self.create_urgent_intervention, "#e74c3c"),
            ("📋 Nouveau Bon de Travail", self.create_work_order, "#3498db"),
            ("📅 Planifier Maintenance", self.schedule_maintenance, "#27ae60")
        ]
        
        for text, action, color in quick_actions:
            btn = QPushButton(text)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 10px 16px;
                    font-weight: bold;
                    font-size: 12px;
                    min-width: 140px;
                }}
                QPushButton:hover {{
                    background-color: {color}dd;
                    transform: translateY(-1px);
                }}
                QPushButton:pressed {{
                    background-color: {color}bb;
                }}
            """)
            btn.clicked.connect(action)
            actions_layout.addWidget(btn)
        
        layout.addLayout(actions_layout)
        
        return header_frame
    
    def create_maintenance_dashboard(self):
        """Crée le tableau de bord de maintenance"""
        dashboard_frame = QFrame()
        dashboard_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-bottom: 1px solid #dee2e6;
                padding: 15px;
            }
        """)
        dashboard_frame.setFixedHeight(120)
        
        layout = QHBoxLayout(dashboard_frame)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(30)
        
        # Indicateurs clés
        self.kpi_widgets = {}
        kpis = [
            ("equipment_total", "🔌", "Équipements", "0", "#3498db"),
            ("interventions_pending", "⏳", "En Attente", "0", "#f39c12"),
            ("interventions_urgent", "🚨", "Urgentes", "0", "#e74c3c"),
            ("preventive_due", "📅", "Préventives Dues", "0", "#9b59b6"),
            ("technicians_active", "👷", "Techniciens", "0", "#27ae60"),
            ("spare_parts_low", "⚠️", "Stock Faible", "0", "#e67e22")
        ]
        
        for kpi_id, icon, label, value, color in kpis:
            kpi_widget = self.create_kpi_widget(icon, label, value, color)
            self.kpi_widgets[kpi_id] = kpi_widget
            layout.addWidget(kpi_widget)
        
        layout.addStretch()
        
        return dashboard_frame
    
    def create_kpi_widget(self, icon, label, value, color):
        """Crée un widget d'indicateur KPI"""
        widget = QFrame()
        widget.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 2px solid {color};
                border-radius: 8px;
                padding: 10px;
                min-width: 100px;
            }}
            QFrame:hover {{
                background-color: {color}10;
            }}
        """)
        
        layout = QVBoxLayout(widget)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(5)
        
        # Icône et valeur
        icon_value = QLabel(f"{icon} {value}")
        icon_value.setStyleSheet(f"""
            QLabel {{
                font-size: 20px;
                font-weight: bold;
                color: {color};
            }}
        """)
        icon_value.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_value)
        
        # Label
        label_widget = QLabel(label)
        label_widget.setStyleSheet("""
            QLabel {
                font-size: 11px;
                color: #6c757d;
                font-weight: bold;
            }
        """)
        label_widget.setAlignment(Qt.AlignCenter)
        layout.addWidget(label_widget)
        
        # Stocker la référence pour mise à jour
        widget.icon_value_label = icon_value
        widget.icon = icon
        widget.color = color
        
        return widget
    
    def create_equipment_tab(self):
        """Crée l'onglet de gestion des équipements"""
        from gui.equipment_management_pro import EquipmentManagementPro
        return EquipmentManagementPro(self.db, self)
    
    def create_interventions_tab(self):
        """Crée l'onglet de gestion des interventions"""
        from gui.interventions_management import InterventionsManagement
        return InterventionsManagement(self.db, self)
    
    def create_preventive_tab(self):
        """Crée l'onglet de planification préventive"""
        from gui.preventive_maintenance import PreventiveMaintenance
        return PreventiveMaintenance(self.db, self)
    
    def create_spare_parts_tab(self):
        """Crée l'onglet de gestion des pièces de rechange"""
        from gui.spare_parts_management_pro import SparePartsManagementPro
        return SparePartsManagementPro(self.db, self)
    
    def create_technicians_tab(self):
        """Crée l'onglet de gestion des techniciens"""
        from gui.technicians_management import TechniciansManagement
        return TechniciansManagement(self.db, self)
    
    def create_history_tab(self):
        """Crée l'onglet d'historique et indicateurs"""
        from gui.maintenance_history_pro import MaintenanceHistoryPro
        return MaintenanceHistoryPro(self.db, self)
    
    def load_data(self):
        """Charge toutes les données de maintenance"""
        self.update_kpis()
    
    def update_kpis(self):
        """Met à jour les indicateurs KPI"""
        try:
            # Compter les équipements
            equipment_count = len(self.db.get_all_equipment())
            self.update_kpi_widget("equipment_total", equipment_count)
            
            # Compter les interventions en attente
            pending_interventions = self.get_pending_interventions_count()
            self.update_kpi_widget("interventions_pending", pending_interventions)
            
            # Compter les interventions urgentes
            urgent_interventions = self.get_urgent_interventions_count()
            self.update_kpi_widget("interventions_urgent", urgent_interventions)
            
            # Compter les maintenances préventives dues
            preventive_due = self.get_preventive_due_count()
            self.update_kpi_widget("preventive_due", preventive_due)
            
            # Compter les techniciens actifs
            active_technicians = self.get_active_technicians_count()
            self.update_kpi_widget("technicians_active", active_technicians)
            
            # Compter les pièces en stock faible
            low_stock_parts = self.get_low_stock_parts_count()
            self.update_kpi_widget("spare_parts_low", low_stock_parts)
            
        except Exception as e:
            print(f"Erreur mise à jour KPIs maintenance : {str(e)}")
    
    def update_kpi_widget(self, kpi_id, value):
        """Met à jour un widget KPI"""
        if kpi_id in self.kpi_widgets:
            widget = self.kpi_widgets[kpi_id]
            widget.icon_value_label.setText(f"{widget.icon} {value}")
    
    def get_pending_interventions_count(self):
        """Retourne le nombre d'interventions en attente"""
        try:
            cursor = self.db.cursor
            cursor.execute("""
                SELECT COUNT(*) FROM maintenance_interventions 
                WHERE status = 'En attente' OR status = 'Planifiée'
            """)
            return cursor.fetchone()[0] or 0
        except:
            return 0
    
    def get_urgent_interventions_count(self):
        """Retourne le nombre d'interventions urgentes"""
        try:
            cursor = self.db.cursor
            cursor.execute("""
                SELECT COUNT(*) FROM maintenance_interventions 
                WHERE priority = 'Urgente' AND status != 'Terminée'
            """)
            return cursor.fetchone()[0] or 0
        except:
            return 0
    
    def get_preventive_due_count(self):
        """Retourne le nombre de maintenances préventives dues"""
        try:
            cursor = self.db.cursor
            today = QDate.currentDate().toString("yyyy-MM-dd")
            cursor.execute("""
                SELECT COUNT(*) FROM equipment 
                WHERE next_maintenance_date <= ? AND next_maintenance_date IS NOT NULL
            """, (today,))
            return cursor.fetchone()[0] or 0
        except:
            return 0
    
    def get_active_technicians_count(self):
        """Retourne le nombre de techniciens actifs"""
        try:
            cursor = self.db.cursor
            cursor.execute("""
                SELECT COUNT(*) FROM technicians 
                WHERE status = 'Actif'
            """)
            return cursor.fetchone()[0] or 0
        except:
            return 0
    
    def get_low_stock_parts_count(self):
        """Retourne le nombre de pièces en stock faible"""
        try:
            cursor = self.db.cursor
            cursor.execute("""
                SELECT COUNT(*) FROM spare_parts 
                WHERE quantity <= alert_threshold AND alert_threshold > 0
            """)
            return cursor.fetchone()[0] or 0
        except:
            return 0
    
    def create_urgent_intervention(self):
        """Crée une intervention urgente"""
        from gui.intervention_dialog import InterventionDialog
        dialog = InterventionDialog(self.db, self, priority="Urgente")
        if dialog.exec_() == QDialog.Accepted:
            self.refresh_data()
            notify_data_changed('maintenance')
    
    def create_work_order(self):
        """Crée un nouveau bon de travail"""
        from gui.work_order_dialog import WorkOrderDialog
        dialog = WorkOrderDialog(self.db, self)
        if dialog.exec_() == QDialog.Accepted:
            self.refresh_data()
            notify_data_changed('maintenance')
    
    def schedule_maintenance(self):
        """Planifie une maintenance préventive"""
        from gui.schedule_maintenance_dialog import ScheduleMaintenanceDialog
        dialog = ScheduleMaintenanceDialog(self.db, self)
        if dialog.exec_() == QDialog.Accepted:
            self.refresh_data()
            notify_data_changed('maintenance')
    
    def refresh_data(self):
        """Actualise toutes les données (pour le système d'actualisation automatique)"""
        self.update_kpis()
        
        # Actualiser tous les onglets
        for i in range(self.tab_widget.count()):
            widget = self.tab_widget.widget(i)
            if hasattr(widget, 'refresh_data'):
                widget.refresh_data()
