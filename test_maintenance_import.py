#!/usr/bin/env python3
"""
Test d'import du menu de maintenance
"""

import sys
import traceback

print("🔍 Test d'import du menu de maintenance")
print("=" * 50)

try:
    print("📦 Test 1: Import de PyQt5...")
    from PyQt5.QtWidgets import QWidget
    print("✅ PyQt5 importé")
except Exception as e:
    print(f"❌ Erreur PyQt5: {e}")
    traceback.print_exc()
    sys.exit(1)

try:
    print("📦 Test 2: Import de database...")
    from database import Database
    print("✅ Database importé")
except Exception as e:
    print(f"❌ Erreur database: {e}")
    traceback.print_exc()
    sys.exit(1)

try:
    print("📦 Test 3: Import de gui.maintenance_menu...")
    from gui.maintenance_menu import MaintenanceMenu
    print("✅ MaintenanceMenu importé")
except Exception as e:
    print(f"❌ Erreur maintenance_menu: {e}")
    traceback.print_exc()
    sys.exit(1)

print("\n🎉 Tous les imports sont réussis !")
print("Le menu de maintenance est prêt à être utilisé.")
