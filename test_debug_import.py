#!/usr/bin/env python3
"""
Test de débogage des imports
"""

import sys
import os
import traceback

# Ajouter le répertoire parent au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("🔍 Test de débogage des imports")
print("=" * 40)

try:
    print("📦 Test 1: Import de PyQt5...")
    from PyQt5.QtWidgets import QWidget
    print("✅ PyQt5 importé")
except Exception as e:
    print(f"❌ Erreur PyQt5: {e}")
    traceback.print_exc()
    sys.exit(1)

try:
    print("📦 Test 2: Import de database...")
    from database import Database
    print("✅ Database importé")
except Exception as e:
    print(f"❌ Erreur database: {e}")
    traceback.print_exc()
    sys.exit(1)

try:
    print("📦 Test 3: Import de gui...")
    import gui
    print("✅ gui importé")
except Exception as e:
    print(f"❌ Erreur gui: {e}")
    traceback.print_exc()
    sys.exit(1)

try:
    print("📦 Test 4: Import de interventions_management...")
    from gui.interventions_management import InterventionsManagement
    print("✅ interventions_management importé")
except Exception as e:
    print(f"❌ Erreur interventions_management: {e}")
    traceback.print_exc()
    sys.exit(1)

try:
    print("📦 Test 5: Import de preventive_maintenance...")
    from gui.preventive_maintenance import PreventiveMaintenance
    print("✅ preventive_maintenance importé")
except Exception as e:
    print(f"❌ Erreur preventive_maintenance: {e}")
    traceback.print_exc()
    sys.exit(1)

try:
    print("📦 Test 6: Import de work_orders_manager...")
    from gui.work_orders_manager import WorkOrdersManager
    print("✅ work_orders_manager importé")
except Exception as e:
    print(f"❌ Erreur work_orders_manager: {e}")
    traceback.print_exc()
    sys.exit(1)

try:
    print("📦 Test 7: Import de maintenance_indicators...")
    from gui.maintenance_indicators import MaintenanceIndicators
    print("✅ maintenance_indicators importé")
except Exception as e:
    print(f"❌ Erreur maintenance_indicators: {e}")
    traceback.print_exc()
    sys.exit(1)

try:
    print("📦 Test 8: Import de maintenance_menu...")
    from gui.maintenance_menu import MaintenanceMenu
    print("✅ maintenance_menu importé")
except Exception as e:
    print(f"❌ Erreur maintenance_menu: {e}")
    traceback.print_exc()
    sys.exit(1)

print("\n🎉 Tous les imports sont réussis !")
