"""
Menu Maintenance Principal
Point d'entrée unifié pour toutes les fonctionnalités de maintenance
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
                             QLabel, QFrame, QSplitter, QGroupBox, QPushButton,
                             QTableWidget, QTableWidgetItem, QHeaderView, QProgressBar,
                             QComboBox, QDateEdit, QLineEdit, QTextEdit, QSpinBox,
                             QMessageBox, QDialog, QFormLayout, QGridLayout)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QDate, QDateTime
from PyQt5.QtGui import QFont, QColor, QPixmap, QIcon

from gui.interventions_management import InterventionsManagement
from gui.preventive_maintenance import PreventiveMaintenance
from gui.maintenance_history import MaintenanceHistoryView
from gui.maintenance_checklist import MaintenanceChecklistView
from gui.work_orders_manager import WorkOrdersManager
from gui.maintenance_indicators import MaintenanceIndicators

class MaintenanceMenu(QWidget):
    """Menu principal de maintenance intégrant toutes les fonctionnalités"""
    
    # Signaux pour la communication avec la fenêtre principale
    maintenance_updated = pyqtSignal()
    intervention_created = pyqtSignal(int)
    work_order_created = pyqtSignal(int)
    
    def __init__(self, db, parent=None):
        super().__init__(parent)
        self.db = db
        self.setup_ui()
        self.load_dashboard_data()
        
        # Timer pour actualisation automatique
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_dashboard)
        self.refresh_timer.start(300000)  # 5 minutes
    
    def setup_ui(self):
        """Configure l'interface utilisateur principale"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # En-tête du menu maintenance
        header = self.create_header()
        layout.addWidget(header)
        
        # Tableau de bord rapide
        dashboard = self.create_dashboard()
        layout.addWidget(dashboard)
        
        # Onglets principaux
        self.main_tabs = QTabWidget()
        self.main_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dcdde1;
                background-color: white;
                border-radius: 8px;
            }
            QTabBar::tab {
                background-color: #f8f9fa;
                border: 1px solid #dcdde1;
                padding: 12px 20px;
                margin-right: 2px;
                font-weight: bold;
                min-width: 180px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
                border-bottom: 2px solid #2980b9;
            }
            QTabBar::tab:hover {
                background-color: #e3f2fd;
                color: #1976d2;
            }
        """)
        
        # Onglet Interventions et Bons de Travail
        self.interventions_tab = InterventionsManagement(self.db, self)
        self.main_tabs.addTab(self.interventions_tab, "🔧 Interventions & Bons de Travail")
        
        # Onglet Planification Préventive
        self.preventive_tab = PreventiveMaintenance(self.db, self)
        self.main_tabs.addTab(self.preventive_tab, "📅 Planification Préventive")
        
        # Onglet Bons de Travail (dédié)
        self.work_orders_tab = WorkOrdersManager(self.db, self)
        self.main_tabs.addTab(self.work_orders_tab, "📋 Bons de Travail")
        
        # Onglet Historique et Indicateurs
        self.history_tab = MaintenanceHistoryView(self, self.db)
        self.main_tabs.addTab(self.history_tab, "📊 Historique & Indicateurs")
        
        # Onglet Indicateurs Avancés
        self.indicators_tab = MaintenanceIndicators(self.db, self)
        self.main_tabs.addTab(self.indicators_tab, "📈 Indicateurs Avancés")
        
        # Onglet Checklists de Maintenance
        self.checklist_tab = MaintenanceChecklistView(self.db, self)
        self.main_tabs.addTab(self.checklist_tab, "✅ Checklists de Maintenance")
        
        layout.addWidget(self.main_tabs)
        
        # Connecter les signaux
        self.interventions_tab.intervention_created.connect(self.on_intervention_created)
        self.interventions_tab.work_order_created.connect(self.on_work_order_created)
        self.preventive_tab.maintenance_scheduled.connect(self.on_maintenance_scheduled)
        self.work_orders_tab.work_order_created.connect(self.on_work_order_created)
        self.work_orders_tab.work_order_updated.connect(self.on_work_order_updated)
        self.work_orders_tab.work_order_deleted.connect(self.on_work_order_deleted)
    
    def create_header(self):
        """Crée l'en-tête du menu maintenance"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 12px;
                padding: 20px;
            }
        """)
        
        header_layout = QHBoxLayout(header_frame)
        
        # Titre principal
        title_label = QLabel("🔧 GESTION MAINTENANCE")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24px;
                font-weight: bold;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
        """)
        
        # Sous-titre
        subtitle_label = QLabel("Interventions • Planification • Historique • Indicateurs")
        subtitle_label.setStyleSheet("""
            QLabel {
                color: #ecf0f1;
                font-size: 14px;
                font-style: italic;
            }
        """)
        
        # Informations rapides
        info_frame = QFrame()
        info_frame.setStyleSheet("""
            QFrame {
                background-color: rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                padding: 15px;
            }
        """)
        info_layout = QVBoxLayout(info_frame)
        
        # Statut global
        self.global_status_label = QLabel("🟢 Système opérationnel")
        self.global_status_label.setStyleSheet("color: white; font-weight: bold;")
        
        # Dernière actualisation
        self.last_update_label = QLabel("Dernière actualisation: À l'instant")
        self.last_update_label.setStyleSheet("color: #ecf0f1; font-size: 11px;")
        
        info_layout.addWidget(self.global_status_label)
        info_layout.addWidget(self.last_update_label)
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(subtitle_label)
        header_layout.addStretch()
        header_layout.addWidget(info_frame)
        
        return header_frame
    
    def create_dashboard(self):
        """Crée le tableau de bord rapide"""
        dashboard_frame = QFrame()
        dashboard_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 12px;
                padding: 20px;
            }
        """)
        
        dashboard_layout = QGridLayout(dashboard_frame)
        
        # Titre du tableau de bord
        dashboard_title = QLabel("📊 TABLEAU DE BORD MAINTENANCE")
        dashboard_title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 15px;
            }
        """)
        dashboard_layout.addWidget(dashboard_title, 0, 0, 1, 4)
        
        # Indicateurs clés (première ligne)
        indicators_row1 = [
            ("🔴 Interventions en cours", "0", "#e74c3c"),
            ("🟡 Maintenances dues", "0", "#f39c12"),
            ("🟢 Maintenances planifiées", "0", "#27ae60"),
            ("📋 Bons de travail", "0", "#3498db")
        ]
        
        for i, (label, value, color) in enumerate(indicators_row1):
            indicator_frame = QFrame()
            indicator_frame.setStyleSheet(f"""
                QFrame {{
                    background-color: {color}15;
                    border: 2px solid {color};
                    border-radius: 8px;
                    padding: 15px;
                    min-width: 200px;
                }}
            """)
            
            indicator_layout = QVBoxLayout(indicator_frame)
            
            # Label
            label_widget = QLabel(label)
            label_widget.setStyleSheet("font-weight: bold; color: #2c3e50;")
            
            # Valeur
            value_widget = QLabel(value)
            value_widget.setStyleSheet(f"font-size: 24px; font-weight: bold; color: {color};")
            
            indicator_layout.addWidget(label_widget)
            indicator_layout.addWidget(value_widget)
            
            # Stocker les références pour mise à jour
            if i == 0:
                self.interventions_count_label = value_widget
            elif i == 1:
                self.due_maintenance_label = value_widget
            elif i == 2:
                self.planned_maintenance_label = value_widget
            elif i == 3:
                self.work_orders_label = value_widget
            
            dashboard_layout.addWidget(indicator_frame, 1, i)
        
        # Indicateurs supplémentaires (deuxième ligne)
        indicators_row2 = [
            ("⚡ Maintenances urgentes", "0", "#e67e22"),
            ("✅ Maintenances terminées", "0", "#16a085"),
            ("💰 Coût mensuel", "0€", "#8e44ad"),
            ("⏱️ Temps moyen", "0h", "#34495e")
        ]
        
        for i, (label, value, color) in enumerate(indicators_row2):
            indicator_frame = QFrame()
            indicator_frame.setStyleSheet(f"""
                QFrame {{
                    background-color: {color}15;
                    border: 2px solid {color};
                    border-radius: 8px;
                    padding: 15px;
                    min-width: 200px;
                }}
            """)
            
            indicator_layout = QVBoxLayout(indicator_frame)
            
            # Label
            label_widget = QLabel(label)
            label_widget.setStyleSheet("font-weight: bold; color: #2c3e50;")
            
            # Valeur
            value_widget = QLabel(value)
            value_widget.setStyleSheet(f"font-size: 20px; font-weight: bold; color: {color};")
            
            indicator_layout.addWidget(label_widget)
            indicator_layout.addWidget(value_widget)
            
            # Stocker les références pour mise à jour
            if i == 0:
                self.urgent_maintenance_label = value_widget
            elif i == 1:
                self.completed_maintenance_label = value_widget
            elif i == 2:
                self.monthly_cost_label = value_widget
            elif i == 3:
                self.avg_time_label = value_widget
            
            dashboard_layout.addWidget(indicator_frame, 2, i)
        
        # Barre de progression des maintenances
        progress_frame = QFrame()
        progress_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        progress_layout = QVBoxLayout(progress_frame)
        
        progress_title = QLabel("📈 Progression des maintenances du mois")
        progress_title.setStyleSheet("font-weight: bold; color: #2c3e50;")
        
        self.maintenance_progress = QProgressBar()
        self.maintenance_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #dee2e6;
                border-radius: 10px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #3498db;
                border-radius: 8px;
            }
        """)
        
        progress_layout.addWidget(progress_title)
        progress_layout.addWidget(self.maintenance_progress)
        
        dashboard_layout.addWidget(progress_frame, 3, 0, 1, 4)
        
        # Actions rapides
        actions_frame = QFrame()
        actions_frame.setStyleSheet("""
            QFrame {
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        actions_layout = QHBoxLayout(actions_frame)
        
        actions_title = QLabel("🚀 Actions rapides:")
        actions_title.setStyleSheet("font-weight: bold; color: #2c3e50;")
        
        # Boutons d'action rapide
        new_intervention_btn = QPushButton("➕ Nouvelle intervention")
        new_intervention_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        new_intervention_btn.clicked.connect(self.create_new_intervention)
        
        new_work_order_btn = QPushButton("📋 Nouveau bon de travail")
        new_work_order_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        new_work_order_btn.clicked.connect(self.create_new_work_order)
        
        schedule_maintenance_btn = QPushButton("📅 Planifier maintenance")
        schedule_maintenance_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        schedule_maintenance_btn.clicked.connect(self.schedule_maintenance)
        
        actions_layout.addWidget(actions_title)
        actions_layout.addWidget(new_intervention_btn)
        actions_layout.addWidget(new_work_order_btn)
        actions_layout.addWidget(schedule_maintenance_btn)
        actions_layout.addStretch()
        
        dashboard_layout.addWidget(actions_frame, 4, 0, 1, 4)
        
        return dashboard_frame
    
    def load_dashboard_data(self):
        """Charge les données du tableau de bord"""
        try:
            # Interventions en cours
            ongoing_interventions = self.db.get_maintenance_interventions()
            ongoing_count = len([i for i in ongoing_interventions if i[4] in ['En cours', 'Planifiée']])
            self.interventions_count_label.setText(str(ongoing_count))
            
            # Maintenances dues
            due_maintenance = self.db.get_equipment_needing_maintenance()
            self.due_maintenance_label.setText(str(len(due_maintenance)))
            
            # Maintenances planifiées
            planned_maintenance = self.db.get_maintenance_tasks()
            planned_count = len([m for m in planned_maintenance if m[6] == 'Planifiée'])
            self.planned_maintenance_label.setText(str(planned_count))
            
            # Bons de travail
            work_orders = self.db.get_work_orders() if hasattr(self.db, 'get_work_orders') else []
            self.work_orders_label.setText(str(len(work_orders)))
            
            # Maintenances urgentes (priorité haute ou critique)
            urgent_count = len([m for m in planned_maintenance if m[5] in ['Haute', 'Critique']])
            self.urgent_maintenance_label.setText(str(urgent_count))
            
            # Maintenances terminées
            completed_count = len([m for m in planned_maintenance if m[6] == 'Terminée'])
            self.completed_maintenance_label.setText(str(completed_count))
            
            # Coût mensuel (estimation basée sur les interventions)
            current_month = QDate.currentDate().month()
            current_year = QDate.currentDate().year()
            monthly_cost = 0
            try:
                # Calculer le coût des interventions du mois
                monthly_interventions = [i for i in ongoing_interventions 
                                       if QDate.fromString(i[3], 'yyyy-MM-dd').month() == current_month]
                monthly_cost = len(monthly_interventions) * 150  # Estimation moyenne
            except:
                monthly_cost = 0
            self.monthly_cost_label.setText(f"{monthly_cost}€")
            
            # Temps moyen des maintenances
            try:
                completed_maintenance = [m for m in planned_maintenance if m[6] == 'Terminée']
                if completed_maintenance:
                    # Estimation basée sur le nombre de maintenances terminées
                    avg_time = 2.5  # Heures moyennes
                    self.avg_time_label.setText(f"{avg_time}h")
                else:
                    self.avg_time_label.setText("0h")
            except:
                self.avg_time_label.setText("0h")
            
            # Progression mensuelle
            total_monthly_tasks = len([t for t in planned_maintenance 
                                     if QDate.fromString(t[4], 'yyyy-MM-dd').month() == current_month])
            completed_monthly_tasks = len([t for t in planned_maintenance 
                                        if QDate.fromString(t[4], 'yyyy-MM-dd').month() == current_month 
                                        and t[6] == 'Terminée'])
            
            if total_monthly_tasks > 0:
                progress = int((completed_monthly_tasks / total_monthly_tasks) * 100)
                self.maintenance_progress.setValue(progress)
            else:
                self.maintenance_progress.setValue(0)
            
            # Mettre à jour le statut global
            if ongoing_count == 0 and len(due_maintenance) == 0:
                self.global_status_label.setText("🟢 Système opérationnel")
                self.global_status_label.setStyleSheet("color: #27ae60; font-weight: bold;")
            elif len(due_maintenance) > 0:
                self.global_status_label.setText("🟡 Maintenances dues")
                self.global_status_label.setStyleSheet("color: #f39c12; font-weight: bold;")
            elif urgent_count > 0:
                self.global_status_label.setText("⚡ Maintenances urgentes")
                self.global_status_label.setStyleSheet("color: #e67e22; font-weight: bold;")
            else:
                self.global_status_label.setText("🔴 Interventions en cours")
                self.global_status_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
            
            # Mettre à jour la date de dernière actualisation
            current_time = QDateTime.currentDateTime().toString("dd/MM/yyyy HH:mm")
            self.last_update_label.setText(f"Dernière actualisation: {current_time}")
            
        except Exception as e:
            print(f"Erreur lors du chargement du tableau de bord: {str(e)}")
    
    def refresh_dashboard(self):
        """Actualise le tableau de bord"""
        self.load_dashboard_data()
    
    def on_intervention_created(self, intervention_id):
        """Appelé quand une nouvelle intervention est créée"""
        self.maintenance_updated.emit()
        self.load_dashboard_data()
    
    def on_work_order_created(self, work_order_id):
        """Appelé quand un nouveau bon de travail est créé"""
        self.maintenance_updated.emit()
        self.load_dashboard_data()
    
    def on_maintenance_scheduled(self, schedule_id):
        """Appelé quand une maintenance est planifiée"""
        self.maintenance_updated.emit()
        self.load_dashboard_data()
    
    def on_work_order_updated(self, work_order_id):
        """Appelé quand un bon de travail est mis à jour"""
        self.maintenance_updated.emit()
        self.load_dashboard_data()
    
    def on_work_order_deleted(self, work_order_id):
        """Appelé quand un bon de travail est supprimé"""
        self.maintenance_updated.emit()
        self.load_dashboard_data()
    
    def create_new_intervention(self):
        """Crée une nouvelle intervention"""
        try:
            # Basculer vers l'onglet des interventions
            self.main_tabs.setCurrentIndex(0)
            # Déclencher la création d'une nouvelle intervention
            if hasattr(self.interventions_tab, 'create_new_intervention'):
                self.interventions_tab.create_new_intervention()
        except Exception as e:
            QMessageBox.information(self, "Information", "Veuillez utiliser l'onglet Interventions pour créer une nouvelle intervention.")
    
    def create_new_work_order(self):
        """Crée un nouveau bon de travail"""
        try:
            # Basculer vers l'onglet des bons de travail
            self.main_tabs.setCurrentIndex(2)
            # Déclencher la création d'un nouveau bon de travail
            if hasattr(self.work_orders_tab, 'create_work_order'):
                self.work_orders_tab.create_work_order()
        except Exception as e:
            QMessageBox.information(self, "Information", "Veuillez utiliser l'onglet Bons de Travail pour créer un nouveau bon de travail.")
    
    def schedule_maintenance(self):
        """Planifie une maintenance"""
        try:
            # Basculer vers l'onglet de planification préventive
            self.main_tabs.setCurrentIndex(1)
            # Déclencher la planification d'une maintenance
            if hasattr(self.preventive_tab, 'schedule_maintenance'):
                self.preventive_tab.schedule_maintenance()
        except Exception as e:
            QMessageBox.information(self, "Information", "Veuillez utiliser l'onglet Planification Préventive pour planifier une maintenance.")
    
    def refresh_all_data(self):
        """Actualise toutes les données de maintenance"""
        self.load_dashboard_data()
        if hasattr(self.interventions_tab, 'refresh_data'):
            self.interventions_tab.refresh_data()
        if hasattr(self.preventive_tab, 'refresh_data'):
            self.preventive_tab.refresh_data()
        if hasattr(self.history_tab, 'refresh_data'):
            self.history_tab.refresh_data()
        if hasattr(self.checklist_tab, 'refresh_data'):
            self.checklist_tab.refresh_data()
        if hasattr(self.work_orders_tab, 'refresh_data'):
            self.work_orders_tab.refresh_data()
        if hasattr(self.indicators_tab, 'refresh_data'):
            self.indicators_tab.refresh_data()
