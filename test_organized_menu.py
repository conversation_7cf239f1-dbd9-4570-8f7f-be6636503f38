#!/usr/bin/env python3
"""
Test de la nouvelle organisation du menu en trois sections
"""

import sys
import os

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_organized_home_widget():
    """Test du nouveau widget d'accueil organisé"""
    print("🏠 TEST WIDGET D'ACCUEIL ORGANISÉ")
    print("-" * 50)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.organized_home_widget import OrganizedHomeWidget
        from database import Database
        
        # Créer une application Qt
        app = QApplication.instance()
        if not app:
            app = QApplication(sys.argv)
        
        # Créer une base de données
        db = Database()
        print("✓ Base de données créée")
        
        # Créer le widget d'accueil organisé
        home_widget = OrganizedHomeWidget(db)
        print("✓ Widget d'accueil organisé créé")
        
        # Vérifier les méthodes
        methods_to_check = [
            'setup_ui',
            'create_section',
            'create_section_item',
            'create_quick_stats',
            'update_statistics',
            'refresh_data'
        ]
        
        for method in methods_to_check:
            if hasattr(home_widget, method):
                print(f"✓ Méthode {method} présente")
            else:
                print(f"❌ Méthode {method} manquante")
                return False
        
        # Vérifier les signaux
        if hasattr(home_widget, 'section_requested'):
            print("✓ Signal section_requested présent")
        else:
            print("❌ Signal section_requested manquant")
            return False
        
        # Tester la mise à jour des statistiques
        home_widget.update_statistics()
        print("✓ Mise à jour des statistiques réussie")
        
        # Tester l'actualisation
        home_widget.refresh_data()
        print("✓ Actualisation des données réussie")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur test widget accueil : {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_main_menu_structure():
    """Test de la nouvelle structure du menu principal"""
    print("\n📋 TEST STRUCTURE MENU PRINCIPAL")
    print("-" * 50)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from database import Database
        from export.excel_export import ExcelExporter
        import main
        
        # Créer une application Qt
        app = QApplication.instance()
        if not app:
            app = QApplication(sys.argv)
        
        # Créer les composants
        db = Database()
        excel_exporter = ExcelExporter(db)
        
        # Créer l'application principale
        main_app = main.OptimizedSotramineApp(db, excel_exporter)
        print("✓ Application principale créée")
        
        # Vérifier que les boutons de menu sont organisés
        if hasattr(main_app, 'sidebar_buttons'):
            buttons = main_app.sidebar_buttons
            print(f"✓ {len(buttons)} boutons de menu trouvés")
            
            # Vérifier les sections attendues
            expected_sections = [
                'home', 'tasks', 'reports',  # Production
                'equipment', 'spare_parts',  # Maintenance
                'personnel', 'attendance',   # Personnel
                'settings'                   # Configuration
            ]
            
            missing_sections = []
            for section in expected_sections:
                if section in buttons:
                    print(f"✓ Section {section} présente")
                else:
                    print(f"❌ Section {section} manquante")
                    missing_sections.append(section)
            
            if missing_sections:
                print(f"❌ Sections manquantes : {missing_sections}")
                return False
            
        else:
            print("❌ Boutons de menu non trouvés")
            return False
        
        # Vérifier la méthode de navigation
        if hasattr(main_app, 'navigate_to_section'):
            print("✓ Méthode navigate_to_section présente")
        else:
            print("❌ Méthode navigate_to_section manquante")
            return False
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur test menu principal : {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_section_organization():
    """Test de l'organisation logique des sections"""
    print("\n🗂️ TEST ORGANISATION LOGIQUE DES SECTIONS")
    print("-" * 50)
    
    # Définir l'organisation attendue
    expected_organization = {
        "PRODUCTION": {
            "color": "#e74c3c",
            "sections": ["home", "tasks", "reports"],
            "description": "Gestion de la production et suivi"
        },
        "MAINTENANCE": {
            "color": "#f39c12", 
            "sections": ["equipment", "spare_parts"],
            "description": "Gestion des équipements et maintenance"
        },
        "PERSONNEL": {
            "color": "#27ae60",
            "sections": ["personnel", "attendance"],
            "description": "Gestion des ressources humaines"
        },
        "CONFIGURATION": {
            "color": "#95a5a6",
            "sections": ["settings"],
            "description": "Paramètres et configuration"
        }
    }
    
    print("📊 Organisation attendue :")
    
    for category, info in expected_organization.items():
        print(f"\n🔸 {category} ({info['color']})")
        print(f"   Description : {info['description']}")
        print(f"   Sections : {', '.join(info['sections'])}")
        
        # Vérifier la logique de l'organisation
        sections = info['sections']
        if category == "PRODUCTION":
            # Production doit contenir les éléments de suivi
            expected_elements = ["home", "tasks", "reports"]
            if all(elem in sections for elem in expected_elements):
                print("   ✓ Organisation logique correcte")
            else:
                print("   ❌ Organisation logique incorrecte")
                return False
                
        elif category == "MAINTENANCE":
            # Maintenance doit contenir équipements et pièces
            expected_elements = ["equipment", "spare_parts"]
            if all(elem in sections for elem in expected_elements):
                print("   ✓ Organisation logique correcte")
            else:
                print("   ❌ Organisation logique incorrecte")
                return False
                
        elif category == "PERSONNEL":
            # Personnel doit contenir gestion et pointage
            expected_elements = ["personnel", "attendance"]
            if all(elem in sections for elem in expected_elements):
                print("   ✓ Organisation logique correcte")
            else:
                print("   ❌ Organisation logique incorrecte")
                return False
    
    print("\n✅ Organisation logique validée")
    return True

def test_navigation_flow():
    """Test du flux de navigation entre sections"""
    print("\n🔄 TEST FLUX DE NAVIGATION")
    print("-" * 50)
    
    # Définir les flux de navigation logiques
    navigation_flows = [
        ("home", "tasks", "Accueil → Tâches"),
        ("tasks", "equipment", "Tâches → Équipements"),
        ("equipment", "spare_parts", "Équipements → Pièces"),
        ("personnel", "attendance", "Personnel → Pointage"),
        ("reports", "home", "Rapports → Accueil")
    ]
    
    print("🔄 Flux de navigation testés :")
    
    for from_section, to_section, description in navigation_flows:
        print(f"   {description} : {from_section} → {to_section}")
        
        # Vérifier que les sections existent dans l'organisation
        all_sections = ["home", "tasks", "reports", "equipment", "spare_parts", "personnel", "attendance", "settings"]
        
        if from_section in all_sections and to_section in all_sections:
            print(f"   ✓ Navigation {description} possible")
        else:
            print(f"   ❌ Navigation {description} impossible")
            return False
    
    print("\n✅ Tous les flux de navigation sont possibles")
    return True

def main():
    """Point d'entrée principal"""
    print("🗂️ SOTRAMINE PHOSPHATE - TEST ORGANISATION MENU EN 3 SECTIONS")
    print("Version 2.1 - Menu Organisé : Production, Maintenance, Personnel")
    print("=" * 75)
    
    tests = [
        ("Widget d'accueil organisé", test_organized_home_widget),
        ("Structure menu principal", test_main_menu_structure),
        ("Organisation logique sections", test_section_organization),
        ("Flux de navigation", test_navigation_flow)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\n🎯 {test_name.upper()}")
            success = test_func()
            results.append((test_name, success))
            
            if success:
                print(f"✅ {test_name} - SUCCÈS")
            else:
                print(f"❌ {test_name} - ÉCHEC")
                
        except Exception as e:
            print(f"❌ Erreur critique dans {test_name} : {str(e)}")
            results.append((test_name, False))
    
    # Résumé final
    print("\n" + "=" * 75)
    print("📋 RÉSUMÉ DES TESTS ORGANISATION MENU")
    print("=" * 75)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ SUCCÈS" if success else "❌ ÉCHEC"
        print(f"{status:<12} {test_name}")
    
    print(f"\n📊 RÉSULTATS FINAUX :")
    print(f"✅ Tests réussis : {passed}/{total}")
    print(f"📈 Taux de réussite : {(passed/total*100):.1f}%")
    
    if passed >= 3:  # Au moins 3 tests sur 4
        print("\n🎉 ORGANISATION MENU EN 3 SECTIONS RÉUSSIE !")
        print("✅ Le menu est maintenant organisé logiquement")
        
        print("\n📝 NOUVELLE ORGANISATION :")
        print("   📊 PRODUCTION : Tableau de bord, Tâches, Rapports")
        print("   🔧 MAINTENANCE : Équipements, Pièces de rechange")
        print("   👥 PERSONNEL : Gestion personnel, Pointage")
        print("   ⚙️ CONFIGURATION : Paramètres")
        
        print("\n🚀 AVANTAGES :")
        print("   • Navigation plus intuitive et logique")
        print("   • Regroupement par domaine métier")
        print("   • Interface plus professionnelle")
        print("   • Meilleure expérience utilisateur")
        
        print("\n✅ UTILISATION :")
        print("   • Menu latéral organisé en sections colorées")
        print("   • Accueil avec navigation par domaines")
        print("   • Flux de travail optimisé")
        print("   • Accès rapide aux fonctions principales")
    else:
        print(f"\n⚠️ {total - passed} test(s) ont échoué")
        print("🔧 L'organisation du menu nécessite des corrections")
    
    return passed >= 3

if __name__ == "__main__":
    try:
        success = main()
        print(f"\n👋 Test organisation terminé - {'Succès' if success else 'Échec'}")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 Test interrompu par l'utilisateur")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Erreur critique : {str(e)}")
        sys.exit(1)
