#!/usr/bin/env python3
"""
Test de l'importation des équipements via l'interface graphique
"""

import sys
import os
import csv
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_sample_equipment_csv():
    """Crée un fichier CSV d'exemple pour les équipements"""
    sample_data = [
        {
            'nom': 'Compresseur Atlas Copco',
            'code': 'COMP-AC-001',
            'modele': 'GA55VSD',
            'numero_serie': 'AC2024001',
            'fabricant': 'Atlas Copco',
            'date_achat': '2024-01-15',
            'date_installation': '2024-02-01',
            'localisation': 'Atelier Mécanique',
            'statut': 'En service',
            'derniere_maintenance': '2024-06-15',
            'prochaine_maintenance': '2024-12-15',
            'notes': 'Compresseur principal pour air comprimé - Maintenance préventive tous les 6 mois'
        },
        {
            'nom': 'Pompe Centrifuge Grundfos',
            'code': 'PUMP-GR-002',
            'modele': 'CR32-4',
            'numero_serie': 'GR2024002',
            'fabricant': 'Grundfos',
            'date_achat': '2024-02-20',
            'date_installation': '2024-03-01',
            'localisation': 'Station de Pompage A',
            'statut': 'En service',
            'derniere_maintenance': '2024-07-01',
            'prochaine_maintenance': '2025-01-01',
            'notes': 'Pompe principale circuit eau - Contrôle mensuel requis'
        },
        {
            'nom': 'Moteur Électrique Siemens',
            'code': 'MOT-SI-003',
            'modele': '1LA7 133-4AA60',
            'numero_serie': 'SI2024003',
            'fabricant': 'Siemens',
            'date_achat': '2024-03-10',
            'date_installation': '2024-03-25',
            'localisation': 'Zone Production B',
            'statut': 'En service',
            'derniere_maintenance': '2024-06-25',
            'prochaine_maintenance': '2024-12-25',
            'notes': 'Moteur convoyeur principal - Lubrification trimestrielle'
        }
    ]
    
    csv_file = 'equipements_exemple.csv'
    
    with open(csv_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = sample_data[0].keys()
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for row in sample_data:
            writer.writerow(row)
    
    print(f"✓ Fichier CSV d'exemple créé : {csv_file}")
    return csv_file

def test_equipment_import_gui():
    """Test de l'importation des équipements via GUI"""
    print("🖥️ TEST DE L'IMPORTATION GUI DES ÉQUIPEMENTS")
    print("=" * 60)
    
    try:
        # Créer l'application Qt
        app = QApplication(sys.argv)
        app.setApplicationName("SOTRAMINE PHOSPHATE - Test Import GUI")
        
        print("✓ Application Qt créée")
        
        # Créer le fichier CSV d'exemple
        csv_file = create_sample_equipment_csv()
        
        # Importer les modules nécessaires
        from database import Database
        from export.excel_export import ExcelExporter
        from main import OptimizedSotramineApp
        from gui.equipment_import_dialog import EquipmentImportDialog
        
        print("✓ Modules importés")
        
        # Initialiser la base de données
        db = Database()
        excel_exporter = ExcelExporter(db)
        main_app = OptimizedSotramineApp(db, excel_exporter)
        
        print("✓ Application principale créée")
        
        # Tester le dialog d'importation
        print("\n📥 Test du dialog d'importation...")
        
        # Vérifier que le dialog peut être créé
        dialog = EquipmentImportDialog(main_app, db)
        print("✓ Dialog d'importation créé")
        
        # Vérifier les composants du dialog
        assert hasattr(dialog, 'select_file_btn'), "Bouton de sélection de fichier manquant"
        assert hasattr(dialog, 'import_btn'), "Bouton d'importation manquant"
        assert hasattr(dialog, 'preview_table'), "Table de prévisualisation manquante"
        assert hasattr(dialog, 'progress_bar'), "Barre de progression manquante"
        assert hasattr(dialog, 'results_text'), "Zone de résultats manquante"
        
        print("✓ Tous les composants du dialog présents")
        
        # Tester l'importation directe
        print("\n📊 Test d'importation directe...")
        equipment_before = db.get_all_equipment()
        count_before = len(equipment_before)
        
        result = db.import_equipment_from_csv(csv_file)
        
        equipment_after = db.get_all_equipment()
        count_after = len(equipment_after)
        
        print(f"✓ Équipements avant : {count_before}")
        print(f"✓ Équipements après : {count_after}")
        print(f"✓ Nouveaux équipements : {count_after - count_before}")
        print(f"✓ Résultat import : {result['imported_count']} importés, {len(result['errors'])} erreurs")
        
        # Afficher l'application brièvement
        main_app.show()
        print("✓ Application affichée")
        
        # Naviguer vers la section équipements
        main_app.navigate_to_section('equipment')
        print("✓ Navigation vers section équipements")
        
        # Vérifier que l'action d'importation est disponible
        if hasattr(main_app, 'import_equipment_dialog'):
            print("✓ Méthode d'importation GUI disponible")
        else:
            print("❌ Méthode d'importation GUI manquante")
        
        # Nettoyer le fichier de test
        try:
            os.remove(csv_file)
            print("✓ Fichier de test supprimé")
        except:
            pass
        
        # Fermer automatiquement après 5 secondes
        QTimer.singleShot(5000, app.quit)
        
        print(f"\n⏰ Application affichée pendant 5 secondes...")
        print("   (Fermez manuellement pour continuer)")
        
        # Lancer l'application
        app.exec_()
        
        db.close()
        
        # Évaluer le succès
        success = (
            result['success'] and 
            result['imported_count'] > 0 and 
            count_after > count_before
        )
        
        return success
        
    except Exception as e:
        print(f"\n❌ ERREUR DANS LE TEST GUI : {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_dialog_components():
    """Test des composants du dialog d'importation"""
    print("\n🧩 TEST DES COMPOSANTS DU DIALOG")
    print("-" * 40)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from database import Database
        from gui.equipment_import_dialog import EquipmentImportDialog
        
        app = QApplication.instance()
        if not app:
            app = QApplication(sys.argv)
        
        db = Database()
        dialog = EquipmentImportDialog(None, db)
        
        # Vérifier les composants
        components = [
            ('select_file_btn', 'Bouton sélection fichier'),
            ('import_btn', 'Bouton importation'),
            ('preview_table', 'Table prévisualisation'),
            ('progress_bar', 'Barre de progression'),
            ('results_text', 'Zone résultats'),
            ('file_label', 'Label fichier'),
            ('status_label', 'Label statut')
        ]
        
        all_present = True
        for attr_name, description in components:
            if hasattr(dialog, attr_name):
                print(f"✓ {description}")
            else:
                print(f"❌ {description} manquant")
                all_present = False
        
        db.close()
        return all_present
        
    except Exception as e:
        print(f"❌ Erreur test composants : {str(e)}")
        return False

def main():
    """Point d'entrée principal"""
    print("🖥️ SOTRAMINE PHOSPHATE - TEST IMPORTATION GUI ÉQUIPEMENTS")
    print("Version 2.1 - Interface d'Importation")
    print("=" * 70)
    
    tests = [
        ("Composants du dialog", test_dialog_components),
        ("Importation GUI complète", test_equipment_import_gui)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\n🎯 {test_name.upper()}")
            success = test_func()
            results.append((test_name, success))
            
            if success:
                print(f"✅ {test_name} - SUCCÈS")
            else:
                print(f"❌ {test_name} - ÉCHEC")
                
        except Exception as e:
            print(f"❌ Erreur critique dans {test_name} : {str(e)}")
            results.append((test_name, False))
    
    # Résumé final
    print("\n" + "=" * 70)
    print("📋 RÉSUMÉ DES TESTS GUI IMPORTATION")
    print("=" * 70)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ SUCCÈS" if success else "❌ ÉCHEC"
        print(f"{status:<12} {test_name}")
    
    print(f"\n📊 RÉSULTATS FINAUX :")
    print(f"✅ Tests réussis : {passed}/{total}")
    print(f"📈 Taux de réussite : {(passed/total*100):.1f}%")
    
    if passed == total:
        print("\n🎉 IMPORTATION GUI DES ÉQUIPEMENTS FONCTIONNELLE !")
        print("✅ L'interface d'importation des équipements fonctionne parfaitement")
        
        print("\n📝 FONCTIONNALITÉS GUI VALIDÉES :")
        print("   • Dialog d'importation moderne")
        print("   • Sélection de fichier CSV")
        print("   • Prévisualisation des données")
        print("   • Barre de progression")
        print("   • Rapport de résultats détaillé")
        print("   • Gestion d'erreurs complète")
        
        print("\n🚀 Pour utiliser l'importation GUI :")
        print("   1. Lancer l'application : python main.py")
        print("   2. Aller dans la section Équipements")
        print("   3. Cliquer sur '📥 Importer CSV'")
        print("   4. Sélectionner votre fichier CSV")
        print("   5. Lancer l'importation")
    else:
        print(f"\n⚠️ {total - passed} test(s) ont échoué")
        print("🔧 L'interface d'importation nécessite des corrections")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        print(f"\n👋 Test GUI importation terminé - {'Succès' if success else 'Échec'}")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 Test interrompu par l'utilisateur")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Erreur critique : {str(e)}")
        sys.exit(1)
