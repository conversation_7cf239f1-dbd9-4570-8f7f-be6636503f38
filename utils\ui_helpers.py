from PyQt5.QtWidgets import (QPush<PERSON>utton, QTabWidget, QToolBar, QAction, 
                             QHBoxLayout, QVBoxLayout, QMenu, QMenuBar)
from PyQt5.QtGui import QIcon
from PyQt5.QtCore import Qt

# Couleurs standards pour les boutons
BUTTON_STYLES = {
    'save': 'background-color: #2ecc71; color: white; font-weight: bold;',
    'cancel': 'background-color: #e74c3c; color: white; font-weight: bold;',
    'add': 'background-color: #3498db; color: white; font-weight: bold;',
    'edit': 'background-color: #f1c40f; color: #2c3e50; font-weight: bold;',
    'delete': 'background-color: #e67e22; color: white; font-weight: bold;'
}

# Actions standard pour les barres d'outils et menus
STANDARD_ACTIONS = {
    'new': {'text': 'Nouveau', 'icon': 'plus.png', 'shortcut': 'Ctrl+N'},
    'save': {'text': 'Enregistrer', 'icon': 'save.png', 'shortcut': 'Ctrl+S'},
    'delete': {'text': 'Supprimer', 'icon': 'delete.png', 'shortcut': 'Del'},
    'edit': {'text': 'Modifier', 'icon': 'edit.png', 'shortcut': 'Ctrl+E'},
    'refresh': {'text': 'Actualiser', 'icon': 'refresh.png', 'shortcut': 'F5'},
    'export': {'text': 'Exporter', 'icon': 'export.png', 'shortcut': 'Ctrl+E'},
    'import': {'text': 'Importer', 'icon': 'import.png', 'shortcut': 'Ctrl+I'},
    'search': {'text': 'Rechercher', 'icon': 'search.png', 'shortcut': 'Ctrl+F'},
    'print': {'text': 'Imprimer', 'icon': 'print.png', 'shortcut': 'Ctrl+P'},
    'help': {'text': 'Aide', 'icon': 'help.png', 'shortcut': 'F1'}
}


def create_standard_button(text, role='default', icon_path=None, on_click=None):
    """Crée un bouton standard avec style et icône"""
    btn = QPushButton(text)
    if role in BUTTON_STYLES:
        btn.setStyleSheet(f"QPushButton {{{BUTTON_STYLES[role]}}} QPushButton:hover {{opacity:0.85;}}")
    if icon_path:
        btn.setIcon(QIcon(icon_path))
    if on_click:
        btn.clicked.connect(on_click)
    return btn


def create_standard_action(action_id, parent=None, on_triggered=None):
    """Crée une action standard pour les menus et barres d'outils"""
    if action_id not in STANDARD_ACTIONS:
        raise ValueError(f"Action '{action_id}' non reconnue")
    
    action_data = STANDARD_ACTIONS[action_id]
    action = QAction(action_data['text'], parent)
    
    # Ajouter l'icône si disponible
    icon_path = f"resources/icons/{action_data['icon']}"
    action.setIcon(QIcon(icon_path))
    
    # Ajouter le raccourci
    action.setShortcut(action_data['shortcut'])
    
    # Connecter le signal si fourni
    if on_triggered:
        action.triggered.connect(on_triggered)
    
    return action


def create_standard_toolbar(parent, actions_list, title="Barre d'outils"):
    """Crée une barre d'outils standard avec les actions spécifiées"""
    toolbar = QToolBar(title, parent)
    toolbar.setMovable(False)
    toolbar.setFloatable(False)
    
    for action_id in actions_list:
        action = create_standard_action(action_id, parent)
        toolbar.addAction(action)
    
    return toolbar


def create_standard_menu(menu_bar, menu_title, actions_list):
    """Crée un menu standard avec les actions spécifiées"""
    menu = menu_bar.addMenu(menu_title)
    
    for action_id in actions_list:
        action = create_standard_action(action_id, menu_bar)
        menu.addAction(action)
    
    return menu


def create_button_layout(buttons_list, parent=None):
    """Crée un layout horizontal avec des boutons standards"""
    layout = QHBoxLayout()
    
    for button_data in buttons_list:
        if isinstance(button_data, str):
            # Bouton simple avec texte
            btn = create_standard_button(button_data)
        elif isinstance(button_data, dict):
            # Bouton avec configuration complète
            btn = create_standard_button(
                text=button_data.get('text', ''),
                role=button_data.get('role', 'default'),
                icon_path=button_data.get('icon'),
                on_click=button_data.get('on_click')
            )
        else:
            continue
        
        layout.addWidget(btn)
    
    layout.addStretch()  # Espace flexible à droite
    return layout


def add_tabs(tab_widget: QTabWidget, tabs: list):
    """
    Ajoute des onglets à un QTabWidget à partir d'une liste de tuples
    tabs = [(widget, 'Titre', 'chemin/icone.png'), ...]
    """
    for tab in tabs:
        if len(tab) == 3:
            widget, title, icon_path = tab
            tab_widget.addTab(widget, QIcon(icon_path), title)
        elif len(tab) == 2:
            widget, title = tab
            tab_widget.addTab(widget, title)
        else:
            raise ValueError('Chaque tab doit être (widget, titre[, icône])')


def create_standard_dialog_buttons(parent, on_save=None, on_cancel=None):
    """Crée les boutons standard pour une boîte de dialogue"""
    layout = QHBoxLayout()
    
    save_btn = create_standard_button("Enregistrer", "save", on_click=on_save)
    cancel_btn = create_standard_button("Annuler", "cancel", on_click=on_cancel)
    
    layout.addWidget(save_btn)
    layout.addWidget(cancel_btn)
    layout.addStretch()
    
    return layout


def create_crud_buttons(parent, on_add=None, on_edit=None, on_delete=None):
    """Crée les boutons CRUD standard (Ajouter, Modifier, Supprimer)"""
    layout = QHBoxLayout()
    
    add_btn = create_standard_button("Ajouter", "add", on_click=on_add)
    edit_btn = create_standard_button("Modifier", "edit", on_click=on_edit)
    delete_btn = create_standard_button("Supprimer", "delete", on_click=on_delete)
    
    layout.addWidget(add_btn)
    layout.addWidget(edit_btn)
    layout.addWidget(delete_btn)
    layout.addStretch()
    
    return layout
