"""
Module complet de gestion des tâches pour SOTRAMINE PHOSPHATE
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                             QTableWidgetItem, QPushButton, QLabel, QFrame, 
                             QComboBox, QLineEdit, QTextEdit, QDateEdit, 
                             QDialog, QFormLayout, QMessageBox, QHeaderView,
                             QSplitter, QTabWidget, QProgressBar)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QColor
from utils.data_refresh_manager import RefreshableWidget, notify_data_changed

class TaskManagerComplete(QWidget, RefreshableWidget):
    """Gestionnaire complet des tâches"""
    
    def __init__(self, db, parent=None):
        super().__init__(parent)
        RefreshableWidget.__init__(self)
        self.db = db
        self.setup_ui()
        self.load_tasks()
        self.enable_auto_refresh(['tasks'])
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # En-tête
        header = self.create_header()
        layout.addWidget(header)
        
        # Contenu principal avec splitter
        splitter = QSplitter(Qt.Horizontal)
        
        # Panel de gauche - Liste des tâches
        left_panel = self.create_tasks_panel()
        splitter.addWidget(left_panel)
        
        # Panel de droite - Détails et actions
        right_panel = self.create_details_panel()
        splitter.addWidget(right_panel)
        
        splitter.setSizes([800, 400])
        layout.addWidget(splitter)
    
    def create_header(self):
        """Crée l'en-tête du module"""
        header = QFrame()
        header.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                padding: 20px;
                border-radius: 0;
            }
        """)
        header.setFixedHeight(80)
        
        layout = QHBoxLayout(header)
        
        # Titre
        title = QLabel("📋 GESTION DES TÂCHES")
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24px;
                font-weight: bold;
            }
        """)
        layout.addWidget(title)
        
        layout.addStretch()
        
        # Boutons d'actions
        btn_new = QPushButton("➕ Nouvelle Tâche")
        btn_new.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        btn_new.clicked.connect(self.create_new_task)
        layout.addWidget(btn_new)
        
        btn_export = QPushButton("📄 Exporter")
        btn_export.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        btn_export.clicked.connect(self.export_tasks)
        layout.addWidget(btn_export)
        
        return header
    
    def create_tasks_panel(self):
        """Crée le panel des tâches"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Filtres
        filters_frame = self.create_filters()
        layout.addWidget(filters_frame)
        
        # Table des tâches
        self.tasks_table = QTableWidget()
        self.tasks_table.setColumnCount(7)
        self.tasks_table.setHorizontalHeaderLabels([
            "ID", "Titre", "Statut", "Priorité", "Assigné à", "Échéance", "Progression"
        ])
        
        # Style de la table
        self.tasks_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #ddd;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eee;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)
        
        self.tasks_table.setAlternatingRowColors(True)
        self.tasks_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.tasks_table.horizontalHeader().setStretchLastSection(True)
        self.tasks_table.itemSelectionChanged.connect(self.on_task_selected)
        
        layout.addWidget(self.tasks_table)
        
        return panel
    
    def create_filters(self):
        """Crée les filtres"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 15px;
            }
        """)
        
        layout = QHBoxLayout(frame)
        
        # Filtre par statut
        layout.addWidget(QLabel("Statut:"))
        self.status_filter = QComboBox()
        self.status_filter.addItems(["Tous", "En attente", "En cours", "Terminé", "Annulé"])
        self.status_filter.currentTextChanged.connect(self.filter_tasks)
        layout.addWidget(self.status_filter)
        
        # Filtre par priorité
        layout.addWidget(QLabel("Priorité:"))
        self.priority_filter = QComboBox()
        self.priority_filter.addItems(["Toutes", "Faible", "Normale", "Élevée", "Critique"])
        self.priority_filter.currentTextChanged.connect(self.filter_tasks)
        layout.addWidget(self.priority_filter)
        
        # Recherche
        layout.addWidget(QLabel("Recherche:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Rechercher une tâche...")
        self.search_edit.textChanged.connect(self.filter_tasks)
        layout.addWidget(self.search_edit)
        
        layout.addStretch()
        
        return frame
    
    def create_details_panel(self):
        """Crée le panel des détails"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Titre
        title = QLabel("📝 Détails de la Tâche")
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # Onglets de détails
        self.details_tabs = QTabWidget()
        self.details_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #ddd;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f8f9fa;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        
        # Onglet informations générales
        self.info_tab = self.create_info_tab()
        self.details_tabs.addTab(self.info_tab, "Informations")
        
        # Onglet historique
        self.history_tab = self.create_history_tab()
        self.details_tabs.addTab(self.history_tab, "Historique")
        
        layout.addWidget(self.details_tabs)
        
        # Boutons d'actions
        actions_frame = self.create_actions_frame()
        layout.addWidget(actions_frame)
        
        return panel
    
    def create_info_tab(self):
        """Crée l'onglet d'informations"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # Labels d'informations
        self.info_labels = {}
        info_fields = [
            ("ID", "id_label"),
            ("Titre", "title_label"),
            ("Description", "description_label"),
            ("Statut", "status_label"),
            ("Priorité", "priority_label"),
            ("Assigné à", "assigned_label"),
            ("Date de création", "created_label"),
            ("Date d'échéance", "due_label"),
            ("Progression", "progress_label")
        ]
        
        for field_name, label_key in info_fields:
            field_frame = QFrame()
            field_layout = QHBoxLayout(field_frame)
            field_layout.setContentsMargins(0, 5, 0, 5)
            
            name_label = QLabel(f"{field_name}:")
            name_label.setStyleSheet("font-weight: bold; min-width: 100px;")
            field_layout.addWidget(name_label)
            
            value_label = QLabel("-")
            value_label.setStyleSheet("color: #495057;")
            if field_name == "Description":
                value_label.setWordWrap(True)
            field_layout.addWidget(value_label)
            
            self.info_labels[label_key] = value_label
            layout.addWidget(field_frame)
        
        layout.addStretch()
        
        return tab
    
    def create_history_tab(self):
        """Crée l'onglet d'historique"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        
        self.history_list = QTableWidget()
        self.history_list.setColumnCount(3)
        self.history_list.setHorizontalHeaderLabels(["Date", "Action", "Utilisateur"])
        self.history_list.horizontalHeader().setStretchLastSection(True)
        
        layout.addWidget(self.history_list)
        
        return tab
    
    def create_actions_frame(self):
        """Crée le cadre des actions"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                margin-top: 15px;
            }
        """)
        
        layout = QHBoxLayout(frame)
        
        # Boutons d'actions
        actions = [
            ("✏️ Modifier", "#f39c12", self.edit_task),
            ("✅ Marquer Terminé", "#27ae60", self.mark_completed),
            ("❌ Supprimer", "#e74c3c", self.delete_task)
        ]
        
        for text, color, action in actions:
            btn = QPushButton(text)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 16px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: {color}dd;
                }}
                QPushButton:disabled {{
                    background-color: #bdc3c7;
                }}
            """)
            btn.clicked.connect(action)
            btn.setEnabled(False)  # Désactivé par défaut
            layout.addWidget(btn)
            
            # Stocker les boutons pour les activer/désactiver
            if text == "✏️ Modifier":
                self.edit_btn = btn
            elif text == "✅ Marquer Terminé":
                self.complete_btn = btn
            elif text == "❌ Supprimer":
                self.delete_btn = btn
        
        layout.addStretch()
        
        return frame
    
    def load_tasks(self):
        """Charge les tâches depuis la base de données"""
        try:
            # Simuler des données de tâches (à remplacer par de vraies données)
            tasks = [
                (1, "Maintenance pompe A1", "En cours", "Élevée", "Jean Dupont", "2025-08-15", 75),
                (2, "Inspection équipement B2", "En attente", "Normale", "Marie Martin", "2025-08-20", 0),
                (3, "Réparation moteur C3", "Terminé", "Critique", "Pierre Durand", "2025-08-10", 100),
                (4, "Nettoyage zone D4", "En cours", "Faible", "Sophie Bernard", "2025-08-18", 50),
                (5, "Contrôle qualité E5", "En attente", "Normale", "Luc Moreau", "2025-08-25", 0)
            ]
            
            self.all_tasks = tasks
            self.populate_table(tasks)
            
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des tâches : {str(e)}")
    
    def populate_table(self, tasks):
        """Remplit la table avec les tâches"""
        self.tasks_table.setRowCount(len(tasks))
        
        for row, task in enumerate(tasks):
            for col, value in enumerate(task):
                if col == 6:  # Colonne progression
                    # Créer une barre de progression
                    progress_widget = QWidget()
                    progress_layout = QHBoxLayout(progress_widget)
                    progress_layout.setContentsMargins(5, 5, 5, 5)
                    
                    progress_bar = QProgressBar()
                    progress_bar.setValue(value)
                    progress_bar.setStyleSheet("""
                        QProgressBar {
                            border: 1px solid #bdc3c7;
                            border-radius: 3px;
                            text-align: center;
                        }
                        QProgressBar::chunk {
                            background-color: #3498db;
                            border-radius: 2px;
                        }
                    """)
                    progress_layout.addWidget(progress_bar)
                    
                    self.tasks_table.setCellWidget(row, col, progress_widget)
                else:
                    item = QTableWidgetItem(str(value))
                    
                    # Colorer selon le statut
                    if col == 2:  # Colonne statut
                        if value == "Terminé":
                            item.setBackground(QColor("#d4edda"))
                        elif value == "En cours":
                            item.setBackground(QColor("#fff3cd"))
                        elif value == "En attente":
                            item.setBackground(QColor("#f8d7da"))
                    
                    # Colorer selon la priorité
                    elif col == 3:  # Colonne priorité
                        if value == "Critique":
                            item.setBackground(QColor("#f8d7da"))
                        elif value == "Élevée":
                            item.setBackground(QColor("#fff3cd"))
                    
                    self.tasks_table.setItem(row, col, item)
        
        # Ajuster les colonnes
        self.tasks_table.resizeColumnsToContents()
    
    def filter_tasks(self):
        """Filtre les tâches selon les critères"""
        status_filter = self.status_filter.currentText()
        priority_filter = self.priority_filter.currentText()
        search_text = self.search_edit.text().lower()
        
        filtered_tasks = []
        
        for task in self.all_tasks:
            # Filtre par statut
            if status_filter != "Tous" and task[2] != status_filter:
                continue
            
            # Filtre par priorité
            if priority_filter != "Toutes" and task[3] != priority_filter:
                continue
            
            # Filtre par recherche
            if search_text and search_text not in task[1].lower():
                continue
            
            filtered_tasks.append(task)
        
        self.populate_table(filtered_tasks)
    
    def on_task_selected(self):
        """Gère la sélection d'une tâche"""
        current_row = self.tasks_table.currentRow()
        if current_row >= 0:
            # Activer les boutons d'actions
            self.edit_btn.setEnabled(True)
            self.complete_btn.setEnabled(True)
            self.delete_btn.setEnabled(True)
            
            # Charger les détails de la tâche
            self.load_task_details(current_row)
        else:
            # Désactiver les boutons
            self.edit_btn.setEnabled(False)
            self.complete_btn.setEnabled(False)
            self.delete_btn.setEnabled(False)
    
    def load_task_details(self, row):
        """Charge les détails d'une tâche"""
        # Récupérer les données de la ligne
        task_data = []
        for col in range(self.tasks_table.columnCount() - 1):  # Exclure la colonne progression
            item = self.tasks_table.item(row, col)
            if item:
                task_data.append(item.text())
            else:
                task_data.append("")
        
        # Mettre à jour les labels d'informations
        if len(task_data) >= 6:
            self.info_labels["id_label"].setText(task_data[0])
            self.info_labels["title_label"].setText(task_data[1])
            self.info_labels["status_label"].setText(task_data[2])
            self.info_labels["priority_label"].setText(task_data[3])
            self.info_labels["assigned_label"].setText(task_data[4])
            self.info_labels["due_label"].setText(task_data[5])
            self.info_labels["description_label"].setText("Description détaillée de la tâche...")
            self.info_labels["created_label"].setText("2025-08-09")
            
            # Progression
            progress_widget = self.tasks_table.cellWidget(row, 6)
            if progress_widget:
                progress_bar = progress_widget.findChild(QProgressBar)
                if progress_bar:
                    self.info_labels["progress_label"].setText(f"{progress_bar.value()}%")
    
    def create_new_task(self):
        """Crée une nouvelle tâche"""
        dialog = TaskDialog(self.db, self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_tasks()
            notify_data_changed('tasks')
    
    def edit_task(self):
        """Modifie la tâche sélectionnée"""
        current_row = self.tasks_table.currentRow()
        if current_row >= 0:
            task_id = self.tasks_table.item(current_row, 0).text()
            dialog = TaskDialog(self.db, self, task_id)
            if dialog.exec_() == QDialog.Accepted:
                self.load_tasks()
                notify_data_changed('tasks')
    
    def mark_completed(self):
        """Marque la tâche comme terminée"""
        current_row = self.tasks_table.currentRow()
        if current_row >= 0:
            reply = QMessageBox.question(self, "Confirmer", 
                                       "Marquer cette tâche comme terminée ?",
                                       QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.Yes:
                # Mettre à jour le statut
                self.tasks_table.item(current_row, 2).setText("Terminé")
                # Mettre à jour la progression
                progress_widget = self.tasks_table.cellWidget(current_row, 6)
                if progress_widget:
                    progress_bar = progress_widget.findChild(QProgressBar)
                    if progress_bar:
                        progress_bar.setValue(100)
                
                notify_data_changed('tasks')
                QMessageBox.information(self, "Succès", "Tâche marquée comme terminée !")
    
    def delete_task(self):
        """Supprime la tâche sélectionnée"""
        current_row = self.tasks_table.currentRow()
        if current_row >= 0:
            reply = QMessageBox.question(self, "Confirmer", 
                                       "Êtes-vous sûr de vouloir supprimer cette tâche ?",
                                       QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.Yes:
                self.tasks_table.removeRow(current_row)
                notify_data_changed('tasks')
                QMessageBox.information(self, "Succès", "Tâche supprimée !")
    
    def export_tasks(self):
        """Exporte les tâches vers Excel"""
        try:
            from export.excel_export import ExcelExporter
            exporter = ExcelExporter(self.db)
            
            file_path = "export_taches.xlsx"
            success = exporter.export_tasks_list(file_path)
            
            if success:
                QMessageBox.information(self, "Export Réussi", 
                                       f"📄 Export Excel réussi !\n\nFichier : {file_path}")
            else:
                QMessageBox.warning(self, "Erreur Export", 
                                   "❌ Erreur lors de l'export Excel.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'export : {str(e)}")
    
    def refresh_data(self):
        """Actualise les données"""
        self.load_tasks()


class TaskDialog(QDialog):
    """Dialog pour créer/modifier une tâche"""
    
    def __init__(self, db, parent=None, task_id=None):
        super().__init__(parent)
        self.db = db
        self.task_id = task_id
        self.setup_ui()
        
        if task_id:
            self.setWindowTitle("Modifier la Tâche")
            self.load_task_data()
        else:
            self.setWindowTitle("Nouvelle Tâche")
    
    def setup_ui(self):
        """Configure l'interface du dialog"""
        self.setFixedSize(500, 600)
        layout = QVBoxLayout(self)
        
        # Formulaire
        form_layout = QFormLayout()
        
        self.title_edit = QLineEdit()
        form_layout.addRow("Titre:", self.title_edit)
        
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(100)
        form_layout.addRow("Description:", self.description_edit)
        
        self.status_combo = QComboBox()
        self.status_combo.addItems(["En attente", "En cours", "Terminé", "Annulé"])
        form_layout.addRow("Statut:", self.status_combo)
        
        self.priority_combo = QComboBox()
        self.priority_combo.addItems(["Faible", "Normale", "Élevée", "Critique"])
        self.priority_combo.setCurrentText("Normale")
        form_layout.addRow("Priorité:", self.priority_combo)
        
        self.assigned_edit = QLineEdit()
        form_layout.addRow("Assigné à:", self.assigned_edit)
        
        self.due_date = QDateEdit()
        self.due_date.setDate(QDate.currentDate().addDays(7))
        self.due_date.setCalendarPopup(True)
        form_layout.addRow("Date d'échéance:", self.due_date)
        
        layout.addLayout(form_layout)
        
        # Boutons
        buttons_layout = QHBoxLayout()
        
        btn_save = QPushButton("💾 Enregistrer")
        btn_save.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        btn_save.clicked.connect(self.save_task)
        buttons_layout.addWidget(btn_save)
        
        btn_cancel = QPushButton("❌ Annuler")
        btn_cancel.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        btn_cancel.clicked.connect(self.reject)
        buttons_layout.addWidget(btn_cancel)
        
        layout.addLayout(buttons_layout)
    
    def load_task_data(self):
        """Charge les données de la tâche à modifier"""
        # Simuler le chargement des données
        self.title_edit.setText("Tâche exemple")
        self.description_edit.setText("Description de la tâche...")
        self.status_combo.setCurrentText("En cours")
        self.priority_combo.setCurrentText("Normale")
        self.assigned_edit.setText("Jean Dupont")
    
    def save_task(self):
        """Sauvegarde la tâche"""
        if not self.title_edit.text().strip():
            QMessageBox.warning(self, "Erreur", "Le titre est obligatoire !")
            return
        
        # Simuler la sauvegarde
        QMessageBox.information(self, "Succès", "Tâche sauvegardée avec succès !")
        self.accept()
