#!/usr/bin/env python3
"""
Script de réinitialisation forcée pour SOTRAMINE PHOSPHATE
Ce script supprime complètement toutes les données et recrée une base vide
"""

import os
import shutil
import sqlite3
import sys
from datetime import datetime

def create_backup():
    """Crée une sauvegarde complète avant réinitialisation"""
    print("💾 CRÉATION DE SAUVEGARDE COMPLÈTE")
    print("-" * 40)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"backup_complete_{timestamp}"
    
    try:
        # C<PERSON>er le dossier de sauvegarde
        os.makedirs(backup_dir, exist_ok=True)
        
        # Sauvegarder la base de données
        if os.path.exists('sotramine.db'):
            shutil.copy2('sotramine.db', os.path.join(backup_dir, 'sotramine.db'))
            print(f"✓ Base de données sauvegardée")
        
        # Sauvegarder le dossier export s'il existe
        if os.path.exists('export'):
            shutil.copytree('export', os.path.join(backup_dir, 'export'))
            print(f"✓ Dossier export sauvegardé")
        
        # Sauvegarder les fichiers de config
        config_files = ['config.ini', 'settings.json', 'user_preferences.json']
        for config_file in config_files:
            if os.path.exists(config_file):
                shutil.copy2(config_file, os.path.join(backup_dir, config_file))
                print(f"✓ {config_file} sauvegardé")
        
        print(f"✅ Sauvegarde complète créée dans : {backup_dir}")
        return backup_dir
        
    except Exception as e:
        print(f"❌ Erreur lors de la sauvegarde : {str(e)}")
        return None

def check_current_data():
    """Vérifie les données actuelles dans la base"""
    print("🔍 VÉRIFICATION DES DONNÉES ACTUELLES")
    print("-" * 40)
    
    if not os.path.exists('sotramine.db'):
        print("⚠️ Aucune base de données trouvée")
        return {}
    
    try:
        conn = sqlite3.connect('sotramine.db')
        cursor = conn.cursor()
        
        # Vérifier les tables et compter les données
        tables_data = {}
        
        # Tables principales à vérifier
        tables_to_check = [
            ('tasks', 'Tâches'),
            ('equipment', 'Équipements'),
            ('persons', 'Personnel'),
            ('spare_parts', 'Pièces de rechange'),
            ('attendance', 'Présences')
        ]
        
        for table_name, display_name in tables_to_check:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                tables_data[table_name] = count
                print(f"📊 {display_name} : {count} enregistrement(s)")
            except sqlite3.OperationalError:
                tables_data[table_name] = 0
                print(f"📊 {display_name} : Table non trouvée")
        
        conn.close()
        
        total_records = sum(tables_data.values())
        print(f"\n📈 TOTAL : {total_records} enregistrements dans la base")
        
        return tables_data
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification : {str(e)}")
        return {}

def force_delete_database():
    """Supprime complètement la base de données et tous ses fichiers"""
    print("\n🗑️ SUPPRESSION FORCÉE DE LA BASE DE DONNÉES")
    print("-" * 40)
    
    # Tous les fichiers SQLite possibles
    db_files = [
        'sotramine.db',
        'sotramine.db-journal',
        'sotramine.db-wal',
        'sotramine.db-shm',
        'sotramine.sqlite',
        'sotramine.sqlite3'
    ]
    
    deleted_files = []
    
    for db_file in db_files:
        if os.path.exists(db_file):
            try:
                # Forcer la suppression même si le fichier est verrouillé
                os.chmod(db_file, 0o777)  # Donner tous les droits
                os.remove(db_file)
                deleted_files.append(db_file)
                print(f"✓ Supprimé : {db_file}")
            except Exception as e:
                print(f"❌ Erreur suppression {db_file} : {str(e)}")
                # Essayer de renommer le fichier pour le "casser"
                try:
                    os.rename(db_file, f"{db_file}.deleted")
                    print(f"⚠️ {db_file} renommé en .deleted")
                except:
                    print(f"❌ Impossible de supprimer {db_file}")
        else:
            print(f"⚪ {db_file} : N'existe pas")
    
    return len(deleted_files) > 0

def force_delete_exports():
    """Supprime complètement le dossier export"""
    print("\n📊 SUPPRESSION FORCÉE DES EXPORTS")
    print("-" * 40)
    
    if os.path.exists('export'):
        try:
            # Lister les fichiers avant suppression
            files = []
            for root, dirs, filenames in os.walk('export'):
                for filename in filenames:
                    files.append(os.path.join(root, filename))
            
            if files:
                print(f"📁 {len(files)} fichier(s) trouvé(s) dans export/")
                for file in files[:5]:  # Afficher les 5 premiers
                    print(f"   • {os.path.basename(file)}")
                if len(files) > 5:
                    print(f"   ... et {len(files) - 5} autres")
            
            # Supprimer le dossier complet
            shutil.rmtree('export')
            print(f"✅ Dossier export/ supprimé complètement")
            return True
            
        except Exception as e:
            print(f"❌ Erreur suppression export/ : {str(e)}")
            return False
    else:
        print(f"⚪ Dossier export/ n'existe pas")
        return True

def create_fresh_database():
    """Crée une nouvelle base de données complètement vide"""
    print("\n🆕 CRÉATION D'UNE NOUVELLE BASE VIDE")
    print("-" * 40)
    
    try:
        # S'assurer qu'aucun fichier de base n'existe
        if os.path.exists('sotramine.db'):
            print("⚠️ Un fichier sotramine.db existe encore")
            return False
        
        # Créer une nouvelle base complètement vide
        conn = sqlite3.connect('sotramine.db')
        cursor = conn.cursor()
        
        # Créer juste une table de test pour vérifier
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS app_info (
                key TEXT PRIMARY KEY,
                value TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Insérer une info sur la réinitialisation
        cursor.execute('''
            INSERT INTO app_info (key, value) 
            VALUES ('last_reset', ?)
        ''', (datetime.now().strftime("%Y-%m-%d %H:%M:%S"),))
        
        conn.commit()
        conn.close()
        
        print("✅ Nouvelle base de données créée")
        print("✅ Base complètement vide et prête")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur création nouvelle base : {str(e)}")
        return False

def verify_reset():
    """Vérifie que la réinitialisation a bien fonctionné"""
    print("\n✅ VÉRIFICATION DE LA RÉINITIALISATION")
    print("-" * 40)
    
    success = True
    
    # Vérifier que la nouvelle base existe
    if os.path.exists('sotramine.db'):
        print("✓ Nouvelle base de données présente")
        
        try:
            conn = sqlite3.connect('sotramine.db')
            cursor = conn.cursor()
            
            # Vérifier qu'elle contient seulement notre table de test
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [table[0] for table in cursor.fetchall()]
            
            print(f"📋 Tables dans la nouvelle base : {len(tables)}")
            for table in tables:
                print(f"   • {table}")
            
            # Vérifier l'info de réinitialisation
            try:
                cursor.execute("SELECT value FROM app_info WHERE key='last_reset'")
                reset_time = cursor.fetchone()
                if reset_time:
                    print(f"✓ Réinitialisation confirmée le : {reset_time[0]}")
                else:
                    print("⚠️ Info de réinitialisation non trouvée")
            except:
                print("⚠️ Table app_info non trouvée")
            
            conn.close()
            
        except Exception as e:
            print(f"❌ Erreur vérification base : {str(e)}")
            success = False
    else:
        print("❌ Nouvelle base de données non trouvée")
        success = False
    
    # Vérifier que le dossier export n'existe plus
    if not os.path.exists('export'):
        print("✓ Dossier export supprimé")
    else:
        print("⚠️ Dossier export existe encore")
        success = False
    
    return success

def main():
    """Point d'entrée principal"""
    print("🔄 SOTRAMINE PHOSPHATE - RÉINITIALISATION FORCÉE")
    print("Version 2.1 - Suppression Complète et Recréation")
    print("=" * 60)
    
    print("⚠️ ATTENTION : Cette opération va SUPPRIMER TOUTES LES DONNÉES !")
    print("Une sauvegarde complète sera créée avant la suppression.")
    print()
    
    # Vérifier les données actuelles
    current_data = check_current_data()
    total_records = sum(current_data.values())
    
    if total_records == 0:
        print("✅ La base semble déjà vide")
        response = input("Voulez-vous quand même forcer la réinitialisation ? (o/N) : ").lower()
        if response not in ['o', 'oui', 'y', 'yes']:
            print("❌ Réinitialisation annulée")
            return
    
    # Demander confirmation avec mot de passe
    print(f"\n🔐 CONFIRMATION REQUISE")
    password = input("Entrez le mot de passe administrateur pour continuer (sotramine) : ")
    
    if password != 'sotramine':
        print("❌ Mot de passe incorrect - Réinitialisation annulée")
        return
    
    print(f"\n🚀 DÉMARRAGE DE LA RÉINITIALISATION FORCÉE...")
    
    # Étape 1: Créer une sauvegarde
    backup_dir = create_backup()
    
    # Étape 2: Supprimer la base de données
    db_deleted = force_delete_database()
    
    # Étape 3: Supprimer les exports
    exports_deleted = force_delete_exports()
    
    # Étape 4: Créer une nouvelle base vide
    new_db_created = create_fresh_database()
    
    # Étape 5: Vérifier le résultat
    verification_ok = verify_reset()
    
    # Résumé final
    print(f"\n" + "=" * 60)
    print("📋 RÉSUMÉ DE LA RÉINITIALISATION FORCÉE")
    print("=" * 60)
    
    print(f"💾 Sauvegarde : {'✅ Créée' if backup_dir else '❌ Échec'}")
    if backup_dir:
        print(f"   📁 Dossier : {backup_dir}")
    
    print(f"🗑️ Suppression base : {'✅ Réussie' if db_deleted else '❌ Échec'}")
    print(f"📊 Suppression exports : {'✅ Réussie' if exports_deleted else '❌ Échec'}")
    print(f"🆕 Nouvelle base : {'✅ Créée' if new_db_created else '❌ Échec'}")
    print(f"✅ Vérification : {'✅ OK' if verification_ok else '❌ Problème'}")
    
    if verification_ok:
        print(f"\n🎉 RÉINITIALISATION FORCÉE TERMINÉE AVEC SUCCÈS !")
        print("✅ Toutes les données ont été supprimées")
        print("✅ Une nouvelle base vide a été créée")
        print("🚀 Vous pouvez maintenant relancer l'application")
        
        if backup_dir:
            print(f"\n💾 SAUVEGARDE DISPONIBLE :")
            print(f"   📁 Dossier : {backup_dir}")
            print(f"   🔄 Pour restaurer : copiez sotramine.db depuis ce dossier")
        
        print(f"\n▶️ RELANCER L'APPLICATION :")
        print(f"   python main.py")
        
    else:
        print(f"\n⚠️ RÉINITIALISATION INCOMPLÈTE")
        print("Certains éléments n'ont pas pu être traités correctement")
        print("Vérifiez les erreurs ci-dessus")
        
        if backup_dir:
            print(f"\n💾 Sauvegarde disponible : {backup_dir}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Réinitialisation interrompue par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur critique : {str(e)}")
        import traceback
        traceback.print_exc()
