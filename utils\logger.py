"""
Système de logging optimisé pour l'application SOTRAMINE PHOSPHATE
"""

import logging
import logging.handlers
import os
from datetime import datetime
from pathlib import Path
from config import LOG_CONFIG, DATA_DIR

class OptimizedLogger:
    """Logger optimisé avec rotation des fichiers et formatage personnalisé"""
    
    def __init__(self, name="SOTRAMINE", level=None):
        self.name = name
        self.level = level or LOG_CONFIG['level']
        self.logger = None
        self._setup_logger()
    
    def _setup_logger(self):
        """Configure le logger avec les paramètres optimisés"""
        self.logger = logging.getLogger(self.name)
        self.logger.setLevel(getattr(logging, self.level))
        
        # Éviter la duplication des handlers
        if self.logger.handlers:
            return
        
        # Créer le répertoire de logs s'il n'existe pas
        log_dir = Path(LOG_CONFIG['file_path']).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # Handler pour fichier avec rotation
        file_handler = logging.handlers.RotatingFileHandler(
            LOG_CONFIG['file_path'],
            maxBytes=LOG_CONFIG['max_file_size'],
            backupCount=LOG_CONFIG['backup_count'],
            encoding='utf-8'
        )
        
        # Handler pour console
        console_handler = logging.StreamHandler()
        
        # Format personnalisé
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # Niveau différent pour console (moins verbeux)
        console_handler.setLevel(logging.WARNING)
        
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def debug(self, message, *args, **kwargs):
        """Log de débogage"""
        self.logger.debug(message, *args, **kwargs)
    
    def info(self, message, *args, **kwargs):
        """Log d'information"""
        self.logger.info(message, *args, **kwargs)
    
    def warning(self, message, *args, **kwargs):
        """Log d'avertissement"""
        self.logger.warning(message, *args, **kwargs)
    
    def error(self, message, *args, **kwargs):
        """Log d'erreur"""
        self.logger.error(message, *args, **kwargs)
    
    def critical(self, message, *args, **kwargs):
        """Log critique"""
        self.logger.critical(message, *args, **kwargs)
    
    def exception(self, message, *args, **kwargs):
        """Log d'exception avec traceback"""
        self.logger.exception(message, *args, **kwargs)

class PerformanceLogger:
    """Logger spécialisé pour le monitoring des performances"""
    
    def __init__(self):
        self.logger = OptimizedLogger("PERFORMANCE")
        self.start_times = {}
    
    def start_timer(self, operation_name):
        """Démarre un timer pour une opération"""
        self.start_times[operation_name] = datetime.now()
        self.logger.debug(f"Début de l'opération: {operation_name}")
    
    def end_timer(self, operation_name):
        """Termine un timer et log la durée"""
        if operation_name in self.start_times:
            duration = datetime.now() - self.start_times[operation_name]
            duration_ms = duration.total_seconds() * 1000
            
            # Log avec niveau approprié selon la durée
            if duration_ms > 5000:  # Plus de 5 secondes
                self.logger.warning(f"Opération lente: {operation_name} - {duration_ms:.2f}ms")
            elif duration_ms > 1000:  # Plus d'1 seconde
                self.logger.info(f"Opération: {operation_name} - {duration_ms:.2f}ms")
            else:
                self.logger.debug(f"Opération: {operation_name} - {duration_ms:.2f}ms")
            
            del self.start_times[operation_name]
            return duration_ms
        else:
            self.logger.error(f"Timer non trouvé pour l'opération: {operation_name}")
            return None
    
    def log_memory_usage(self, operation_name=""):
        """Log l'utilisation mémoire actuelle"""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            
            self.logger.info(f"Mémoire utilisée {operation_name}: {memory_mb:.2f} MB")
            return memory_mb
        except ImportError:
            self.logger.warning("psutil non disponible pour le monitoring mémoire")
            return None
    
    def log_database_stats(self, db_cursor):
        """Log les statistiques de la base de données"""
        try:
            # Taille de la base de données
            db_cursor.execute("PRAGMA page_count")
            page_count = db_cursor.fetchone()[0]
            
            db_cursor.execute("PRAGMA page_size")
            page_size = db_cursor.fetchone()[0]
            
            db_size_mb = (page_count * page_size) / 1024 / 1024
            
            # Cache hit ratio
            db_cursor.execute("PRAGMA cache_size")
            cache_size = db_cursor.fetchone()[0]
            
            self.logger.info(f"Base de données - Taille: {db_size_mb:.2f} MB, Cache: {cache_size} pages")
            
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération des stats DB: {str(e)}")

def get_logger(name="SOTRAMINE"):
    """Factory function pour obtenir un logger"""
    return OptimizedLogger(name)

def get_performance_logger():
    """Factory function pour obtenir un logger de performance"""
    return PerformanceLogger()

# Logger global pour l'application
app_logger = get_logger()
perf_logger = get_performance_logger()