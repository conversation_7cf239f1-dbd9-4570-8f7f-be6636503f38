#!/usr/bin/env python3
"""
Test d'import simple
"""

print("🔍 Test d'import simple")
print("=" * 30)

try:
    print("1. Import PyQt5...")
    from PyQt5.QtWidgets import QWidget
    print("✅ PyQt5 OK")
except Exception as e:
    print(f"❌ PyQt5: {e}")
    import traceback
    traceback.print_exc()

try:
    print("2. Import database...")
    from database import Database
    print("✅ Database OK")
except Exception as e:
    print(f"❌ Database: {e}")
    import traceback
    traceback.print_exc()

try:
    print("3. Import utils.data_refresh_manager...")
    from utils.data_refresh_manager import RefreshableWidget
    print("✅ RefreshableWidget OK")
except Exception as e:
    print(f"❌ RefreshableWidget: {e}")
    import traceback
    traceback.print_exc()

try:
    print("4. Import interventions_management...")
    from gui.interventions_management import InterventionsManagement
    print("✅ InterventionsManagement OK")
except Exception as e:
    print(f"❌ InterventionsManagement: {e}")
    import traceback
    traceback.print_exc()

print("\n🎯 Test terminé")
