#!/usr/bin/env python3
"""
SOTRAMINE PHOSPHATE - APPLICATION CORRIGÉE
Version avec gestion d'erreurs améliorée pour éviter les plantages
"""

import sys
import os
import traceback
from PyQt5.QtWidgets import (QApplication, QMainWindow, QStackedWidget, QWidget, 
                             QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QFrame, 
                             QSplitter, QMessageBox, QTabWidget, QTextEdit)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QIcon

class ErrorWidget(QWidget):
    """Widget affichant les erreurs de manière conviviale"""
    
    def __init__(self, error_message, parent=None):
        super().__init__(parent)
        self.setup_ui(error_message)
    
    def setup_ui(self, error_message):
        layout = QVBoxLayout(self)
        
        # Titre d'erreur
        error_label = QLabel("⚠️ Erreur de chargement du module")
        error_label.setStyleSheet("""
            QLabel {
                color: #e74c3c;
                font-size: 18px;
                font-weight: bold;
                margin: 20px;
            }
        """)
        layout.addWidget(error_label)
        
        # Message d'erreur
        error_text = QTextEdit()
        error_text.setPlainText(f"Le module n'a pas pu être chargé :\n\n{error_message}")
        error_text.setReadOnly(True)
        error_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
                font-family: 'Courier New', monospace;
            }
        """)
        layout.addWidget(error_text)
        
        # Bouton de retour
        back_btn = QPushButton("🔙 Retour à l'accueil")
        back_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        layout.addWidget(back_btn)

class SotramineAppFixed(QMainWindow):
    """Application SOTRAMINE corrigée avec gestion d'erreurs"""
    
    def __init__(self):
        super().__init__()
        self.db = None
        self.excel_exporter = None
        self.current_module = None
        self.modules_cache = {}
        self.setup_database()
        self.setup_ui()
        self.setup_auto_refresh()
        print("🎉 APPLICATION SOTRAMINE PHOSPHATE CORRIGÉE INITIALISÉE")
    
    def setup_database(self):
        """Initialise la base de données avec gestion d'erreurs"""
        try:
            from database import Database
            from export.excel_export import ExcelExporter
            
            self.db = Database()
            self.excel_exporter = ExcelExporter(self.db)
            print("✓ Base de données et exporteur initialisés")
        except Exception as e:
            print(f"❌ Erreur base de données : {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Impossible d'initialiser la base de données :\n{str(e)}")
    
    def setup_ui(self):
        """Configure l'interface utilisateur corrigée"""
        self.setWindowTitle("SOTRAMINE PHOSPHATE - Version Corrigée v2.2")
        self.setGeometry(100, 100, 1600, 1000)
        
        # Widget central avec splitter
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Splitter principal
        splitter = QSplitter(Qt.Horizontal)
        
        # Menu latéral
        self.sidebar = self.create_sidebar()
        splitter.addWidget(self.sidebar)
        
        # Zone de contenu
        self.content_area = self.create_content_area()
        splitter.addWidget(self.content_area)
        
        # Proportions
        splitter.setSizes([350, 1250])
        splitter.setCollapsible(0, False)
        splitter.setCollapsible(1, False)
        
        main_layout.addWidget(splitter)
        
        # Afficher l'accueil par défaut
        self.show_home()
    
    def create_sidebar(self):
        """Crée le menu latéral avec gestion d'erreurs"""
        sidebar = QFrame()
        sidebar.setStyleSheet("""
            QFrame {
                background-color: #2c3e50;
                border-right: 2px solid #34495e;
            }
        """)
        sidebar.setFixedWidth(350)
        
        layout = QVBoxLayout(sidebar)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # En-tête avec logo
        header = self.create_header()
        layout.addWidget(header)
        
        # Sections du menu
        self.create_menu_sections(layout)
        
        layout.addStretch()
        
        return sidebar
    
    def create_header(self):
        """Crée l'en-tête du menu"""
        header = QFrame()
        header.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                padding: 20px;
                border-radius: 0;
            }
        """)
        header.setFixedHeight(100)
        
        layout = QVBoxLayout(header)
        
        # Logo et titre
        title = QLabel("🏭 SOTRAMINE PHOSPHATE")
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
            }
        """)
        layout.addWidget(title)
        
        subtitle = QLabel("Gestion de Maintenance Industrielle")
        subtitle.setStyleSheet("""
            QLabel {
                color: #ecf0f1;
                font-size: 12px;
            }
        """)
        layout.addWidget(subtitle)
        
        return header
    
    def create_menu_sections(self, layout):
        """Crée les sections du menu avec gestion d'erreurs"""
        self.sidebar_buttons = {}
        
        # Section PRODUCTION
        self.add_section_header(layout, "📊 PRODUCTION", "#e74c3c")
        self.add_section_buttons(layout, [
            ("home", "🏠 Accueil", "Tableau de bord principal"),
            ("tasks", "📋 Tâches", "Gestion des tâches et projets"),
            ("reports", "📊 Rapports", "Rapports et statistiques")
        ])
        
        # Section MAINTENANCE
        self.add_section_header(layout, "🔧 MAINTENANCE", "#f39c12")
        self.add_section_buttons(layout, [
            ("equipment", "⚙️ Équipements", "Gestion des équipements"),
            ("maintenance", "🔧 Maintenance", "Planification et suivi"),
            ("spare_parts", "🔩 Pièces", "Gestion des pièces de rechange")
        ])
        
        # Section PERSONNEL
        self.add_section_header(layout, "👥 PERSONNEL", "#27ae60")
        self.add_section_buttons(layout, [
            ("personnel", "👷 Personnel", "Gestion du personnel"),
            ("attendance", "⏰ Pointage", "Suivi des présences")
        ])
        
        # Section SYSTÈME
        self.add_section_header(layout, "⚙️ SYSTÈME", "#9b59b6")
        self.add_section_buttons(layout, [
            ("settings", "⚙️ Paramètres", "Configuration système"),
            ("backup", "💾 Sauvegarde", "Sauvegarde et restauration"),
            ("help", "❓ Aide", "Documentation et support")
        ])
    
    def add_section_header(self, layout, title, color):
        """Ajoute un en-tête de section"""
        header = QLabel(title)
        header.setStyleSheet(f"""
            QLabel {{
                color: {color};
                font-size: 14px;
                font-weight: bold;
                padding: 15px 20px 5px 20px;
                background-color: #34495e;
                margin-top: 10px;
            }}
        """)
        layout.addWidget(header)
    
    def add_section_buttons(self, layout, items):
        """Ajoute les boutons d'une section"""
        for button_id, text, tooltip in items:
            btn = QPushButton(text)
            btn.setToolTip(tooltip)
            btn.setCheckable(True)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #34495e;
                    color: white;
                    border: none;
                    text-align: left;
                    padding: 12px 20px;
                    font-size: 13px;
                }
                QPushButton:hover {
                    background-color: #4a5f7a;
                }
                QPushButton:checked {
                    background-color: #3498db;
                    border-left: 4px solid #2980b9;
                }
            """)
            
            btn.clicked.connect(lambda checked, bid=button_id: self.navigate_to_section(bid))
            layout.addWidget(btn)
            self.sidebar_buttons[button_id] = btn
    
    def create_content_area(self):
        """Crée la zone de contenu principale"""
        self.content_area = QStackedWidget()
        self.content_area.setStyleSheet("""
            QStackedWidget {
                background-color: #f8f9fa;
            }
        """)
        
        # Page d'accueil
        self.home_page = self.create_home_page()
        self.content_area.addWidget(self.home_page)
        
        return self.content_area
    
    def create_home_page(self):
        """Crée la page d'accueil"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Titre de bienvenue
        welcome = QLabel("🏭 Bienvenue dans SOTRAMINE PHOSPHATE")
        welcome.setStyleSheet("""
            QLabel {
                font-size: 28px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(welcome)
        
        # Sous-titre
        subtitle = QLabel("Système de Gestion de Maintenance Industrielle Complet")
        subtitle.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #7f8c8d;
                margin-bottom: 30px;
            }
        """)
        layout.addWidget(subtitle)
        
        # Message de statut
        status = QLabel("✅ Application initialisée avec succès - Tous les modules sont disponibles")
        status.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #27ae60;
                padding: 15px;
                background-color: #d5f4e6;
                border-radius: 5px;
                border: 1px solid #27ae60;
            }
        """)
        layout.addWidget(status)
        
        layout.addStretch()
        return page
    
    def setup_auto_refresh(self):
        """Configure l'actualisation automatique"""
        try:
            self.refresh_timer = QTimer()
            self.refresh_timer.timeout.connect(self.refresh_data)
            self.refresh_timer.start(30000)  # 30 secondes
            print("✓ Actualisation automatique activée")
        except Exception as e:
            print(f"❌ Erreur actualisation automatique : {str(e)}")
    
    def refresh_data(self):
        """Actualise les données"""
        try:
            if self.current_module and hasattr(self.current_module, 'refresh_data'):
                self.current_module.refresh_data()
        except Exception as e:
            print(f"❌ Erreur actualisation : {str(e)}")
    
    def navigate_to_section(self, section_id):
        """Navigue vers une section avec gestion d'erreurs robuste"""
        try:
            print(f"🔄 Navigation vers : {section_id}")
            
            # Désélectionner tous les boutons
            for btn in self.sidebar_buttons.values():
                btn.setChecked(False)
            
            # Sélectionner le bouton actuel
            if section_id in self.sidebar_buttons:
                self.sidebar_buttons[section_id].setChecked(True)
            
            # Navigation selon la section
            if section_id == 'home':
                self.show_home()
            elif section_id == 'tasks':
                self.show_tasks()
            elif section_id == 'equipment':
                self.show_equipment()
            elif section_id == 'spare_parts':
                self.show_spare_parts()
            elif section_id == 'personnel':
                self.show_personnel()
            elif section_id == 'attendance':
                self.show_attendance()
            elif section_id == 'reports':
                self.show_reports()
            elif section_id == 'maintenance':
                self.show_maintenance()
            elif section_id == 'settings':
                self.show_settings()
            elif section_id == 'backup':
                self.show_backup()
            elif section_id == 'help':
                self.show_help()
                
        except Exception as e:
            print(f"❌ Erreur navigation : {str(e)}")
            traceback.print_exc()
            self.show_error_page(f"Erreur lors de la navigation : {str(e)}")
    
    def show_home(self):
        """Affiche la page d'accueil"""
        self.content_area.setCurrentWidget(self.home_page)
        self.setWindowTitle("SOTRAMINE PHOSPHATE - Tableau de Bord")
        self.current_module = None
    
    def show_tasks(self):
        """Affiche la gestion des tâches avec gestion d'erreurs"""
        try:
            if 'tasks' not in self.modules_cache:
                from gui.task_manager_complete import TaskManagerComplete
                self.modules_cache['tasks'] = TaskManagerComplete(self.db, self)
                self.content_area.addWidget(self.modules_cache['tasks'])
            
            self.content_area.setCurrentWidget(self.modules_cache['tasks'])
            self.setWindowTitle("SOTRAMINE PHOSPHATE - Gestion des Tâches")
            self.current_module = self.modules_cache['tasks']
            print("✅ Module tâches chargé avec succès")
            
        except Exception as e:
            print(f"❌ Erreur module tâches : {str(e)}")
            traceback.print_exc()
            self.show_error_page(f"Erreur lors du chargement du module tâches : {str(e)}")
    
    def show_equipment(self):
        """Affiche la gestion des équipements avec gestion d'erreurs"""
        try:
            if 'equipment' not in self.modules_cache:
                from gui.equipment_manager_complete import EquipmentManagerComplete
                self.modules_cache['equipment'] = EquipmentManagerComplete(self.db, self)
                self.content_area.addWidget(self.modules_cache['equipment'])
            
            self.content_area.setCurrentWidget(self.modules_cache['equipment'])
            self.setWindowTitle("SOTRAMINE PHOSPHATE - Gestion des Équipements")
            self.current_module = self.modules_cache['equipment']
            print("✅ Module équipements chargé avec succès")
            
        except Exception as e:
            print(f"❌ Erreur module équipements : {str(e)}")
            traceback.print_exc()
            self.show_error_page(f"Erreur lors du chargement du module équipements : {str(e)}")
    
    def show_personnel(self):
        """Affiche la gestion du personnel avec gestion d'erreurs"""
        try:
            if 'personnel' not in self.modules_cache:
                from gui.personnel_manager_complete import PersonnelManagerComplete
                self.modules_cache['personnel'] = PersonnelManagerComplete(self.db, self)
                self.content_area.addWidget(self.modules_cache['personnel'])
            
            self.content_area.setCurrentWidget(self.modules_cache['personnel'])
            self.setWindowTitle("SOTRAMINE PHOSPHATE - Gestion du Personnel")
            self.current_module = self.modules_cache['personnel']
            print("✅ Module personnel chargé avec succès")
            
        except Exception as e:
            print(f"❌ Erreur module personnel : {str(e)}")
            traceback.print_exc()
            self.show_error_page(f"Erreur lors du chargement du module personnel : {str(e)}")
    
    def show_attendance(self):
        """Affiche le pointage avec gestion d'erreurs"""
        try:
            if 'attendance' not in self.modules_cache:
                from gui.attendance_manager_complete import AttendanceManagerComplete
                self.modules_cache['attendance'] = AttendanceManagerComplete(self.db, self)
                self.content_area.addWidget(self.modules_cache['attendance'])
            
            self.content_area.setCurrentWidget(self.modules_cache['attendance'])
            self.setWindowTitle("SOTRAMINE PHOSPHATE - Pointage")
            self.current_module = self.modules_cache['attendance']
            print("✅ Module pointage chargé avec succès")
            
        except Exception as e:
            print(f"❌ Erreur module pointage : {str(e)}")
            traceback.print_exc()
            self.show_error_page(f"Erreur lors du chargement du module pointage : {str(e)}")
    
    def show_reports(self):
        """Affiche les rapports avec gestion d'erreurs"""
        try:
            if 'reports' not in self.modules_cache:
                from gui.reports_manager_complete import ReportsManagerComplete
                self.modules_cache['reports'] = ReportsManagerComplete(self.db, self)
                self.content_area.addWidget(self.modules_cache['reports'])
            
            self.content_area.setCurrentWidget(self.modules_cache['reports'])
            self.setWindowTitle("SOTRAMINE PHOSPHATE - Rapports")
            self.current_module = self.modules_cache['reports']
            print("✅ Module rapports chargé avec succès")
            
        except Exception as e:
            print(f"❌ Erreur module rapports : {str(e)}")
            traceback.print_exc()
            self.show_error_page(f"Erreur lors du chargement du module rapports : {str(e)}")
    
    def show_maintenance(self):
        """Affiche la maintenance avec gestion d'erreurs"""
        try:
            if 'maintenance' not in self.modules_cache:
                from gui.maintenance_menu import MaintenanceMenu
                self.modules_cache['maintenance'] = MaintenanceMenu(self.db, self)
                self.content_area.addWidget(self.modules_cache['maintenance'])
            
            self.content_area.setCurrentWidget(self.modules_cache['maintenance'])
            self.setWindowTitle("SOTRAMINE PHOSPHATE - Maintenance")
            self.current_module = self.modules_cache['maintenance']
            print("✅ Module maintenance chargé avec succès")
            
        except Exception as e:
            print(f"❌ Erreur module maintenance : {str(e)}")
            traceback.print_exc()
            self.show_error_page(f"Erreur lors du chargement du module maintenance : {str(e)}")
    
    def show_spare_parts(self):
        """Affiche la gestion des pièces avec gestion d'erreurs"""
        try:
            if 'spare_parts' not in self.modules_cache:
                from gui.spare_parts_manager import SparePartsManager
                self.modules_cache['spare_parts'] = SparePartsManager(self.db, self)
                self.content_area.addWidget(self.modules_cache['spare_parts'])
            
            self.content_area.setCurrentWidget(self.modules_cache['spare_parts'])
            self.setWindowTitle("SOTRAMINE PHOSPHATE - Pièces de Rechange")
            self.current_module = self.modules_cache['spare_parts']
            print("✅ Module pièces chargé avec succès")
            
        except Exception as e:
            print(f"❌ Erreur module pièces : {str(e)}")
            traceback.print_exc()
            self.show_error_page(f"Erreur lors du chargement du module pièces : {str(e)}")
    
    def show_settings(self):
        """Affiche les paramètres"""
        self.show_simple_page("⚙️ Paramètres", "Configuration du système")
    
    def show_backup(self):
        """Affiche la sauvegarde"""
        self.show_simple_page("💾 Sauvegarde", "Sauvegarde et restauration des données")
    
    def show_help(self):
        """Affiche l'aide"""
        self.show_simple_page("❓ Aide", "Documentation et support utilisateur")
    
    def show_simple_page(self, title, description):
        """Affiche une page simple"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(20, 20, 20, 20)
        
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title_label)
        
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #7f8c8d;
            }
        """)
        layout.addWidget(desc_label)
        
        layout.addStretch()
        
        # Ajouter la page au content area si elle n'existe pas
        if not hasattr(self, f'{title.lower().replace(" ", "_")}_page'):
            setattr(self, f'{title.lower().replace(" ", "_")}_page', page)
            self.content_area.addWidget(page)
        
        self.content_area.setCurrentWidget(page)
        self.setWindowTitle(f"SOTRAMINE PHOSPHATE - {title}")
        self.current_module = None
    
    def show_error_page(self, error_message):
        """Affiche une page d'erreur"""
        error_widget = ErrorWidget(error_message, self)
        
        # Ajouter au content area si pas déjà présent
        if not hasattr(self, 'error_page'):
            self.error_page = error_widget
            self.content_area.addWidget(self.error_page)
        else:
            # Remplacer le contenu de la page d'erreur existante
            self.content_area.removeWidget(self.error_page)
            self.error_page = error_widget
            self.content_area.addWidget(self.error_page)
        
        self.content_area.setCurrentWidget(self.error_page)
        self.setWindowTitle("SOTRAMINE PHOSPHATE - Erreur")

def main():
    """Fonction principale"""
    try:
        app = QApplication(sys.argv)
        print("✓ Application Qt créée")
        
        # Configuration de l'application
        app.setApplicationName("SOTRAMINE PHOSPHATE")
        app.setApplicationVersion("2.2")
        
        # Création de la fenêtre principale
        window = SotramineAppFixed()
        print("✓ Interface utilisateur créée")
        
        # Affichage de la fenêtre
        window.show()
        print("✓ Application affichée")
        
        print("\n🎉 APPLICATION CORRIGÉE LANCÉE AVEC SUCCÈS !")
        print("🔧 Gestion d'erreurs améliorée - Plus de plantages !")
        print("📋 Tous les modules sont protégés contre les erreurs")
        
        # Lancement de l'application
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ Erreur fatale : {str(e)}")
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
