#!/usr/bin/env python3
"""
LANCEUR POUR SOTRAMINE PHOSPHATE VERSION SIMPLIFIÉE
Version corrigée sans erreurs d'affichage
"""

import sys
import os

def main():
    """Lance l'application SOTRAMINE PHOSPHATE version simplifiée"""
    print("🚀 LANCEMENT SOTRAMINE PHOSPHATE - VERSION SIMPLIFIÉE CORRIGÉE")
    print("=" * 70)
    print("✅ Version sans erreurs d'affichage")
    print("🎨 Interface simplifiée et fonctionnelle")
    print("🔧 Navigation fluide entre modules")
    print("=" * 70)
    
    try:
        # Importer et lancer l'application simplifiée
        from sotramine_simple_working import main as app_main
        app_main()
        
    except ImportError as e:
        print(f"❌ Erreur d'import : {str(e)}")
        print("🔧 Vérifiez que le fichier sotramine_simple_working.py existe")
        
    except Exception as e:
        print(f"❌ Erreur lors du lancement : {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
