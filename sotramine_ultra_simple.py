#!/usr/bin/env python3
"""
SOTRAMINE PHOSPHATE - VERSION ULTRA-SIMPLE
Application de base sans modules complexes pour éviter les plantages
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QStackedWidget, QWidget, 
                             QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QFrame, 
                             QSplitter, QMessageBox, QTabWidget, QTextEdit, QTableWidget,
                             QTableWidgetItem, QHeaderView)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont

class SimpleModule(QWidget):
    """Module simple de base"""
    
    def __init__(self, title, description, parent=None):
        super().__init__(parent)
        self.title = title
        self.description = description
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Titre
        title_label = QLabel(self.title)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title_label)
        
        # Description
        desc_label = QLabel(self.description)
        desc_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #7f8c8d;
                margin-bottom: 30px;
            }
        """)
        layout.addWidget(desc_label)
        
        # Contenu spécifique
        self.create_content(layout)
        
        layout.addStretch()
    
    def create_content(self, layout):
        """Crée le contenu spécifique du module"""
        # Par défaut, affiche un message
        content = QLabel("Module en cours de développement")
        content.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #95a5a6;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 5px;
                border: 1px solid #bdc3c7;
            }
        """)
        layout.addWidget(content)

class TasksModule(SimpleModule):
    """Module de gestion des tâches simplifié"""
    
    def __init__(self, parent=None):
        super().__init__("📋 Gestion des Tâches", "Gestion simplifiée des tâches et projets", parent)
    
    def create_content(self, layout):
        # Tableau des tâches
        table = QTableWidget()
        table.setColumnCount(4)
        table.setHorizontalHeaderLabels(["ID", "Tâche", "Statut", "Priorité"])
        
        # Données d'exemple
        table.setRowCount(3)
        table.setItem(0, 0, QTableWidgetItem("1"))
        table.setItem(0, 1, QTableWidgetItem("Maintenance préventive"))
        table.setItem(0, 2, QTableWidgetItem("En cours"))
        table.setItem(0, 3, QTableWidgetItem("Haute"))
        
        table.setItem(1, 0, QTableWidgetItem("2"))
        table.setItem(1, 1, QTableWidgetItem("Réparation équipement"))
        table.setItem(1, 2, QTableWidgetItem("En attente"))
        table.setItem(1, 3, QTableWidgetItem("Moyenne"))
        
        table.setItem(2, 0, QTableWidgetItem("3"))
        table.setItem(2, 1, QTableWidgetItem("Inspection sécurité"))
        table.setItem(2, 2, QTableWidgetItem("Terminée"))
        table.setItem(2, 3, QTableWidgetItem("Basse"))
        
        # Ajuster les colonnes
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        
        layout.addWidget(table)

class EquipmentModule(SimpleModule):
    """Module de gestion des équipements simplifié"""
    
    def __init__(self, parent=None):
        super().__init__("⚙️ Gestion des Équipements", "Gestion simplifiée des équipements industriels", parent)
    
    def create_content(self, layout):
        # Tableau des équipements
        table = QTableWidget()
        table.setColumnCount(4)
        table.setHorizontalHeaderLabels(["ID", "Équipement", "Localisation", "État"])
        
        # Données d'exemple
        table.setRowCount(3)
        table.setItem(0, 0, QTableWidgetItem("E001"))
        table.setItem(0, 1, QTableWidgetItem("Pompe centrifuge"))
        table.setItem(0, 2, QTableWidgetItem("Zone A"))
        table.setItem(0, 3, QTableWidgetItem("Opérationnel"))
        
        table.setItem(1, 0, QTableWidgetItem("E002"))
        table.setItem(1, 1, QTableWidgetItem("Compresseur"))
        table.setItem(1, 2, QTableWidgetItem("Zone B"))
        table.setItem(1, 3, QTableWidgetItem("Maintenance"))
        
        table.setItem(2, 0, QTableWidgetItem("E003"))
        table.setItem(2, 1, QTableWidgetItem("Convoyeur"))
        table.setItem(2, 2, QTableWidgetItem("Zone C"))
        table.setItem(2, 3, QTableWidgetItem("Opérationnel"))
        
        # Ajuster les colonnes
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        
        layout.addWidget(table)

class PersonnelModule(SimpleModule):
    """Module de gestion du personnel simplifié"""
    
    def __init__(self, parent=None):
        super().__init__("👥 Gestion du Personnel", "Gestion simplifiée du personnel et des équipes", parent)
    
    def create_content(self, layout):
        # Tableau du personnel
        table = QTableWidget()
        table.setColumnCount(4)
        table.setHorizontalHeaderLabels(["ID", "Nom", "Fonction", "Équipe"])
        
        # Données d'exemple
        table.setRowCount(3)
        table.setItem(0, 0, QTableWidgetItem("P001"))
        table.setItem(0, 1, QTableWidgetItem("Jean Dupont"))
        table.setItem(0, 2, QTableWidgetItem("Technicien"))
        table.setItem(0, 3, QTableWidgetItem("Maintenance"))
        
        table.setItem(1, 0, QTableWidgetItem("P002"))
        table.setItem(1, 1, QTableWidgetItem("Marie Martin"))
        table.setItem(1, 2, QTableWidgetItem("Ingénieur"))
        table.setItem(1, 3, QTableWidgetItem("Production"))
        
        table.setItem(2, 0, QTableWidgetItem("P003"))
        table.setItem(2, 1, QTableWidgetItem("Pierre Durand"))
        table.setItem(2, 2, QTableWidgetItem("Chef d'équipe"))
        table.setItem(2, 3, QTableWidgetItem("Maintenance"))
        
        # Ajuster les colonnes
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QTableWidget.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        
        layout.addWidget(table)

class SotramineUltraSimple(QMainWindow):
    """Application SOTRAMINE ultra-simplifiée"""
    
    def __init__(self):
        super().__init__()
        self.modules_cache = {}
        self.setup_ui()
        print("🎉 APPLICATION SOTRAMINE ULTRA-SIMPLE INITIALISÉE")
    
    def setup_ui(self):
        """Configure l'interface utilisateur ultra-simple"""
        self.setWindowTitle("SOTRAMINE PHOSPHATE - Version Ultra-Simple v3.0")
        self.setGeometry(100, 100, 1600, 1000)
        
        # Widget central avec splitter
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Splitter principal
        splitter = QSplitter(Qt.Horizontal)
        
        # Menu latéral
        self.sidebar = self.create_sidebar()
        splitter.addWidget(self.sidebar)
        
        # Zone de contenu
        self.content_area = self.create_content_area()
        splitter.addWidget(self.content_area)
        
        # Proportions
        splitter.setSizes([350, 1250])
        splitter.setCollapsible(0, False)
        splitter.setCollapsible(1, False)
        
        main_layout.addWidget(splitter)
        
        # Afficher l'accueil par défaut
        self.show_home()
    
    def create_sidebar(self):
        """Crée le menu latéral ultra-simple"""
        sidebar = QFrame()
        sidebar.setStyleSheet("""
            QFrame {
                background-color: #2c3e50;
                border-right: 2px solid #34495e;
            }
        """)
        sidebar.setFixedWidth(350)
        
        layout = QVBoxLayout(sidebar)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # En-tête
        header = self.create_header()
        layout.addWidget(header)
        
        # Boutons du menu
        self.create_menu_buttons(layout)
        
        layout.addStretch()
        
        return sidebar
    
    def create_header(self):
        """Crée l'en-tête du menu"""
        header = QFrame()
        header.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                padding: 20px;
                border-radius: 0;
            }
        """)
        header.setFixedHeight(100)
        
        layout = QVBoxLayout(header)
        
        # Logo et titre
        title = QLabel("🏭 SOTRAMINE PHOSPHATE")
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 18px;
                font-weight: bold;
            }
        """)
        layout.addWidget(title)
        
        subtitle = QLabel("Version Ultra-Simple - Sans Plantages")
        subtitle.setStyleSheet("""
            QLabel {
                color: #ecf0f1;
                font-size: 12px;
            }
        """)
        layout.addWidget(subtitle)
        
        return header
    
    def create_menu_buttons(self, layout):
        """Crée les boutons du menu"""
        self.sidebar_buttons = {}
        
        menu_items = [
            ("home", "🏠 Accueil", "Tableau de bord principal"),
            ("tasks", "📋 Tâches", "Gestion des tâches simplifiée"),
            ("equipment", "⚙️ Équipements", "Gestion des équipements simplifiée"),
            ("personnel", "👥 Personnel", "Gestion du personnel simplifiée"),
            ("maintenance", "🔧 Maintenance", "Planification maintenance"),
            ("reports", "📊 Rapports", "Rapports et statistiques"),
            ("settings", "⚙️ Paramètres", "Configuration système")
        ]
        
        for button_id, text, tooltip in menu_items:
            btn = QPushButton(text)
            btn.setToolTip(tooltip)
            btn.setCheckable(True)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #34495e;
                    color: white;
                    border: none;
                    text-align: left;
                    padding: 15px 20px;
                    font-size: 14px;
                    margin: 2px 10px;
                    border-radius: 5px;
                }
                QPushButton:hover {
                    background-color: #4a5f7a;
                }
                QPushButton:checked {
                    background-color: #3498db;
                    border-left: 4px solid #2980b9;
                }
            """)
            
            btn.clicked.connect(lambda checked, bid=button_id: self.navigate_to_section(bid))
            layout.addWidget(btn)
            self.sidebar_buttons[button_id] = btn
    
    def create_content_area(self):
        """Crée la zone de contenu principale"""
        self.content_area = QStackedWidget()
        self.content_area.setStyleSheet("""
            QStackedWidget {
                background-color: #f8f9fa;
            }
        """)
        
        # Page d'accueil
        self.home_page = self.create_home_page()
        self.content_area.addWidget(self.home_page)
        
        return self.content_area
    
    def create_home_page(self):
        """Crée la page d'accueil"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Titre de bienvenue
        welcome = QLabel("🏭 Bienvenue dans SOTRAMINE PHOSPHATE")
        welcome.setStyleSheet("""
            QLabel {
                font-size: 28px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(welcome)
        
        # Sous-titre
        subtitle = QLabel("Version Ultra-Simple - Système de Gestion de Maintenance Industrielle")
        subtitle.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #7f8c8d;
                margin-bottom: 30px;
            }
        """)
        layout.addWidget(subtitle)
        
        # Message de statut
        status = QLabel("✅ Application ultra-simplifiée - Aucun plantage possible !")
        status.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #27ae60;
                padding: 15px;
                background-color: #d5f4e6;
                border-radius: 5px;
                border: 1px solid #27ae60;
            }
        """)
        layout.addWidget(status)
        
        # Informations sur les modules
        info = QLabel("📋 Modules disponibles : Tâches, Équipements, Personnel, Maintenance, Rapports")
        info.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #3498db;
                padding: 15px;
                background-color: #ebf3fd;
                border-radius: 5px;
                border: 1px solid #3498db;
                margin-top: 20px;
            }
        """)
        layout.addWidget(info)
        
        layout.addStretch()
        return page
    
    def navigate_to_section(self, section_id):
        """Navigue vers une section"""
        try:
            print(f"🔄 Navigation vers : {section_id}")
            
            # Désélectionner tous les boutons
            for btn in self.sidebar_buttons.values():
                btn.setChecked(False)
            
            # Sélectionner le bouton actuel
            if section_id in self.sidebar_buttons:
                self.sidebar_buttons[section_id].setChecked(True)
            
            # Navigation selon la section
            if section_id == 'home':
                self.show_home()
            elif section_id == 'tasks':
                self.show_tasks()
            elif section_id == 'equipment':
                self.show_equipment()
            elif section_id == 'personnel':
                self.show_personnel()
            elif section_id == 'maintenance':
                self.show_maintenance()
            elif section_id == 'reports':
                self.show_reports()
            elif section_id == 'settings':
                self.show_settings()
                
        except Exception as e:
            print(f"❌ Erreur navigation : {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la navigation : {str(e)}")
    
    def show_home(self):
        """Affiche la page d'accueil"""
        self.content_area.setCurrentWidget(self.home_page)
        self.setWindowTitle("SOTRAMINE PHOSPHATE - Tableau de Bord")
    
    def show_tasks(self):
        """Affiche la gestion des tâches"""
        if 'tasks' not in self.modules_cache:
            self.modules_cache['tasks'] = TasksModule(self)
            self.content_area.addWidget(self.modules_cache['tasks'])
        
        self.content_area.setCurrentWidget(self.modules_cache['tasks'])
        self.setWindowTitle("SOTRAMINE PHOSPHATE - Gestion des Tâches")
        print("✅ Module tâches affiché")
    
    def show_equipment(self):
        """Affiche la gestion des équipements"""
        if 'equipment' not in self.modules_cache:
            self.modules_cache['equipment'] = EquipmentModule(self)
            self.content_area.addWidget(self.modules_cache['equipment'])
        
        self.content_area.setCurrentWidget(self.modules_cache['equipment'])
        self.setWindowTitle("SOTRAMINE PHOSPHATE - Gestion des Équipements")
        print("✅ Module équipements affiché")
    
    def show_personnel(self):
        """Affiche la gestion du personnel"""
        if 'personnel' not in self.modules_cache:
            self.modules_cache['personnel'] = PersonnelModule(self)
            self.content_area.addWidget(self.modules_cache['personnel'])
        
        self.content_area.setCurrentWidget(self.modules_cache['personnel'])
        self.setWindowTitle("SOTRAMINE PHOSPHATE - Gestion du Personnel")
        print("✅ Module personnel affiché")
    
    def show_maintenance(self):
        """Affiche la maintenance"""
        self.show_simple_page("🔧 Maintenance", "Planification et suivi de la maintenance préventive")
    
    def show_reports(self):
        """Affiche les rapports"""
        self.show_simple_page("📊 Rapports", "Génération de rapports et statistiques")
    
    def show_settings(self):
        """Affiche les paramètres"""
        self.show_simple_page("⚙️ Paramètres", "Configuration du système et des préférences")
    
    def show_simple_page(self, title, description):
        """Affiche une page simple"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(20, 20, 20, 20)
        
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title_label)
        
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #7f8c8d;
                margin-bottom: 30px;
            }
        """)
        layout.addWidget(desc_label)
        
        # Contenu spécifique
        content = QLabel("Module en cours de développement - Version ultra-simple")
        content.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #95a5a6;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 5px;
                border: 1px solid #bdc3c7;
            }
        """)
        layout.addWidget(content)
        
        layout.addStretch()
        
        # Ajouter la page au content area
        self.content_area.addWidget(page)
        self.content_area.setCurrentWidget(page)
        self.setWindowTitle(f"SOTRAMINE PHOSPHATE - {title}")

def main():
    """Fonction principale"""
    try:
        app = QApplication(sys.argv)
        print("✓ Application Qt créée")
        
        # Configuration de l'application
        app.setApplicationName("SOTRAMINE PHOSPHATE")
        app.setApplicationVersion("3.0 Ultra-Simple")
        
        # Création de la fenêtre principale
        window = SotramineUltraSimple()
        print("✓ Interface utilisateur créée")
        
        # Affichage de la fenêtre
        window.show()
        print("✓ Application affichée")
        
        print("\n🎉 APPLICATION ULTRA-SIMPLE LANCÉE AVEC SUCCÈS !")
        print("🚀 Version ultra-simplifiée - Aucun plantage possible !")
        print("📋 Modules de base fonctionnels et stables")
        
        # Lancement de l'application
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ Erreur fatale : {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
