#!/usr/bin/env python3
"""
Test de débogage pour l'export d'équipements
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_export_direct():
    """Test direct de l'export d'équipements"""
    print("🎯 TEST DIRECT D'EXPORT D'ÉQUIPEMENTS")
    
    try:
        app = QApplication(sys.argv)
        from database import Database
        from export.excel_export import ExcelExporter
        from main import OptimizedSotramineApp
        
        db = Database()
        excel_exporter = ExcelExporter(db)
        main_app = OptimizedSotramineApp(db, excel_exporter)
        
        print("✓ Application créée")
        
        # Test 1: Export direct via ExcelExporter
        print("\n1. Test export direct via ExcelExporter:")
        try:
            filename = excel_exporter.export_equipment_to_excel()
            print(f"✓ Export direct réussi : {filename}")
        except Exception as e:
            print(f"❌ Erreur export direct : {str(e)}")
        
        # Test 2: Export via export_current_section avec section equipment
        print("\n2. Test export via export_current_section (section equipment):")
        try:
            main_app.current_section = 'equipment'
            main_app.export_current_section()
            print("✓ Export via export_current_section réussi")
        except Exception as e:
            print(f"❌ Erreur export via export_current_section : {str(e)}")
        
        # Test 3: Export via handle_action
        print("\n3. Test export via handle_action:")
        try:
            main_app.handle_action('export_equipment')
            print("✓ Export via handle_action réussi")
        except Exception as e:
            print(f"❌ Erreur export via handle_action : {str(e)}")
        
        # Test 4: Export via handle_toolbar_action
        print("\n4. Test export via handle_toolbar_action:")
        try:
            main_app.handle_toolbar_action('export_equipment')
            print("✓ Export via handle_toolbar_action réussi")
        except Exception as e:
            print(f"❌ Erreur export via handle_toolbar_action : {str(e)}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur générale : {str(e)}")
        return False

def test_action_handlers():
    """Test des gestionnaires d'actions"""
    print("\n🔧 TEST DES GESTIONNAIRES D'ACTIONS")
    
    try:
        app = QApplication(sys.argv)
        from database import Database
        from export.excel_export import ExcelExporter
        from main import OptimizedSotramineApp
        
        db = Database()
        excel_exporter = ExcelExporter(db)
        main_app = OptimizedSotramineApp(db, excel_exporter)
        
        # Vérifier les actions disponibles
        print("Actions disponibles dans handle_action:")
        action_handlers = {
            'home': lambda: main_app.navigate_to_section('home'),
            'search': main_app.show_search_dialog,
            'refresh': main_app.refresh_current_section,
            'new_task': main_app.create_new_task,
            'new_equipment': main_app.create_new_equipment,
            'new_person': main_app.create_new_person,
            'new_spare_part': main_app.create_new_spare_part,
            'schedule_maintenance': main_app.schedule_maintenance,
            'export': main_app.export_current_section,
            'export_equipment': main_app.export_current_section,
            'import': main_app.import_to_current_section,
            'import_equipment': main_app.import_to_current_section,
            'settings': main_app.open_configuration,
            'help': main_app.show_help
        }
        
        for action_id in action_handlers.keys():
            print(f"  - {action_id}")
        
        # Test si export_equipment est bien dans les actions
        if 'export_equipment' in action_handlers:
            print("✓ Action 'export_equipment' trouvée dans handle_action")
        else:
            print("❌ Action 'export_equipment' manquante dans handle_action")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur : {str(e)}")
        return False

def main():
    """Fonction principale"""
    print("🎯 TEST DE DÉBOGAGE EXPORT ÉQUIPEMENTS")
    print("=" * 60)
    
    tests = [
        ("Export direct", test_export_direct),
        ("Gestionnaires d'actions", test_action_handlers)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Test : {test_name}")
        print("-" * 40)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur : {str(e)}")
            results.append((test_name, False))
    
    # Résumé
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 60)
    
    success_count = 0
    for test_name, result in results:
        status = "✅ SUCCÈS" if result else "❌ ÉCHEC"
        print(f"{status} - {test_name}")
        if result:
            success_count += 1
    
    print(f"\n📊 RÉSULTATS : {success_count}/{len(results)} tests réussis")
    
    return success_count == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
