"""
Gestion professionnelle des équipements
Module avancé pour la gestion complète du parc d'équipements
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                             QTableWidgetItem, QPushButton, QLineEdit, QComboBox,
                             QLabel, QFrame, QSplitter, QGroupBox, QFormLayout,
                             QTextEdit, QDateEdit, QHeaderView, QMessageBox,
                             QDialog, QTabWidget, QProgressBar, QCheckBox,
                             QSpinBox, QDoubleSpinBox, QFileDialog, QMenu, QAction)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QTimer
from PyQt5.QtGui import QFont, QPixmap, QIcon, QColor
from utils.data_refresh_manager import RefreshableWidget, notify_data_changed

class EquipmentManagementPro(QWidget, RefreshableWidget):
    """Gestionnaire professionnel des équipements"""
    
    equipment_selected = pyqtSignal(int)
    equipment_updated = pyqtSignal()
    
    def __init__(self, db, parent=None):
        super().__init__(parent)
        RefreshableWidget.__init__(self)
        self.db = db
        self.current_equipment = None
        self.selected_equipment_ids = []
        
        self.setup_ui()
        self.load_equipment()
        self.enable_auto_refresh(['equipment'])
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Barre d'outils équipements
        toolbar = self.create_equipment_toolbar()
        layout.addWidget(toolbar)
        
        # Splitter principal
        main_splitter = QSplitter(Qt.Horizontal)
        
        # Panneau gauche : Liste des équipements
        left_panel = self.create_equipment_list_panel()
        main_splitter.addWidget(left_panel)
        
        # Panneau droit : Détails et actions
        right_panel = self.create_equipment_details_panel()
        main_splitter.addWidget(right_panel)
        
        # Proportions du splitter
        main_splitter.setSizes([600, 400])
        layout.addWidget(main_splitter)
    
    def create_equipment_toolbar(self):
        """Crée la barre d'outils des équipements"""
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        
        layout = QHBoxLayout(toolbar_frame)
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(15)
        
        # Recherche
        search_label = QLabel("🔍 Recherche :")
        search_label.setStyleSheet("font-weight: bold; color: #495057;")
        layout.addWidget(search_label)
        
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Nom, modèle, fabricant, localisation...")
        self.search_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px 12px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 14px;
                min-width: 250px;
            }
            QLineEdit:focus {
                border-color: #f39c12;
                outline: none;
            }
        """)
        self.search_edit.textChanged.connect(self.filter_equipment)
        layout.addWidget(self.search_edit)
        
        # Filtre par statut
        status_label = QLabel("📊 Statut :")
        status_label.setStyleSheet("font-weight: bold; color: #495057;")
        layout.addWidget(status_label)
        
        self.status_filter = QComboBox()
        self.status_filter.addItems(["Tous", "En service", "En maintenance", "Hors service", "En attente"])
        self.status_filter.setStyleSheet("""
            QComboBox {
                padding: 8px 12px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 14px;
                min-width: 120px;
            }
        """)
        self.status_filter.currentTextChanged.connect(self.filter_equipment)
        layout.addWidget(self.status_filter)
        
        layout.addStretch()
        
        # Boutons d'actions
        actions_layout = QHBoxLayout()
        actions_layout.setSpacing(8)
        
        # Bouton Nouvel équipement
        new_btn = QPushButton("➕ Nouvel Équipement")
        new_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        new_btn.clicked.connect(self.add_equipment)
        actions_layout.addWidget(new_btn)
        
        # Bouton Import
        import_btn = QPushButton("📥 Importer")
        import_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        import_btn.clicked.connect(self.import_equipment)
        actions_layout.addWidget(import_btn)
        
        # Bouton Export
        export_btn = QPushButton("📤 Exporter")
        export_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        export_btn.clicked.connect(self.export_equipment)
        actions_layout.addWidget(export_btn)
        
        layout.addLayout(actions_layout)
        
        return toolbar_frame
    
    def create_equipment_list_panel(self):
        """Crée le panneau de liste des équipements"""
        panel = QFrame()
        panel.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }
        """)
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # En-tête de la liste
        header_layout = QHBoxLayout()
        
        title_label = QLabel("📋 Liste des Équipements")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #495057;
            }
        """)
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Compteur d'équipements
        self.equipment_count_label = QLabel("0 équipement(s)")
        self.equipment_count_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        header_layout.addWidget(self.equipment_count_label)
        
        layout.addLayout(header_layout)
        
        # Table des équipements
        self.equipment_table = QTableWidget()
        self.equipment_table.setColumnCount(8)
        self.equipment_table.setHorizontalHeaderLabels([
            "ID", "Nom", "Modèle", "Fabricant", "Localisation", 
            "Statut", "Dernière Maintenance", "Prochaine Maintenance"
        ])
        
        # Configuration de la table
        self.equipment_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.equipment_table.setSelectionMode(QTableWidget.ExtendedSelection)
        self.equipment_table.setAlternatingRowColors(True)
        self.equipment_table.setSortingEnabled(True)
        
        # Style de la table
        self.equipment_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #e3f2fd;
                border: none;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                padding: 10px;
                border: none;
                border-bottom: 2px solid #dee2e6;
                font-weight: bold;
                color: #495057;
            }
        """)
        
        # Ajuster les colonnes
        header = self.equipment_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # ID
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # Nom
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Modèle
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Fabricant
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Localisation
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Statut
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Dernière
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # Prochaine
        
        # Connecter les signaux
        self.equipment_table.itemSelectionChanged.connect(self.on_equipment_selection_changed)
        self.equipment_table.itemDoubleClicked.connect(self.edit_equipment)
        
        # Menu contextuel
        self.equipment_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.equipment_table.customContextMenuRequested.connect(self.show_context_menu)
        
        layout.addWidget(self.equipment_table)
        
        return panel
    
    def create_equipment_details_panel(self):
        """Crée le panneau de détails des équipements"""
        panel = QFrame()
        panel.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }
        """)
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # En-tête des détails
        header_label = QLabel("📋 Détails de l'Équipement")
        header_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #495057;
                padding-bottom: 10px;
                border-bottom: 2px solid #f39c12;
            }
        """)
        layout.addWidget(header_label)
        
        # Onglets de détails
        self.details_tabs = QTabWidget()
        self.details_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                background-color: #f8f9fa;
            }
            QTabBar::tab {
                background-color: #e9ecef;
                border: 1px solid #dee2e6;
                padding: 8px 16px;
                margin-right: 2px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #f39c12;
                color: white;
            }
        """)
        
        # Onglet Informations générales
        self.general_info_tab = self.create_general_info_tab()
        self.details_tabs.addTab(self.general_info_tab, "ℹ️ Général")
        
        # Onglet Maintenance
        self.maintenance_tab = self.create_maintenance_tab()
        self.details_tabs.addTab(self.maintenance_tab, "🔧 Maintenance")
        
        # Onglet Documents
        self.documents_tab = self.create_documents_tab()
        self.details_tabs.addTab(self.documents_tab, "📄 Documents")
        
        layout.addWidget(self.details_tabs)
        
        # Boutons d'actions
        actions_frame = QFrame()
        actions_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-radius: 6px;
                padding: 10px;
            }
        """)
        
        actions_layout = QHBoxLayout(actions_frame)
        actions_layout.setSpacing(10)
        
        # Bouton Modifier
        self.edit_btn = QPushButton("✏️ Modifier")
        self.edit_btn.setStyleSheet("""
            QPushButton {
                background-color: #ffc107;
                color: #212529;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
            QPushButton:disabled {
                background-color: #6c757d;
                color: white;
            }
        """)
        self.edit_btn.clicked.connect(self.edit_equipment)
        self.edit_btn.setEnabled(False)
        actions_layout.addWidget(self.edit_btn)
        
        # Bouton Supprimer
        self.delete_btn = QPushButton("🗑️ Supprimer")
        self.delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
        self.delete_btn.clicked.connect(self.delete_equipment)
        self.delete_btn.setEnabled(False)
        actions_layout.addWidget(self.delete_btn)
        
        actions_layout.addStretch()
        
        # Bouton Planifier Maintenance
        self.schedule_btn = QPushButton("📅 Planifier Maintenance")
        self.schedule_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
        self.schedule_btn.clicked.connect(self.schedule_maintenance)
        self.schedule_btn.setEnabled(False)
        actions_layout.addWidget(self.schedule_btn)
        
        layout.addWidget(actions_frame)
        
        return panel
    
    def create_general_info_tab(self):
        """Crée l'onglet d'informations générales"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # Formulaire d'informations
        form_layout = QFormLayout()
        form_layout.setSpacing(10)
        
        # Labels d'informations (lecture seule)
        self.info_labels = {}
        
        info_fields = [
            ("name", "Nom :"),
            ("code", "Code :"),
            ("model", "Modèle :"),
            ("serial_number", "N° Série :"),
            ("manufacturer", "Fabricant :"),
            ("purchase_date", "Date d'achat :"),
            ("installation_date", "Date d'installation :"),
            ("location", "Localisation :"),
            ("status", "Statut :"),
            ("notes", "Notes :")
        ]
        
        for field_id, label_text in info_fields:
            label = QLabel(label_text)
            label.setStyleSheet("font-weight: bold; color: #495057;")
            
            if field_id == "notes":
                value_label = QLabel()
                value_label.setWordWrap(True)
                value_label.setMaximumHeight(60)
            else:
                value_label = QLabel()
            
            value_label.setStyleSheet("""
                QLabel {
                    background-color: white;
                    border: 1px solid #dee2e6;
                    border-radius: 4px;
                    padding: 8px;
                    color: #495057;
                }
            """)
            
            self.info_labels[field_id] = value_label
            form_layout.addRow(label, value_label)
        
        layout.addLayout(form_layout)
        layout.addStretch()
        
        return tab
    
    def create_maintenance_tab(self):
        """Crée l'onglet de maintenance"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # Informations de maintenance
        maintenance_group = QGroupBox("📅 Planification Maintenance")
        maintenance_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #f39c12;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px;
                color: #f39c12;
            }
        """)
        
        maintenance_layout = QFormLayout(maintenance_group)
        
        # Labels de maintenance
        self.maintenance_labels = {}
        
        maintenance_fields = [
            ("last_maintenance_date", "Dernière maintenance :"),
            ("next_maintenance_date", "Prochaine maintenance :"),
            ("maintenance_frequency", "Fréquence :"),
            ("maintenance_type", "Type :")
        ]
        
        for field_id, label_text in maintenance_fields:
            label = QLabel(label_text)
            label.setStyleSheet("font-weight: bold; color: #495057;")
            
            value_label = QLabel()
            value_label.setStyleSheet("""
                QLabel {
                    background-color: white;
                    border: 1px solid #dee2e6;
                    border-radius: 4px;
                    padding: 8px;
                    color: #495057;
                }
            """)
            
            self.maintenance_labels[field_id] = value_label
            maintenance_layout.addRow(label, value_label)
        
        layout.addWidget(maintenance_group)
        
        # Historique des interventions récentes
        history_group = QGroupBox("📋 Interventions Récentes")
        history_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #17a2b8;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px;
                color: #17a2b8;
            }
        """)
        
        history_layout = QVBoxLayout(history_group)
        
        self.interventions_table = QTableWidget()
        self.interventions_table.setColumnCount(4)
        self.interventions_table.setHorizontalHeaderLabels([
            "Date", "Type", "Technicien", "Statut"
        ])
        self.interventions_table.setMaximumHeight(150)
        self.interventions_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                padding: 8px;
                border: none;
                border-bottom: 1px solid #dee2e6;
                font-weight: bold;
                color: #495057;
            }
        """)
        
        history_layout.addWidget(self.interventions_table)
        layout.addWidget(history_group)
        
        layout.addStretch()
        
        return tab
    
    def create_documents_tab(self):
        """Crée l'onglet des documents"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # Barre d'outils documents
        docs_toolbar = QHBoxLayout()
        
        add_doc_btn = QPushButton("📎 Ajouter Document")
        add_doc_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        add_doc_btn.clicked.connect(self.add_document)
        docs_toolbar.addWidget(add_doc_btn)
        
        docs_toolbar.addStretch()
        
        layout.addLayout(docs_toolbar)
        
        # Liste des documents
        self.documents_table = QTableWidget()
        self.documents_table.setColumnCount(4)
        self.documents_table.setHorizontalHeaderLabels([
            "Nom", "Type", "Date", "Actions"
        ])
        
        self.documents_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                padding: 8px;
                border: none;
                border-bottom: 1px solid #dee2e6;
                font-weight: bold;
                color: #495057;
            }
        """)
        
        layout.addWidget(self.documents_table)
        
        return tab
    
    def load_equipment(self):
        """Charge la liste des équipements"""
        try:
            equipment_list = self.db.get_all_equipment()
            self.populate_equipment_table(equipment_list)
            self.equipment_count_label.setText(f"{len(equipment_list)} équipement(s)")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des équipements : {str(e)}")
    
    def populate_equipment_table(self, equipment_list):
        """Remplit la table des équipements"""
        self.equipment_table.setRowCount(len(equipment_list))
        
        for row, equipment in enumerate(equipment_list):
            # ID
            self.equipment_table.setItem(row, 0, QTableWidgetItem(str(equipment[0])))
            
            # Nom
            self.equipment_table.setItem(row, 1, QTableWidgetItem(equipment[2] or ""))
            
            # Modèle
            self.equipment_table.setItem(row, 2, QTableWidgetItem(equipment[4] or ""))
            
            # Fabricant
            self.equipment_table.setItem(row, 3, QTableWidgetItem(equipment[6] or ""))
            
            # Localisation
            self.equipment_table.setItem(row, 4, QTableWidgetItem(equipment[9] or ""))
            
            # Statut avec couleur
            status_item = QTableWidgetItem(equipment[10] or "")
            status_color = self.get_status_color(equipment[10])
            status_item.setBackground(QColor(status_color))
            self.equipment_table.setItem(row, 5, status_item)
            
            # Dernière maintenance
            last_maintenance = ""
            if equipment[11]:
                last_maintenance = QDate.fromString(equipment[11], "yyyy-MM-dd").toString("dd/MM/yyyy")
            self.equipment_table.setItem(row, 6, QTableWidgetItem(last_maintenance))
            
            # Prochaine maintenance
            next_maintenance = ""
            if equipment[12]:
                next_maintenance = QDate.fromString(equipment[12], "yyyy-MM-dd").toString("dd/MM/yyyy")
                # Colorer en rouge si en retard
                next_item = QTableWidgetItem(next_maintenance)
                if QDate.fromString(equipment[12], "yyyy-MM-dd") < QDate.currentDate():
                    next_item.setBackground(QColor("#ffebee"))
                    next_item.setForeground(QColor("#d32f2f"))
                self.equipment_table.setItem(row, 7, next_item)
            else:
                self.equipment_table.setItem(row, 7, QTableWidgetItem(""))
    
    def get_status_color(self, status):
        """Retourne la couleur associée au statut"""
        colors = {
            "En service": "#e8f5e8",
            "En maintenance": "#fff3cd",
            "Hors service": "#f8d7da",
            "En attente": "#d1ecf1"
        }
        return colors.get(status, "#f8f9fa")
    
    def filter_equipment(self):
        """Filtre les équipements selon les critères"""
        search_text = self.search_edit.text().lower()
        status_filter = self.status_filter.currentText()
        
        for row in range(self.equipment_table.rowCount()):
            show_row = True
            
            # Filtre par texte de recherche
            if search_text:
                row_text = ""
                for col in range(1, 5):  # Nom, Modèle, Fabricant, Localisation
                    item = self.equipment_table.item(row, col)
                    if item:
                        row_text += item.text().lower() + " "
                
                if search_text not in row_text:
                    show_row = False
            
            # Filtre par statut
            if status_filter != "Tous":
                status_item = self.equipment_table.item(row, 5)
                if not status_item or status_item.text() != status_filter:
                    show_row = False
            
            self.equipment_table.setRowHidden(row, not show_row)
    
    def on_equipment_selection_changed(self):
        """Gère le changement de sélection d'équipement"""
        selected_rows = set()
        for item in self.equipment_table.selectedItems():
            selected_rows.add(item.row())
        
        if selected_rows:
            # Prendre le premier équipement sélectionné
            row = min(selected_rows)
            equipment_id_item = self.equipment_table.item(row, 0)
            if equipment_id_item:
                equipment_id = int(equipment_id_item.text())
                self.load_equipment_details(equipment_id)
                self.current_equipment = equipment_id
                
                # Activer les boutons
                self.edit_btn.setEnabled(True)
                self.delete_btn.setEnabled(True)
                self.schedule_btn.setEnabled(True)
        else:
            self.clear_equipment_details()
            self.current_equipment = None
            
            # Désactiver les boutons
            self.edit_btn.setEnabled(False)
            self.delete_btn.setEnabled(False)
            self.schedule_btn.setEnabled(False)
    
    def load_equipment_details(self, equipment_id):
        """Charge les détails d'un équipement"""
        try:
            equipment = self.db.get_equipment(equipment_id)
            if equipment:
                # Remplir les informations générales
                self.info_labels["name"].setText(equipment[2] or "")
                self.info_labels["code"].setText(equipment[3] or "")
                self.info_labels["model"].setText(equipment[4] or "")
                self.info_labels["serial_number"].setText(equipment[5] or "")
                self.info_labels["manufacturer"].setText(equipment[6] or "")
                
                # Dates
                purchase_date = ""
                if equipment[7]:
                    purchase_date = QDate.fromString(equipment[7], "yyyy-MM-dd").toString("dd/MM/yyyy")
                self.info_labels["purchase_date"].setText(purchase_date)
                
                installation_date = ""
                if equipment[8]:
                    installation_date = QDate.fromString(equipment[8], "yyyy-MM-dd").toString("dd/MM/yyyy")
                self.info_labels["installation_date"].setText(installation_date)
                
                self.info_labels["location"].setText(equipment[9] or "")
                self.info_labels["status"].setText(equipment[10] or "")
                self.info_labels["notes"].setText(equipment[13] or "")
                
                # Informations de maintenance
                last_maintenance = ""
                if equipment[11]:
                    last_maintenance = QDate.fromString(equipment[11], "yyyy-MM-dd").toString("dd/MM/yyyy")
                self.maintenance_labels["last_maintenance_date"].setText(last_maintenance)
                
                next_maintenance = ""
                if equipment[12]:
                    next_maintenance = QDate.fromString(equipment[12], "yyyy-MM-dd").toString("dd/MM/yyyy")
                self.maintenance_labels["next_maintenance_date"].setText(next_maintenance)
                
                # Charger les interventions récentes
                self.load_recent_interventions(equipment_id)
                
                # Charger les documents
                self.load_equipment_documents(equipment_id)
                
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des détails : {str(e)}")
    
    def clear_equipment_details(self):
        """Efface les détails de l'équipement"""
        for label in self.info_labels.values():
            label.setText("")
        
        for label in self.maintenance_labels.values():
            label.setText("")
        
        self.interventions_table.setRowCount(0)
        self.documents_table.setRowCount(0)
    
    def load_recent_interventions(self, equipment_id):
        """Charge les interventions récentes pour un équipement"""
        try:
            cursor = self.db.cursor
            cursor.execute("""
                SELECT intervention_date, intervention_type, technician_name, status
                FROM maintenance_interventions 
                WHERE equipment_id = ? 
                ORDER BY intervention_date DESC 
                LIMIT 5
            """, (equipment_id,))
            
            interventions = cursor.fetchall()
            self.interventions_table.setRowCount(len(interventions))
            
            for row, intervention in enumerate(interventions):
                self.interventions_table.setItem(row, 0, QTableWidgetItem(intervention[0] or ""))
                self.interventions_table.setItem(row, 1, QTableWidgetItem(intervention[1] or ""))
                self.interventions_table.setItem(row, 2, QTableWidgetItem(intervention[2] or ""))
                self.interventions_table.setItem(row, 3, QTableWidgetItem(intervention[3] or ""))
                
        except Exception as e:
            print(f"Erreur chargement interventions : {str(e)}")
    
    def load_equipment_documents(self, equipment_id):
        """Charge les documents d'un équipement"""
        try:
            cursor = self.db.cursor
            cursor.execute("""
                SELECT name, document_type, upload_date, file_path
                FROM equipment_documents 
                WHERE equipment_id = ? 
                ORDER BY upload_date DESC
            """, (equipment_id,))
            
            documents = cursor.fetchall()
            self.documents_table.setRowCount(len(documents))
            
            for row, doc in enumerate(documents):
                self.documents_table.setItem(row, 0, QTableWidgetItem(doc[0] or ""))
                self.documents_table.setItem(row, 1, QTableWidgetItem(doc[1] or ""))
                self.documents_table.setItem(row, 2, QTableWidgetItem(doc[2] or ""))
                
                # Bouton d'actions pour le document
                actions_widget = QWidget()
                actions_layout = QHBoxLayout(actions_widget)
                actions_layout.setContentsMargins(5, 2, 5, 2)
                
                view_btn = QPushButton("👁️")
                view_btn.setToolTip("Voir le document")
                view_btn.setMaximumSize(30, 25)
                view_btn.clicked.connect(lambda checked, path=doc[3]: self.view_document(path))
                actions_layout.addWidget(view_btn)
                
                delete_doc_btn = QPushButton("🗑️")
                delete_doc_btn.setToolTip("Supprimer le document")
                delete_doc_btn.setMaximumSize(30, 25)
                delete_doc_btn.clicked.connect(lambda checked, path=doc[3]: self.delete_document(path))
                actions_layout.addWidget(delete_doc_btn)
                
                self.documents_table.setCellWidget(row, 3, actions_widget)
                
        except Exception as e:
            print(f"Erreur chargement documents : {str(e)}")
    
    def show_context_menu(self, position):
        """Affiche le menu contextuel"""
        if self.equipment_table.itemAt(position):
            menu = QMenu(self)
            
            edit_action = QAction("✏️ Modifier", self)
            edit_action.triggered.connect(self.edit_equipment)
            menu.addAction(edit_action)
            
            delete_action = QAction("🗑️ Supprimer", self)
            delete_action.triggered.connect(self.delete_equipment)
            menu.addAction(delete_action)
            
            menu.addSeparator()
            
            schedule_action = QAction("📅 Planifier Maintenance", self)
            schedule_action.triggered.connect(self.schedule_maintenance)
            menu.addAction(schedule_action)
            
            intervention_action = QAction("🔧 Nouvelle Intervention", self)
            intervention_action.triggered.connect(self.create_intervention)
            menu.addAction(intervention_action)
            
            menu.exec_(self.equipment_table.mapToGlobal(position))
    
    def add_equipment(self):
        """Ajoute un nouvel équipement"""
        from gui.equipment_dialog_pro import EquipmentDialogPro
        dialog = EquipmentDialogPro(self.db, self)
        if dialog.exec_() == QDialog.Accepted:
            self.refresh_data()
            notify_data_changed('equipment')
    
    def edit_equipment(self):
        """Modifie l'équipement sélectionné"""
        if self.current_equipment:
            from gui.equipment_dialog_pro import EquipmentDialogPro
            dialog = EquipmentDialogPro(self.db, self, self.current_equipment)
            if dialog.exec_() == QDialog.Accepted:
                self.refresh_data()
                notify_data_changed('equipment')
    
    def delete_equipment(self):
        """Supprime l'équipement sélectionné"""
        if self.current_equipment:
            reply = QMessageBox.question(
                self, "Confirmation", 
                "Êtes-vous sûr de vouloir supprimer cet équipement ?",
                QMessageBox.Yes | QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                try:
                    self.db.delete_equipment(self.current_equipment)
                    self.refresh_data()
                    notify_data_changed('equipment')
                    QMessageBox.information(self, "Succès", "Équipement supprimé avec succès.")
                except Exception as e:
                    QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression : {str(e)}")
    
    def schedule_maintenance(self):
        """Planifie une maintenance pour l'équipement"""
        if self.current_equipment:
            from gui.schedule_maintenance_dialog import ScheduleMaintenanceDialog
            dialog = ScheduleMaintenanceDialog(self.db, self, self.current_equipment)
            if dialog.exec_() == QDialog.Accepted:
                self.refresh_data()
                notify_data_changed('maintenance')
    
    def create_intervention(self):
        """Crée une nouvelle intervention"""
        if self.current_equipment:
            from gui.intervention_dialog import InterventionDialog
            dialog = InterventionDialog(self.db, self, equipment_id=self.current_equipment)
            if dialog.exec_() == QDialog.Accepted:
                self.refresh_data()
                notify_data_changed('maintenance')
    
    def import_equipment(self):
        """Importe des équipements depuis un fichier"""
        from gui.equipment_import_dialog import EquipmentImportDialog
        dialog = EquipmentImportDialog(self, self.db)
        if dialog.exec_() == QDialog.Accepted:
            self.refresh_data()
            notify_data_changed('equipment')
    
    def export_equipment(self):
        """Exporte la liste des équipements"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "Exporter les équipements", 
                f"equipements_{QDate.currentDate().toString('yyyy-MM-dd')}.xlsx",
                "Fichiers Excel (*.xlsx)"
            )
            
            if file_path:
                # Utiliser l'exporteur Excel existant
                if hasattr(self.parent(), 'excel_exporter'):
                    self.parent().excel_exporter.export_equipment_list(file_path)
                    QMessageBox.information(self, "Succès", f"Équipements exportés vers {file_path}")
                else:
                    QMessageBox.warning(self, "Erreur", "Exporteur Excel non disponible")
                    
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'export : {str(e)}")
    
    def add_document(self):
        """Ajoute un document à l'équipement"""
        if self.current_equipment:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "Sélectionner un document",
                "", "Tous les fichiers (*.*)"
            )
            
            if file_path:
                from gui.document_dialog import DocumentDialog
                dialog = DocumentDialog(self.db, self, self.current_equipment, file_path)
                if dialog.exec_() == QDialog.Accepted:
                    self.load_equipment_documents(self.current_equipment)
    
    def view_document(self, file_path):
        """Ouvre un document"""
        try:
            import os
            os.startfile(file_path)
        except Exception as e:
            QMessageBox.warning(self, "Erreur", f"Impossible d'ouvrir le document : {str(e)}")
    
    def delete_document(self, file_path):
        """Supprime un document"""
        reply = QMessageBox.question(
            self, "Confirmation",
            "Êtes-vous sûr de vouloir supprimer ce document ?",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                # Supprimer de la base de données
                cursor = self.db.cursor
                cursor.execute("DELETE FROM equipment_documents WHERE file_path = ?", (file_path,))
                self.db.conn.commit()
                
                # Recharger les documents
                if self.current_equipment:
                    self.load_equipment_documents(self.current_equipment)
                    
                QMessageBox.information(self, "Succès", "Document supprimé avec succès.")
            except Exception as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression : {str(e)}")
    
    def refresh_data(self):
        """Actualise les données (pour le système d'actualisation automatique)"""
        self.load_equipment()
        if self.current_equipment:
            self.load_equipment_details(self.current_equipment)

        # Émettre le signal de mise à jour
        self.equipment_updated.emit()
