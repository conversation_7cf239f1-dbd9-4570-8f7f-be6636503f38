#!/usr/bin/env python3
"""
Test de l'interface utilisateur pour l'export d'équipements
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QTimer

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ui_export():
    """Test de l'export via l'interface utilisateur"""
    print("🎯 TEST DE L'INTERFACE UTILISATEUR POUR L'EXPORT")
    
    try:
        app = QApplication(sys.argv)
        from database import Database
        from export.excel_export import ExcelExporter
        from main import OptimizedSotramineApp
        
        db = Database()
        excel_exporter = ExcelExporter(db)
        main_app = OptimizedSotramineApp(db, excel_exporter)
        
        print("✓ Application créée")
        
        # Test 1: Vérifier la section par défaut
        print(f"\n1. Section par défaut : {getattr(main_app, 'current_section', 'Non définie')}")
        
        # Test 2: Naviguer vers la section équipements
        print("\n2. Navigation vers la section équipements:")
        main_app.navigate_to_section('equipment')
        print(f"   Section après navigation : {getattr(main_app, 'current_section', 'Non définie')}")
        
        # Test 3: Vérifier que la barre d'outils contextuelle est mise à jour
        print("\n3. Vérification de la barre d'outils contextuelle:")
        if hasattr(main_app, 'contextual_toolbar'):
            print("   ✓ Barre d'outils contextuelle disponible")
            # Simuler un clic sur le bouton d'export
            print("   ✓ Simulation du clic sur le bouton d'export...")
            
            # Utiliser un timer pour simuler le clic après que l'interface soit prête
            def simulate_export_click():
                try:
                    main_app.handle_action('export_equipment')
                    print("   ✓ Export simulé avec succès")
                    
                    # Afficher un message de confirmation
                    QMessageBox.information(main_app, "Test Export", 
                                          "L'export d'équipements a été testé avec succès!\n\n"
                                          "Vérifiez le dossier 'export' pour le fichier généré.")
                    
                except Exception as e:
                    print(f"   ❌ Erreur lors de l'export simulé : {str(e)}")
                    QMessageBox.critical(main_app, "Erreur Test", 
                                       f"Erreur lors du test d'export : {str(e)}")
            
            # Programmer l'exécution après 1 seconde
            QTimer.singleShot(1000, simulate_export_click)
            
        else:
            print("   ❌ Barre d'outils contextuelle non disponible")
        
        # Afficher l'application
        main_app.show()
        
        # Lancer la boucle d'événements
        print("\n4. Lancement de l'interface utilisateur...")
        print("   L'application va s'ouvrir et tester l'export automatiquement.")
        print("   Fermez l'application pour terminer le test.")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ Erreur générale : {str(e)}")
        return False

if __name__ == "__main__":
    print("🎯 TEST DE L'INTERFACE UTILISATEUR POUR L'EXPORT D'ÉQUIPEMENTS")
    print("=" * 70)
    
    result = test_ui_export()
    
    if result == 0:
        print("\n✅ Test terminé avec succès")
    else:
        print("\n❌ Test terminé avec des erreurs")
    
    sys.exit(result)
