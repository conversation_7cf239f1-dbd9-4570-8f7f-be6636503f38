#!/usr/bin/env python3
"""
Application principale SOTRAMINE PHOSPHATE - Version corrigée
Système de Gestion de Maintenance Industrielle
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, <PERSON><PERSON>tackedWidget, QWidget, 
                             QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QFrame, 
                             QSplitter, QMessageBox, QTabWidget)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QIcon

class SotramineMainApp(QMainWindow):
    """Application principale SOTRAMINE PHOSPHATE avec toutes les fonctionnalités"""
    
    def __init__(self):
        super().__init__()
        self.db = None
        self.excel_exporter = None
        self.setup_database()
        self.setup_ui()
        self.setup_auto_refresh()
    
    def setup_database(self):
        """Initialise la base de données"""
        try:
            from database import Database
            from export.excel_export import ExcelExporter
            
            self.db = Database()
            self.excel_exporter = ExcelExporter(self.db)
            print("✓ Base de données et exporteur initialisés")
        except Exception as e:
            print(f"❌ Erreur base de données : {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Impossible d'initialiser la base de données :\n{str(e)}")
    
    def setup_ui(self):
        """Configure l'interface utilisateur complète"""
        self.setWindowTitle("SOTRAMINE PHOSPHATE - Système de Gestion Intégré")
        self.setGeometry(100, 100, 1600, 1000)
        
        # Widget central avec splitter
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Splitter principal
        splitter = QSplitter(Qt.Horizontal)
        
        # Menu latéral
        self.sidebar = self.create_sidebar()
        splitter.addWidget(self.sidebar)
        
        # Zone de contenu avec onglets
        self.content_area = self.create_content_area()
        splitter.addWidget(self.content_area)
        
        # Proportions
        splitter.setSizes([350, 1250])
        splitter.setCollapsible(0, False)
        splitter.setCollapsible(1, False)
        
        main_layout.addWidget(splitter)
        
        # Afficher l'accueil par défaut
        self.show_home()
    
    def create_sidebar(self):
        """Crée le menu latéral organisé"""
        sidebar = QFrame()
        sidebar.setStyleSheet("""
            QFrame {
                background-color: #2c3e50;
                border-right: 2px solid #34495e;
            }
        """)
        sidebar.setFixedWidth(350)
        
        layout = QVBoxLayout(sidebar)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # En-tête avec logo
        header = self.create_header()
        layout.addWidget(header)
        
        # Sections du menu
        self.create_menu_sections(layout)
        
        layout.addStretch()
        
        return sidebar
    
    def create_header(self):
        """Crée l'en-tête avec logo"""
        header = QFrame()
        header.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #34495e, stop:1 #2c3e50);
                padding: 25px;
                border-bottom: 3px solid #3498db;
            }
        """)
        header.setFixedHeight(120)
        
        layout = QVBoxLayout(header)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(8)
        
        # Titre principal
        title = QLabel("SOTRAMINE")
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24px;
                font-weight: bold;
                letter-spacing: 2px;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Sous-titre
        subtitle = QLabel("PHOSPHATE")
        subtitle.setStyleSheet("""
            QLabel {
                color: #3498db;
                font-size: 18px;
                font-weight: bold;
                letter-spacing: 1px;
            }
        """)
        subtitle.setAlignment(Qt.AlignCenter)
        layout.addWidget(subtitle)
        
        # Version
        version = QLabel("v2.1 - Maintenance Professionnelle")
        version.setStyleSheet("""
            QLabel {
                color: #95a5a6;
                font-size: 10px;
                font-weight: normal;
            }
        """)
        version.setAlignment(Qt.AlignCenter)
        layout.addWidget(version)
        
        return header
    
    def create_menu_sections(self, layout):
        """Crée les sections du menu organisées"""
        self.sidebar_buttons = {}
        
        # Section PRODUCTION
        self.add_section_header(layout, "📊 PRODUCTION", "#e74c3c")
        production_items = [
            ('home', '🏠 Tableau de Bord', 'Vue d\'ensemble et statistiques'),
            ('tasks', '📋 Gestion des Tâches', 'Création et suivi des tâches'),
            ('reports', '📄 Rapports & Analyses', 'Génération de rapports')
        ]
        self.add_section_buttons(layout, production_items)
        
        layout.addSpacing(20)
        
        # Section MAINTENANCE
        self.add_section_header(layout, "🔧 MAINTENANCE", "#f39c12")
        maintenance_items = [
            ('maintenance', '🔧 Centre de Maintenance', 'Système complet de maintenance (NOUVEAU)'),
            ('equipment', '🔌 Gestion Équipements', 'Parc d\'équipements et maintenance'),
            ('spare_parts', '🔧 Pièces de Rechange', 'Inventaire et gestion du stock')
        ]
        self.add_section_buttons(layout, maintenance_items)
        
        layout.addSpacing(20)
        
        # Section PERSONNEL
        self.add_section_header(layout, "👥 PERSONNEL", "#27ae60")
        personnel_items = [
            ('personnel', '👤 Gestion Personnel', 'Base de données du personnel'),
            ('attendance', '📊 Pointage & Présences', 'Suivi des présences quotidiennes')
        ]
        self.add_section_buttons(layout, personnel_items)
        
        layout.addSpacing(20)
        
        # Section CONFIGURATION
        self.add_section_header(layout, "⚙️ CONFIGURATION", "#9b59b6")
        config_items = [
            ('settings', '⚙️ Paramètres', 'Configuration de l\'application'),
            ('backup', '💾 Sauvegarde', 'Sauvegarde et restauration'),
            ('help', '❓ Aide', 'Documentation et support')
        ]
        self.add_section_buttons(layout, config_items)
    
    def add_section_header(self, layout, title, color):
        """Ajoute un en-tête de section"""
        header = QLabel(title)
        header.setStyleSheet(f"""
            QLabel {{
                font-size: 14px;
                font-weight: bold;
                padding: 15px 20px 10px 20px;
                color: {color};
                background-color: transparent;
                border-bottom: 2px solid {color};
                margin: 8px 0 8px 0;
            }}
        """)
        layout.addWidget(header)
    
    def add_section_buttons(self, layout, items):
        """Ajoute les boutons d'une section"""
        for section_id, title, description in items:
            btn = QPushButton(title)
            btn.setToolTip(description)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: transparent;
                    color: #ecf0f1;
                    border: none;
                    padding: 14px 25px;
                    text-align: left;
                    font-size: 14px;
                    margin-left: 20px;
                    border-radius: 6px;
                }
                QPushButton:hover {
                    background-color: #34495e;
                    color: white;
                    border-left: 4px solid #3498db;
                }
                QPushButton:pressed {
                    background-color: #3498db;
                }
                QPushButton:checked {
                    background-color: #3498db;
                    border-left: 4px solid #2980b9;
                }
            """)
            btn.setCheckable(True)
            btn.clicked.connect(lambda checked, sid=section_id: self.navigate_to_section(sid))
            self.sidebar_buttons[section_id] = btn
            layout.addWidget(btn)
    
    def create_content_area(self):
        """Crée la zone de contenu principal"""
        content = QStackedWidget()
        
        # Page d'accueil
        self.home_page = self.create_home_page()
        content.addWidget(self.home_page)
        
        # Page de maintenance (nouveau)
        self.maintenance_page = self.create_maintenance_page()
        content.addWidget(self.maintenance_page)
        
        return content
    
    def create_home_page(self):
        """Crée la page d'accueil avec statistiques"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(40, 40, 40, 40)
        layout.setSpacing(30)
        
        # Titre de bienvenue
        welcome_frame = self.create_welcome_frame()
        layout.addWidget(welcome_frame)
        
        # Statistiques rapides
        stats_frame = self.create_stats_frame()
        layout.addWidget(stats_frame)
        
        # Actions rapides
        actions_frame = self.create_quick_actions_frame()
        layout.addWidget(actions_frame)
        
        # Actualités et notifications
        news_frame = self.create_news_frame()
        layout.addWidget(news_frame)
        
        layout.addStretch()
        
        return page
    
    def create_welcome_frame(self):
        """Crée le cadre de bienvenue"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 15px;
                padding: 30px;
                margin: 10px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(15)
        
        # Titre principal
        title = QLabel("Bienvenue dans SOTRAMINE PHOSPHATE")
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 32px;
                font-weight: bold;
                margin-bottom: 10px;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Sous-titre
        subtitle = QLabel("Système de Gestion de Maintenance Industrielle")
        subtitle.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.9);
                font-size: 18px;
                font-weight: 500;
            }
        """)
        subtitle.setAlignment(Qt.AlignCenter)
        layout.addWidget(subtitle)
        
        # Badge nouveau
        badge = QLabel("🆕 Menu Maintenance Développé Professionnellement")
        badge.setStyleSheet("""
            QLabel {
                background-color: #e74c3c;
                color: white;
                padding: 8px 16px;
                border-radius: 20px;
                font-size: 12px;
                font-weight: bold;
                margin-top: 10px;
            }
        """)
        badge.setAlignment(Qt.AlignCenter)
        layout.addWidget(badge)
        
        return frame
    
    def create_stats_frame(self):
        """Crée le cadre des statistiques"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 12px;
                padding: 25px;
                margin: 10px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        # Titre
        title = QLabel("📊 Tableau de Bord")
        title.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
        # Grille de statistiques
        stats_layout = QHBoxLayout()
        
        # Statistiques simulées (à remplacer par de vraies données)
        stats = [
            ("📋", "Tâches", "0", "#3498db"),
            ("🔌", "Équipements", "0", "#e67e22"),
            ("🔧", "Interventions", "0", "#e74c3c"),
            ("👥", "Personnel", "0", "#27ae60"),
            ("📊", "Rapports", "0", "#9b59b6"),
            ("⚠️", "Alertes", "0", "#f39c12")
        ]
        
        for icon, label, value, color in stats:
            stat_widget = self.create_stat_widget(icon, label, value, color)
            stats_layout.addWidget(stat_widget)
        
        layout.addLayout(stats_layout)
        
        return frame
    
    def create_stat_widget(self, icon, label, value, color):
        """Crée un widget de statistique"""
        widget = QFrame()
        widget.setStyleSheet(f"""
            QFrame {{
                background-color: {color}15;
                border: 2px solid {color};
                border-radius: 10px;
                padding: 20px;
                margin: 5px;
                min-width: 120px;
                min-height: 100px;
            }}
            QFrame:hover {{
                background-color: {color}25;
                transform: scale(1.05);
            }}
        """)
        
        layout = QVBoxLayout(widget)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(8)
        
        # Icône
        icon_label = QLabel(icon)
        icon_label.setStyleSheet(f"""
            QLabel {{
                font-size: 28px;
                color: {color};
            }}
        """)
        icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_label)
        
        # Valeur
        value_label = QLabel(value)
        value_label.setStyleSheet(f"""
            QLabel {{
                font-size: 24px;
                font-weight: bold;
                color: {color};
            }}
        """)
        value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(value_label)
        
        # Label
        label_widget = QLabel(label)
        label_widget.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #7f8c8d;
                font-weight: bold;
            }
        """)
        label_widget.setAlignment(Qt.AlignCenter)
        layout.addWidget(label_widget)
        
        return widget
    
    def create_quick_actions_frame(self):
        """Crée le cadre des actions rapides"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 12px;
                padding: 25px;
                margin: 10px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        # Titre
        title = QLabel("⚡ Actions Rapides")
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # Boutons d'actions
        actions_layout = QHBoxLayout()
        
        actions = [
            ("📋 Nouvelle Tâche", lambda: self.navigate_to_section('tasks'), "#3498db"),
            ("🔧 Centre Maintenance", lambda: self.navigate_to_section('maintenance'), "#f39c12"),
            ("🔌 Nouvel Équipement", lambda: self.navigate_to_section('equipment'), "#e67e22"),
            ("👤 Nouveau Personnel", lambda: self.navigate_to_section('personnel'), "#27ae60"),
            ("📊 Voir Rapports", lambda: self.navigate_to_section('reports'), "#9b59b6")
        ]
        
        for text, action, color in actions:
            btn = QPushButton(text)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 12px 16px;
                    font-size: 13px;
                    font-weight: bold;
                    min-width: 140px;
                    min-height: 45px;
                }}
                QPushButton:hover {{
                    background-color: {color}dd;
                    transform: translateY(-2px);
                }}
                QPushButton:pressed {{
                    background-color: {color}bb;
                    transform: translateY(0px);
                }}
            """)
            btn.clicked.connect(action)
            actions_layout.addWidget(btn)
        
        layout.addLayout(actions_layout)
        
        return frame
    
    def create_news_frame(self):
        """Crée le cadre des actualités"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 12px;
                padding: 25px;
                margin: 10px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        # Titre
        title = QLabel("📢 Actualités & Notifications")
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # Actualités
        news_items = [
            ("🆕", "Menu Maintenance développé professionnellement", "Nouveau système complet de gestion de maintenance industrielle"),
            ("✅", "Base de données optimisée", "Performances améliorées avec index et optimisations"),
            ("🎨", "Interface modernisée", "Design professionnel avec navigation intuitive"),
            ("🔄", "Actualisation automatique", "Synchronisation temps réel entre tous les modules")
        ]
        
        for icon, title_text, description in news_items:
            news_item = self.create_news_item(icon, title_text, description)
            layout.addWidget(news_item)
        
        return frame
    
    def create_news_item(self, icon, title_text, description):
        """Crée un élément d'actualité"""
        item = QFrame()
        item.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-left: 4px solid #3498db;
                border-radius: 6px;
                padding: 12px;
                margin: 5px 0;
            }
            QFrame:hover {
                background-color: #e9ecef;
            }
        """)
        
        layout = QHBoxLayout(item)
        layout.setSpacing(12)
        
        # Icône
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                min-width: 30px;
            }
        """)
        layout.addWidget(icon_label)
        
        # Contenu
        content_layout = QVBoxLayout()
        content_layout.setSpacing(4)
        
        # Titre
        title_label = QLabel(title_text)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
            }
        """)
        content_layout.addWidget(title_label)
        
        # Description
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #6c757d;
            }
        """)
        content_layout.addWidget(desc_label)
        
        layout.addLayout(content_layout)
        layout.addStretch()
        
        return item
