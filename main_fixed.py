#!/usr/bin/env python3
"""
Application principale SOTRAMINE PHOSPHATE - Version corrigée
Système de Gestion de Maintenance Industrielle
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, <PERSON><PERSON>tackedWidget, QWidget, 
                             QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QFrame, 
                             QSplitter, QMessageBox, QTabWidget)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QIcon

class SotramineMainApp(QMainWindow):
    """Application principale SOTRAMINE PHOSPHATE avec toutes les fonctionnalités"""
    
    def __init__(self):
        super().__init__()
        self.db = None
        self.excel_exporter = None
        self.setup_database()
        self.setup_ui()
        self.setup_auto_refresh()
    
    def setup_database(self):
        """Initialise la base de données"""
        try:
            from database import Database
            from export.excel_export import ExcelExporter
            
            self.db = Database()
            self.excel_exporter = ExcelExporter(self.db)
            print("✓ Base de données et exporteur initialisés")
        except Exception as e:
            print(f"❌ Erreur base de données : {str(e)}")
            QMessageBox.critical(self, "Erreur", f"Impossible d'initialiser la base de données :\n{str(e)}")
    
    def setup_ui(self):
        """Configure l'interface utilisateur complète"""
        self.setWindowTitle("SOTRAMINE PHOSPHATE - Système de Gestion Intégré")
        self.setGeometry(100, 100, 1600, 1000)
        
        # Widget central avec splitter
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Splitter principal
        splitter = QSplitter(Qt.Horizontal)
        
        # Menu latéral
        self.sidebar = self.create_sidebar()
        splitter.addWidget(self.sidebar)
        
        # Zone de contenu avec onglets
        self.content_area = self.create_content_area()
        splitter.addWidget(self.content_area)
        
        # Proportions
        splitter.setSizes([350, 1250])
        splitter.setCollapsible(0, False)
        splitter.setCollapsible(1, False)
        
        main_layout.addWidget(splitter)
        
        # Afficher l'accueil par défaut
        self.show_home()
    
    def create_sidebar(self):
        """Crée le menu latéral organisé"""
        sidebar = QFrame()
        sidebar.setStyleSheet("""
            QFrame {
                background-color: #2c3e50;
                border-right: 2px solid #34495e;
            }
        """)
        sidebar.setFixedWidth(350)
        
        layout = QVBoxLayout(sidebar)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # En-tête avec logo
        header = self.create_header()
        layout.addWidget(header)
        
        # Sections du menu
        self.create_menu_sections(layout)
        
        layout.addStretch()
        
        return sidebar
    
    def create_header(self):
        """Crée l'en-tête avec logo"""
        header = QFrame()
        header.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #34495e, stop:1 #2c3e50);
                padding: 25px;
                border-bottom: 3px solid #3498db;
            }
        """)
        header.setFixedHeight(120)
        
        layout = QVBoxLayout(header)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(8)
        
        # Titre principal
        title = QLabel("SOTRAMINE")
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24px;
                font-weight: bold;
                letter-spacing: 2px;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Sous-titre
        subtitle = QLabel("PHOSPHATE")
        subtitle.setStyleSheet("""
            QLabel {
                color: #3498db;
                font-size: 18px;
                font-weight: bold;
                letter-spacing: 1px;
            }
        """)
        subtitle.setAlignment(Qt.AlignCenter)
        layout.addWidget(subtitle)
        
        # Version
        version = QLabel("v2.1 - Maintenance Professionnelle")
        version.setStyleSheet("""
            QLabel {
                color: #95a5a6;
                font-size: 10px;
                font-weight: normal;
            }
        """)
        version.setAlignment(Qt.AlignCenter)
        layout.addWidget(version)
        
        return header
    
    def create_menu_sections(self, layout):
        """Crée les sections du menu organisées"""
        self.sidebar_buttons = {}
        
        # Section PRODUCTION
        self.add_section_header(layout, "📊 PRODUCTION", "#e74c3c")
        production_items = [
            ('home', '🏠 Tableau de Bord', 'Vue d\'ensemble et statistiques'),
            ('tasks', '📋 Gestion des Tâches', 'Création et suivi des tâches'),
            ('reports', '📄 Rapports & Analyses', 'Génération de rapports')
        ]
        self.add_section_buttons(layout, production_items)
        
        layout.addSpacing(20)
        
        # Section MAINTENANCE
        self.add_section_header(layout, "🔧 MAINTENANCE", "#f39c12")
        maintenance_items = [
            ('maintenance', '🔧 Centre de Maintenance', 'Système complet de maintenance (NOUVEAU)'),
            ('equipment', '🔌 Gestion Équipements', 'Parc d\'équipements et maintenance'),
            ('spare_parts', '🔧 Pièces de Rechange', 'Inventaire et gestion du stock')
        ]
        self.add_section_buttons(layout, maintenance_items)
        
        layout.addSpacing(20)
        
        # Section PERSONNEL
        self.add_section_header(layout, "👥 PERSONNEL", "#27ae60")
        personnel_items = [
            ('personnel', '👤 Gestion Personnel', 'Base de données du personnel'),
            ('attendance', '📊 Pointage & Présences', 'Suivi des présences quotidiennes')
        ]
        self.add_section_buttons(layout, personnel_items)
        
        layout.addSpacing(20)
        
        # Section CONFIGURATION
        self.add_section_header(layout, "⚙️ CONFIGURATION", "#9b59b6")
        config_items = [
            ('settings', '⚙️ Paramètres', 'Configuration de l\'application'),
            ('backup', '💾 Sauvegarde', 'Sauvegarde et restauration'),
            ('help', '❓ Aide', 'Documentation et support')
        ]
        self.add_section_buttons(layout, config_items)
    
    def add_section_header(self, layout, title, color):
        """Ajoute un en-tête de section"""
        header = QLabel(title)
        header.setStyleSheet(f"""
            QLabel {{
                font-size: 14px;
                font-weight: bold;
                padding: 15px 20px 10px 20px;
                color: {color};
                background-color: transparent;
                border-bottom: 2px solid {color};
                margin: 8px 0 8px 0;
            }}
        """)
        layout.addWidget(header)
    
    def add_section_buttons(self, layout, items):
        """Ajoute les boutons d'une section"""
        for section_id, title, description in items:
            btn = QPushButton(title)
            btn.setToolTip(description)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: transparent;
                    color: #ecf0f1;
                    border: none;
                    padding: 14px 25px;
                    text-align: left;
                    font-size: 14px;
                    margin-left: 20px;
                    border-radius: 6px;
                }
                QPushButton:hover {
                    background-color: #34495e;
                    color: white;
                    border-left: 4px solid #3498db;
                }
                QPushButton:pressed {
                    background-color: #3498db;
                }
                QPushButton:checked {
                    background-color: #3498db;
                    border-left: 4px solid #2980b9;
                }
            """)
            btn.setCheckable(True)
            btn.clicked.connect(lambda checked, sid=section_id: self.navigate_to_section(sid))
            self.sidebar_buttons[section_id] = btn
            layout.addWidget(btn)
    
    def create_content_area(self):
        """Crée la zone de contenu principal"""
        content = QStackedWidget()
        
        # Page d'accueil
        self.home_page = self.create_home_page()
        content.addWidget(self.home_page)
        
        # Page de maintenance (nouveau)
        self.maintenance_page = self.create_maintenance_page()
        content.addWidget(self.maintenance_page)
        
        return content
    
    def create_home_page(self):
        """Crée la page d'accueil avec statistiques"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(40, 40, 40, 40)
        layout.setSpacing(30)
        
        # Titre de bienvenue
        welcome_frame = self.create_welcome_frame()
        layout.addWidget(welcome_frame)
        
        # Statistiques rapides
        stats_frame = self.create_stats_frame()
        layout.addWidget(stats_frame)
        
        # Actions rapides
        actions_frame = self.create_quick_actions_frame()
        layout.addWidget(actions_frame)
        
        # Actualités et notifications
        news_frame = self.create_news_frame()
        layout.addWidget(news_frame)
        
        layout.addStretch()
        
        return page
    
    def create_welcome_frame(self):
        """Crée le cadre de bienvenue"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 15px;
                padding: 30px;
                margin: 10px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(15)
        
        # Titre principal
        title = QLabel("Bienvenue dans SOTRAMINE PHOSPHATE")
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 32px;
                font-weight: bold;
                margin-bottom: 10px;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Sous-titre
        subtitle = QLabel("Système de Gestion de Maintenance Industrielle")
        subtitle.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.9);
                font-size: 18px;
                font-weight: 500;
            }
        """)
        subtitle.setAlignment(Qt.AlignCenter)
        layout.addWidget(subtitle)
        
        # Badge nouveau
        badge = QLabel("🆕 Menu Maintenance Développé Professionnellement")
        badge.setStyleSheet("""
            QLabel {
                background-color: #e74c3c;
                color: white;
                padding: 8px 16px;
                border-radius: 20px;
                font-size: 12px;
                font-weight: bold;
                margin-top: 10px;
            }
        """)
        badge.setAlignment(Qt.AlignCenter)
        layout.addWidget(badge)
        
        return frame
    
    def create_stats_frame(self):
        """Crée le cadre des statistiques"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 12px;
                padding: 25px;
                margin: 10px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        # Titre
        title = QLabel("📊 Tableau de Bord")
        title.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
        # Grille de statistiques
        stats_layout = QHBoxLayout()
        
        # Statistiques simulées (à remplacer par de vraies données)
        stats = [
            ("📋", "Tâches", "0", "#3498db"),
            ("🔌", "Équipements", "0", "#e67e22"),
            ("🔧", "Interventions", "0", "#e74c3c"),
            ("👥", "Personnel", "0", "#27ae60"),
            ("📊", "Rapports", "0", "#9b59b6"),
            ("⚠️", "Alertes", "0", "#f39c12")
        ]
        
        for icon, label, value, color in stats:
            stat_widget = self.create_stat_widget(icon, label, value, color)
            stats_layout.addWidget(stat_widget)
        
        layout.addLayout(stats_layout)
        
        return frame
    
    def create_stat_widget(self, icon, label, value, color):
        """Crée un widget de statistique"""
        widget = QFrame()
        widget.setStyleSheet(f"""
            QFrame {{
                background-color: {color}15;
                border: 2px solid {color};
                border-radius: 10px;
                padding: 20px;
                margin: 5px;
                min-width: 120px;
                min-height: 100px;
            }}
            QFrame:hover {{
                background-color: {color}25;
                transform: scale(1.05);
            }}
        """)
        
        layout = QVBoxLayout(widget)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(8)
        
        # Icône
        icon_label = QLabel(icon)
        icon_label.setStyleSheet(f"""
            QLabel {{
                font-size: 28px;
                color: {color};
            }}
        """)
        icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_label)
        
        # Valeur
        value_label = QLabel(value)
        value_label.setStyleSheet(f"""
            QLabel {{
                font-size: 24px;
                font-weight: bold;
                color: {color};
            }}
        """)
        value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(value_label)
        
        # Label
        label_widget = QLabel(label)
        label_widget.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #7f8c8d;
                font-weight: bold;
            }
        """)
        label_widget.setAlignment(Qt.AlignCenter)
        layout.addWidget(label_widget)
        
        return widget
    
    def create_quick_actions_frame(self):
        """Crée le cadre des actions rapides"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 12px;
                padding: 25px;
                margin: 10px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        # Titre
        title = QLabel("⚡ Actions Rapides")
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # Boutons d'actions
        actions_layout = QHBoxLayout()
        
        actions = [
            ("📋 Nouvelle Tâche", lambda: self.navigate_to_section('tasks'), "#3498db"),
            ("🔧 Centre Maintenance", lambda: self.navigate_to_section('maintenance'), "#f39c12"),
            ("🔌 Nouvel Équipement", lambda: self.navigate_to_section('equipment'), "#e67e22"),
            ("👤 Nouveau Personnel", lambda: self.navigate_to_section('personnel'), "#27ae60"),
            ("📊 Voir Rapports", lambda: self.navigate_to_section('reports'), "#9b59b6")
        ]
        
        for text, action, color in actions:
            btn = QPushButton(text)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 12px 16px;
                    font-size: 13px;
                    font-weight: bold;
                    min-width: 140px;
                    min-height: 45px;
                }}
                QPushButton:hover {{
                    background-color: {color}dd;
                    transform: translateY(-2px);
                }}
                QPushButton:pressed {{
                    background-color: {color}bb;
                    transform: translateY(0px);
                }}
            """)
            btn.clicked.connect(action)
            actions_layout.addWidget(btn)
        
        layout.addLayout(actions_layout)
        
        return frame
    
    def create_news_frame(self):
        """Crée le cadre des actualités"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 12px;
                padding: 25px;
                margin: 10px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        # Titre
        title = QLabel("📢 Actualités & Notifications")
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # Actualités
        news_items = [
            ("🆕", "Menu Maintenance développé professionnellement", "Nouveau système complet de gestion de maintenance industrielle"),
            ("✅", "Base de données optimisée", "Performances améliorées avec index et optimisations"),
            ("🎨", "Interface modernisée", "Design professionnel avec navigation intuitive"),
            ("🔄", "Actualisation automatique", "Synchronisation temps réel entre tous les modules")
        ]
        
        for icon, title_text, description in news_items:
            news_item = self.create_news_item(icon, title_text, description)
            layout.addWidget(news_item)
        
        return frame
    
    def create_news_item(self, icon, title_text, description):
        """Crée un élément d'actualité"""
        item = QFrame()
        item.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-left: 4px solid #3498db;
                border-radius: 6px;
                padding: 12px;
                margin: 5px 0;
            }
            QFrame:hover {
                background-color: #e9ecef;
            }
        """)
        
        layout = QHBoxLayout(item)
        layout.setSpacing(12)
        
        # Icône
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                min-width: 30px;
            }
        """)
        layout.addWidget(icon_label)
        
        # Contenu
        content_layout = QVBoxLayout()
        content_layout.setSpacing(4)
        
        # Titre
        title_label = QLabel(title_text)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
            }
        """)
        content_layout.addWidget(title_label)
        
        # Description
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #6c757d;
            }
        """)
        content_layout.addWidget(desc_label)
        
        layout.addLayout(content_layout)
        layout.addStretch()
        
        return item

    def create_maintenance_page(self):
        """Crée la page de maintenance professionnelle"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # En-tête maintenance
        header = self.create_maintenance_header()
        layout.addWidget(header)

        # Contenu maintenance avec onglets
        maintenance_content = self.create_maintenance_content()
        layout.addWidget(maintenance_content)

        return page

    def create_maintenance_header(self):
        """Crée l'en-tête de la section maintenance"""
        header = QFrame()
        header.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f39c12, stop:1 #e67e22);
                border-radius: 0;
                padding: 25px;
            }
        """)
        header.setFixedHeight(100)

        layout = QHBoxLayout(header)
        layout.setContentsMargins(30, 20, 30, 20)

        # Titre et description
        title_layout = QVBoxLayout()
        title_layout.setSpacing(5)

        title = QLabel("🔧 CENTRE DE MAINTENANCE PROFESSIONNEL")
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 28px;
                font-weight: bold;
                margin: 0;
            }
        """)
        title_layout.addWidget(title)

        subtitle = QLabel("Système complet de gestion de maintenance industrielle")
        subtitle.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.9);
                font-size: 16px;
                margin: 0;
            }
        """)
        title_layout.addWidget(subtitle)

        layout.addLayout(title_layout)
        layout.addStretch()

        # Actions rapides
        actions_layout = QHBoxLayout()
        actions_layout.setSpacing(15)

        quick_actions = [
            ("🚨 Intervention Urgente", "#e74c3c"),
            ("📋 Nouveau Bon de Travail", "#3498db"),
            ("📅 Planifier Maintenance", "#27ae60")
        ]

        for text, color in quick_actions:
            btn = QPushButton(text)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 12px 18px;
                    font-weight: bold;
                    font-size: 13px;
                    min-width: 160px;
                }}
                QPushButton:hover {{
                    background-color: {color}dd;
                }}
                QPushButton:pressed {{
                    background-color: {color}bb;
                }}
            """)
            btn.clicked.connect(lambda checked, t=text: self.maintenance_action(t))
            actions_layout.addWidget(btn)

        layout.addLayout(actions_layout)

        return header

    def create_maintenance_content(self):
        """Crée le contenu de maintenance avec onglets"""
        tab_widget = QTabWidget()
        tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dcdde1;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f8f9fa;
                border: 1px solid #dcdde1;
                padding: 15px 25px;
                margin-right: 2px;
                font-weight: bold;
                min-width: 140px;
                font-size: 14px;
            }
            QTabBar::tab:selected {
                background-color: #f39c12;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #e67e22;
                color: white;
            }
        """)

        # Onglets de maintenance
        tabs = [
            ("🔌 Équipements", self.create_equipment_tab()),
            ("🔧 Interventions", self.create_interventions_tab()),
            ("📅 Planification", self.create_planning_tab()),
            ("🔧 Pièces", self.create_parts_tab()),
            ("👷 Techniciens", self.create_technicians_tab()),
            ("📊 Historique", self.create_history_tab())
        ]

        for title, content in tabs:
            tab_widget.addTab(content, title)

        return tab_widget

    def create_equipment_tab(self):
        """Crée l'onglet de gestion des équipements"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)

        # Titre
        title = QLabel("🔌 Gestion Avancée des Équipements")
        title.setStyleSheet("""
            QLabel {
                font-size: 22px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #e3f2fd;
                border-radius: 10px;
                border-left: 5px solid #3498db;
            }
        """)
        layout.addWidget(title)

        # Description
        desc = QLabel("Gérez votre parc d'équipements avec des fonctionnalités avancées :\n"
                     "• Fiche complète par équipement avec historique\n"
                     "• Planification de maintenance préventive\n"
                     "• Gestion des documents techniques\n"
                     "• Suivi des interventions et coûts")
        desc.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #495057;
                padding: 20px;
                background-color: #f8f9fa;
                border-radius: 8px;
                line-height: 1.6;
            }
        """)
        layout.addWidget(desc)

        # Boutons d'actions
        actions_layout = QHBoxLayout()

        equipment_actions = [
            ("🔌 Gérer les Équipements", "#3498db", lambda: self.navigate_to_section('equipment')),
            ("➕ Nouvel Équipement", "#28a745", self.add_equipment),
            ("📊 Statistiques", "#17a2b8", self.show_equipment_stats),
            ("📄 Export Excel", "#6f42c1", self.export_equipment)
        ]

        for text, color, action in equipment_actions:
            btn = QPushButton(text)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 15px 20px;
                    font-size: 14px;
                    font-weight: bold;
                    min-width: 160px;
                }}
                QPushButton:hover {{
                    background-color: {color}dd;
                }}
            """)
            btn.clicked.connect(action)
            actions_layout.addWidget(btn)

        layout.addLayout(actions_layout)
        layout.addStretch()

        return tab

    def create_interventions_tab(self):
        """Crée l'onglet des interventions"""
        return self.create_maintenance_tab("🔧 Interventions et Bons de Travail",
                                         "Gérez les interventions de maintenance avec un système complet de bons de travail",
                                         "#e74c3c")

    def create_planning_tab(self):
        """Crée l'onglet de planification"""
        return self.create_maintenance_tab("📅 Planification Préventive",
                                         "Planifiez et automatisez la maintenance préventive de vos équipements",
                                         "#9b59b6")

    def create_parts_tab(self):
        """Crée l'onglet des pièces"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)

        # Titre
        title = QLabel("🔧 Gestion des Pièces de Rechange")
        title.setStyleSheet("""
            QLabel {
                font-size: 22px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: #fff3e0;
                border-radius: 10px;
                border-left: 5px solid #ff9800;
            }
        """)
        layout.addWidget(title)

        # Bouton d'action principal
        btn = QPushButton("🔧 Gérer les Pièces de Rechange")
        btn.setStyleSheet("""
            QPushButton {
                background-color: #ff9800;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 15px 25px;
                font-size: 16px;
                font-weight: bold;
                min-width: 250px;
            }
            QPushButton:hover {
                background-color: #f57c00;
            }
        """)
        btn.clicked.connect(lambda: self.navigate_to_section('spare_parts'))
        layout.addWidget(btn)

        layout.addStretch()

        return tab

    def create_technicians_tab(self):
        """Crée l'onglet des techniciens"""
        return self.create_maintenance_tab("👷 Gestion des Techniciens",
                                         "Gérez votre équipe de maintenance avec spécialisations et planning",
                                         "#27ae60")

    def create_history_tab(self):
        """Crée l'onglet d'historique"""
        return self.create_maintenance_tab("📊 Historique et Indicateurs",
                                         "Consultez l'historique complet et les KPIs de maintenance",
                                         "#17a2b8")

    def create_maintenance_tab(self, title_text, description, color):
        """Crée un onglet de maintenance générique"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)

        # Titre
        title = QLabel(title_text)
        title.setStyleSheet(f"""
            QLabel {{
                font-size: 22px;
                font-weight: bold;
                color: #2c3e50;
                padding: 15px;
                background-color: {color}20;
                border-radius: 10px;
                border-left: 5px solid {color};
            }}
        """)
        layout.addWidget(title)

        # Description
        desc = QLabel(description)
        desc.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #495057;
                padding: 20px;
                background-color: #f8f9fa;
                border-radius: 8px;
            }
        """)
        layout.addWidget(desc)

        # Message de développement
        dev_msg = QLabel("🚧 Module en cours de développement\n\nCette fonctionnalité sera disponible dans une prochaine version.")
        dev_msg.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #6c757d;
                padding: 20px;
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 8px;
                text-align: center;
            }
        """)
        dev_msg.setAlignment(Qt.AlignCenter)
        layout.addWidget(dev_msg)

        layout.addStretch()

        return tab

    def setup_auto_refresh(self):
        """Configure l'actualisation automatique"""
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_data)
        self.refresh_timer.start(30000)  # Actualisation toutes les 30 secondes
        print("✓ Actualisation automatique activée")

    def refresh_data(self):
        """Actualise les données"""
        # Actualisation silencieuse des données
        pass

    # Méthodes de navigation
    def navigate_to_section(self, section_id):
        """Navigue vers une section"""
        # Désélectionner tous les boutons
        for btn in self.sidebar_buttons.values():
            btn.setChecked(False)

        # Sélectionner le bouton actuel
        if section_id in self.sidebar_buttons:
            self.sidebar_buttons[section_id].setChecked(True)

        # Navigation selon la section
        if section_id == 'home':
            self.show_home()
        elif section_id == 'maintenance':
            self.show_maintenance()
        elif section_id == 'tasks':
            self.show_tasks()
        elif section_id == 'equipment':
            self.show_equipment()
        elif section_id == 'spare_parts':
            self.show_spare_parts()
        elif section_id == 'personnel':
            self.show_personnel()
        elif section_id == 'attendance':
            self.show_attendance()
        elif section_id == 'reports':
            self.show_reports()
        elif section_id == 'settings':
            self.show_settings()
        elif section_id == 'backup':
            self.show_backup()
        elif section_id == 'help':
            self.show_help()

    def show_home(self):
        """Affiche la page d'accueil"""
        self.content_area.setCurrentWidget(self.home_page)
        self.setWindowTitle("SOTRAMINE PHOSPHATE - Tableau de Bord")

    def show_maintenance(self):
        """Affiche la page de maintenance"""
        self.content_area.setCurrentWidget(self.maintenance_page)
        self.setWindowTitle("SOTRAMINE PHOSPHATE - Centre de Maintenance")

    def show_tasks(self):
        """Affiche la gestion des tâches"""
        QMessageBox.information(self, "Gestion des Tâches",
                               "📋 Module de gestion des tâches\n\n"
                               "Fonctionnalités :\n"
                               "• Création et suivi des tâches\n"
                               "• Assignation au personnel\n"
                               "• Suivi des échéances\n"
                               "• Rapports de progression\n\n"
                               "Module en cours de développement.")

    def show_equipment(self):
        """Affiche la gestion des équipements"""
        QMessageBox.information(self, "Gestion des Équipements",
                               "🔌 Module de gestion des équipements\n\n"
                               "Fonctionnalités :\n"
                               "• Fiche complète par équipement\n"
                               "• Historique des interventions\n"
                               "• Documents techniques\n"
                               "• Planification maintenance\n\n"
                               "Module en cours de développement.")

    def show_spare_parts(self):
        """Affiche la gestion des pièces"""
        QMessageBox.information(self, "Pièces de Rechange",
                               "🔧 Module de gestion des pièces\n\n"
                               "Fonctionnalités :\n"
                               "• Inventaire complet\n"
                               "• Alertes de stock\n"
                               "• Suivi des consommations\n"
                               "• Gestion des fournisseurs\n\n"
                               "Module en cours de développement.")

    def show_personnel(self):
        """Affiche la gestion du personnel"""
        QMessageBox.information(self, "Gestion du Personnel",
                               "👤 Module de gestion du personnel\n\n"
                               "Fonctionnalités :\n"
                               "• Base de données personnel\n"
                               "• Compétences et formations\n"
                               "• Planning des équipes\n"
                               "• Évaluations\n\n"
                               "Module en cours de développement.")

    def show_attendance(self):
        """Affiche le pointage"""
        QMessageBox.information(self, "Pointage et Présences",
                               "📊 Module de pointage\n\n"
                               "Fonctionnalités :\n"
                               "• Suivi quotidien des présences\n"
                               "• Gestion des congés\n"
                               "• Calcul des heures\n"
                               "• Rapports mensuels\n\n"
                               "Module en cours de développement.")

    def show_reports(self):
        """Affiche les rapports"""
        QMessageBox.information(self, "Rapports et Analyses",
                               "📄 Module de rapports\n\n"
                               "Fonctionnalités :\n"
                               "• Rapports de maintenance\n"
                               "• Analyses de performance\n"
                               "• Tableaux de bord\n"
                               "• Export Excel/PDF\n\n"
                               "Module en cours de développement.")

    def show_settings(self):
        """Affiche les paramètres"""
        QMessageBox.information(self, "Paramètres",
                               "⚙️ Configuration de l'application\n\n"
                               "Options disponibles :\n"
                               "• Paramètres généraux\n"
                               "• Configuration base de données\n"
                               "• Préférences utilisateur\n"
                               "• Thèmes et apparence\n\n"
                               "Module en cours de développement.")

    def show_backup(self):
        """Affiche la sauvegarde"""
        QMessageBox.information(self, "Sauvegarde",
                               "💾 Sauvegarde et restauration\n\n"
                               "Fonctionnalités :\n"
                               "• Sauvegarde automatique\n"
                               "• Export des données\n"
                               "• Restauration\n"
                               "• Archivage\n\n"
                               "Module en cours de développement.")

    def show_help(self):
        """Affiche l'aide"""
        QMessageBox.information(self, "Aide et Support",
                               "❓ Documentation et support\n\n"
                               "Ressources disponibles :\n"
                               "• Guide utilisateur\n"
                               "• Tutoriels vidéo\n"
                               "• FAQ\n"
                               "• Support technique\n\n"
                               "Module en cours de développement.")

    # Méthodes d'actions
    def maintenance_action(self, action_text):
        """Gère les actions rapides de maintenance"""
        QMessageBox.information(self, "Action Maintenance",
                               f"🔧 {action_text}\n\n"
                               "Cette fonctionnalité sera implémentée dans une prochaine version.")

    def add_equipment(self):
        """Ajoute un nouvel équipement"""
        QMessageBox.information(self, "Nouvel Équipement",
                               "🔌 Ajout d'un nouvel équipement\n\n"
                               "Cette fonctionnalité ouvrira un formulaire complet pour :\n"
                               "• Saisir les informations techniques\n"
                               "• Attacher des documents\n"
                               "• Planifier la maintenance\n"
                               "• Configurer les alertes\n\n"
                               "Module en cours de développement.")

    def show_equipment_stats(self):
        """Affiche les statistiques des équipements"""
        QMessageBox.information(self, "Statistiques Équipements",
                               "📊 Statistiques des équipements\n\n"
                               "Analyses disponibles :\n"
                               "• Répartition par type\n"
                               "• État de maintenance\n"
                               "• Coûts par équipement\n"
                               "• Taux de disponibilité\n\n"
                               "Module en cours de développement.")

    def export_equipment(self):
        """Exporte la liste des équipements"""
        if self.excel_exporter:
            try:
                file_path = "export_equipements.xlsx"
                success = self.excel_exporter.export_equipment_list(file_path)
                if success:
                    QMessageBox.information(self, "Export Réussi",
                                           f"📄 Export Excel réussi !\n\nFichier sauvegardé : {file_path}")
                else:
                    QMessageBox.warning(self, "Erreur Export",
                                       "❌ Erreur lors de l'export Excel.\n\nVérifiez que le module openpyxl est installé.")
            except Exception as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de l'export : {str(e)}")
        else:
            QMessageBox.warning(self, "Export Indisponible",
                               "❌ Exporteur Excel non disponible.\n\nVérifiez la configuration de la base de données.")

def main():
    """Point d'entrée principal"""
    print("🚀 LANCEMENT SOTRAMINE PHOSPHATE - APPLICATION PRINCIPALE")
    print("=" * 60)

    # Créer l'application Qt
    app = QApplication(sys.argv)
    print("✓ Application Qt créée")

    # Créer la fenêtre principale
    window = SotramineMainApp()
    print("✓ Interface utilisateur créée")

    # Afficher la fenêtre
    window.show()
    print("✓ Application affichée")

    print("\n🎉 APPLICATION PRINCIPALE LANCÉE AVEC SUCCÈS !")
    print("📋 Fonctionnalités disponibles :")
    print("   📊 PRODUCTION : Tableau de bord, Tâches, Rapports")
    print("   🔧 MAINTENANCE : Centre complet avec 6 modules spécialisés")
    print("   👥 PERSONNEL : Gestion personnel et pointage")
    print("   ⚙️ CONFIGURATION : Paramètres, sauvegarde, aide")
    print("\n✨ Interface moderne avec menu maintenance professionnel")
    print("🔄 Actualisation automatique activée")
    print("🗄️ Base de données optimisée")

    # Lancer la boucle d'événements
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
