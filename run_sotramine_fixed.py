#!/usr/bin/env python3
"""
SCRIPT DE LANCEMENT POUR SOTRAMINE PHOSPHATE VERSION CORRIGÉE
Application complète avec tous les modules fonctionnels
Affichage parfait et toutes les fonctionnalités demandées
"""

import sys
import os

def main():
    """Lance l'application SOTRAMINE PHOSPHATE version corrigée"""
    print("🚀 LANCEMENT SOTRAMINE PHOSPHATE - VERSION CORRIGÉE")
    print("=" * 60)
    print("✅ Tous les modules vraiment fonctionnels")
    print("🎨 Affichage parfait et optimisé")
    print("🔧 Toutes les fonctionnalités demandées")
    print("=" * 60)
    
    try:
        # Importer et lancer l'application corrigée
        from sotramine_complete_fixed import main as app_main
        app_main()
        
    except ImportError as e:
        print(f"❌ Erreur d'import : {str(e)}")
        print("🔧 Vérifiez que tous les modules sont présents")
        
        # Fallback vers l'application finale
        try:
            print("🔄 Tentative de lancement de l'application finale...")
            from sotramine_app_final import main as fallback_main
            fallback_main()
        except Exception as fallback_error:
            print(f"❌ Erreur fallback : {str(fallback_error)}")
            print("💡 Vérifiez l'installation de PyQt5")
        
    except Exception as e:
        print(f"❌ Erreur lors du lancement : {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
