#!/usr/bin/env python3
"""
LANCEUR POUR SOTRAMINE PHOSPHATE - VERSION PROFESSIONNELLE
Application avec design professionnel et thème moderne optimisé
"""

import sys
import os

def main():
    """Lance l'application SOTRAMINE PHOSPHATE version professionnelle"""
    print("🎨 LANCEMENT SOTRAMINE PHOSPHATE - VERSION PROFESSIONNELLE")
    print("=" * 80)
    print("🏭 Système Professionnel de Gestion de Maintenance Industrielle")
    print("🎨 Design moderne avec thème professionnel optimisé")
    print("💼 Interface élégante pour environnement industriel")
    print("⚡ Performance optimisée et responsive")
    print("=" * 80)
    
    try:
        # Vérifier les dépendances
        print("🔍 Vérification des dépendances professionnelles...")
        
        try:
            import PyQt5
            from PyQt5.QtWidgets import QApplication
            from PyQt5.QtCore import Qt
            from PyQt5.QtGui import QFont, QPalette
            print("✅ PyQt5 disponible avec modules complets")
        except ImportError as e:
            print(f"❌ PyQt5 non trouvé : {e}")
            print("💡 Installation requise : pip install PyQt5")
            return 1
        
        # Vérifier la version Python
        if sys.version_info < (3, 6):
            print("❌ Python 3.6+ requis pour la version professionnelle")
            return 1
        
        print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} compatible")
        
        # Importer et lancer l'application professionnelle
        print("📦 Chargement de l'application professionnelle...")
        from sotramine_professional import main as app_main
        
        print("🎨 Initialisation du thème professionnel...")
        print("🎯 Configuration de l'interface moderne...")
        print("⚡ Optimisation des performances...")
        
        print("\n🎉 Lancement de l'application professionnelle...")
        app_main()
        
    except ImportError as e:
        print(f"❌ Erreur d'import : {str(e)}")
        print("🔧 Vérifiez que les fichiers requis existent :")
        print("   - sotramine_professional.py")
        print("   - run_professional_app.py (ce fichier)")
        print("\n💡 Assurez-vous que tous les modules sont dans le même répertoire")
        return 1
        
    except Exception as e:
        print(f"❌ Erreur lors du lancement professionnel : {str(e)}")
        print("\n🔧 Informations de débogage :")
        import traceback
        traceback.print_exc()
        
        print("\n📋 Vérifications suggérées :")
        print("   1. Vérifiez que PyQt5 est correctement installé")
        print("   2. Vérifiez les permissions d'accès aux fichiers")
        print("   3. Vérifiez que le système supporte l'interface graphique")
        print("   4. Redémarrez l'application si nécessaire")
        
        return 1

if __name__ == "__main__":
    exit_code = main()
    if exit_code:
        print(f"\n❌ Application fermée avec le code d'erreur : {exit_code}")
        input("Appuyez sur Entrée pour fermer...")
        sys.exit(exit_code)
    else:
        print("\n✅ Application fermée normalement")
