#!/usr/bin/env python3
"""
LANCEUR POUR SOTRAMINE PHOSPHATE - VERSION FONCTIONNELLE
Version corrigée et testée qui fonctionne
"""

import sys
import os

def main():
    """Lance l'application SOTRAMINE PHOSPHATE fonctionnelle"""
    print("🚀 LANCEMENT SOTRAMINE PHOSPHATE - VERSION FONCTIONNELLE")
    print("=" * 60)
    print("🏭 Système de Gestion de Maintenance Industrielle")
    print("✅ Version corrigée et testée")
    print("🔧 Interface moderne et stable")
    print("=" * 60)
    
    try:
        # Vérifier PyQt5
        print("🔍 Vérification des dépendances...")
        
        try:
            import PyQt5
            from PyQt5.QtWidgets import QApplication
            print("✅ PyQt5 disponible")
        except ImportError as e:
            print(f"❌ PyQt5 non trouvé : {e}")
            print("💡 Installation : pip install PyQt5")
            return 1
        
        # Importer et lancer l'application
        print("📦 Chargement de l'application...")
        from sotramine_working import main as app_main
        
        print("🎨 Initialisation de l'interface...")
        print("🎉 Lancement de l'application...")
        
        # Lancer l'application
        app_main()
        
    except ImportError as e:
        print(f"❌ Erreur d'import : {str(e)}")
        print("🔧 Vérifiez que le fichier sotramine_working.py existe")
        return 1
        
    except Exception as e:
        print(f"❌ Erreur : {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    if exit_code:
        print(f"\n❌ Erreur : {exit_code}")
        input("Appuyez sur Entrée pour fermer...")
        sys.exit(exit_code)
