#!/usr/bin/env python3
"""
Lanceur principal pour l'application SOTRAMINE PHOSPHATE
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap, QFont

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def show_splash_screen(app):
    """Affiche l'écran de démarrage"""
    try:
        # Créer un écran de démarrage simple
        splash_pix = QPixmap(400, 300)
        splash_pix.fill(Qt.white)
        
        splash = QSplashScreen(splash_pix, Qt.WindowStaysOnTopHint)
        splash.setMask(splash_pix.mask())
        
        # Ajouter du texte
        splash.showMessage(
            "SOTRAMINE PHOSPHATE\nSystème de Gestion de Maintenance\n\nChargement...",
            Qt.AlignCenter | Qt.<PERSON>,
            Qt.black
        )
        
        splash.show()
        app.processEvents()
        
        return splash
    except:
        return None

def launch_modern_interface():
    """Lance l'interface moderne"""
    try:
        from test_simple_ui import SimpleModernWindow
        from database import Database
        from export.excel_export import ExcelExporter
        
        print("🚀 LANCEMENT DE SOTRAMINE PHOSPHATE")
        print("=" * 50)
        
        # Créer l'application Qt
        app = QApplication(sys.argv)
        app.setApplicationName("SOTRAMINE PHOSPHATE")
        app.setApplicationVersion("2.1")
        
        # Configuration pour les écrans haute résolution
        app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        print("✓ Application Qt initialisée")
        
        # Afficher l'écran de démarrage
        splash = show_splash_screen(app)
        
        # Initialiser la base de données
        print("📊 Initialisation de la base de données...")
        if splash:
            splash.showMessage("Initialisation de la base de données...", Qt.AlignCenter | Qt.AlignBottom, Qt.black)
            app.processEvents()
        
        db = Database()
        print("✓ Base de données connectée")
        
        # Initialiser l'exporteur Excel
        print("📄 Initialisation de l'exporteur Excel...")
        if splash:
            splash.showMessage("Initialisation de l'exporteur Excel...", Qt.AlignCenter | Qt.AlignBottom, Qt.black)
            app.processEvents()
        
        excel_exporter = ExcelExporter(db)
        print("✓ Exporteur Excel initialisé")
        
        # Créer la fenêtre principale
        print("🎨 Création de l'interface...")
        if splash:
            splash.showMessage("Création de l'interface...", Qt.AlignCenter | Qt.AlignBottom, Qt.black)
            app.processEvents()
        
        main_window = SimpleModernWindow(db, excel_exporter)
        
        # Fermer l'écran de démarrage
        if splash:
            splash.finish(main_window)
        
        # Afficher la fenêtre principale
        main_window.show()
        main_window.raise_()
        main_window.activateWindow()
        
        print("✓ Interface affichée")
        print("\n🎉 APPLICATION LANCÉE AVEC SUCCÈS !")
        print("=" * 50)
        print("Fonctionnalités disponibles :")
        print("• Menu latéral rétractable (bouton ☰)")
        print("• Navigation entre sections")
        print("• Barre d'outils contextuelle")
        print("• Actions rapides")
        print("• Recherche intégrée")
        print("• Tableau de bord interactif")
        print("=" * 50)
        
        # Lancer la boucle d'événements
        return app.exec_()
        
    except Exception as e:
        print(f"\n❌ ERREUR LORS DU LANCEMENT : {str(e)}")
        import traceback
        traceback.print_exc()
        
        # Afficher une boîte de dialogue d'erreur
        try:
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)
            
            QMessageBox.critical(
                None,
                "Erreur de lancement",
                f"Impossible de lancer SOTRAMINE PHOSPHATE :\n\n{str(e)}\n\nVeuillez vérifier les logs pour plus de détails."
            )
        except:
            pass
        
        return 1

def launch_original_interface():
    """Lance l'interface originale"""
    try:
        print("🔄 Tentative de lancement de l'interface originale...")
        
        from main import main as original_main
        return original_main()
        
    except Exception as e:
        print(f"❌ Erreur interface originale : {str(e)}")
        return 1

def check_dependencies():
    """Vérifie les dépendances nécessaires"""
    try:
        import PyQt5
        import sqlite3
        import pandas
        import openpyxl
        print("✓ Toutes les dépendances sont disponibles")
        return True
    except ImportError as e:
        print(f"❌ Dépendance manquante : {str(e)}")
        print("Installez les dépendances avec : pip install -r requirements.txt")
        return False

def main():
    """Point d'entrée principal"""
    print("🏭 SOTRAMINE PHOSPHATE - Système de Gestion de Maintenance")
    print("Version 2.1 - Interface Moderne")
    print("=" * 60)
    
    # Vérifier les dépendances
    if not check_dependencies():
        return 1
    
    # Vérifier les arguments de ligne de commande
    if len(sys.argv) > 1:
        if sys.argv[1] == "--original":
            print("🔄 Lancement de l'interface originale...")
            return launch_original_interface()
        elif sys.argv[1] == "--help":
            print("Usage:")
            print("  python launch_app.py          # Interface moderne (par défaut)")
            print("  python launch_app.py --original  # Interface originale")
            print("  python launch_app.py --help      # Afficher cette aide")
            return 0
    
    # Lancer l'interface moderne par défaut
    return launch_modern_interface()

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n👋 Application fermée par l'utilisateur")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Erreur critique : {str(e)}")
        sys.exit(1)
