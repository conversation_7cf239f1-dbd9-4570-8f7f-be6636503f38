#!/usr/bin/env python3
"""
Test d'import des modules de maintenance un par un
"""

import sys
import traceback

print("🔍 Test d'import des modules de maintenance")
print("=" * 50)

modules_to_test = [
    ("PyQt5.QtWidgets", "from PyQt5.QtWidgets import QWidget"),
    ("database", "from database import Database"),
    ("gui.interventions_management", "from gui.interventions_management import InterventionsManagement"),
    ("gui.preventive_maintenance", "from gui.preventive_maintenance import PreventiveMaintenance"),
    ("gui.maintenance_history", "from gui.maintenance_history import MaintenanceHistoryView"),
    ("gui.maintenance_checklist", "from gui.maintenance_checklist import MaintenanceChecklistView"),
    ("gui.work_orders_manager", "from gui.work_orders_manager import WorkOrdersManager"),
    ("gui.maintenance_indicators", "from gui.maintenance_indicators import MaintenanceIndicators"),
    ("gui.maintenance_menu", "from gui.maintenance_menu import MaintenanceMenu")
]

for module_name, import_statement in modules_to_test:
    try:
        print(f"📦 Test: {module_name}...")
        exec(import_statement)
        print(f"✅ {module_name} importé avec succès")
    except Exception as e:
        print(f"❌ Erreur {module_name}: {e}")
        traceback.print_exc()
        print("-" * 30)

print("\n🎯 Test terminé !")
