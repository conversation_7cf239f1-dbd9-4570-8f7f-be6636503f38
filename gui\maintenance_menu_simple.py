"""
Menu Maintenance Principal Simplifié
Version simplifiée pour éviter les conflits d'import
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
                             QLabel, QFrame, QSplitter, QGroupBox, QPushButton,
                             QTableWidget, QTableWidgetItem, QHeaderView, QProgressBar,
                             QComboBox, QDateEdit, QLineEdit, QTextEdit, QSpinBox,
                             QMessageBox, QDialog, QFormLayout, QGridLayout)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QDate, QDateTime
from PyQt5.QtGui import QFont, QColor, QPixmap, QIcon

from gui.interventions_management_simple import InterventionsManagement

class MaintenanceMenu(QWidget):
    """Menu principal de maintenance simplifié"""
    
    # Signaux pour la communication avec la fenêtre principale
    maintenance_updated = pyqtSignal()
    intervention_created = pyqtSignal(int)
    work_order_created = pyqtSignal(int)
    
    def __init__(self, db, parent=None):
        super().__init__(parent)
        self.db = db
        self.setup_ui()
        self.load_dashboard_data()
        
        # Timer pour actualisation automatique
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_dashboard)
        self.refresh_timer.start(300000)  # 5 minutes
    
    def setup_ui(self):
        """Configure l'interface utilisateur principale"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # En-tête du menu maintenance
        header = self.create_header()
        layout.addWidget(header)
        
        # Tableau de bord rapide
        dashboard = self.create_dashboard()
        layout.addWidget(dashboard)
        
        # Onglets principaux
        self.main_tabs = QTabWidget()
        self.main_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dcdde1;
                background-color: white;
                border-radius: 8px;
            }
            QTabBar::tab {
                background-color: #f8f9fa;
                border: 1px solid #dcdde1;
                padding: 12px 20px;
                margin-right: 2px;
                font-weight: bold;
                min-width: 180px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
                border-bottom: 2px solid #2980b9;
            }
            QTabBar::tab:hover {
                background-color: #e3f2fd;
                color: #1976d2;
            }
        """)
        
        # Onglet Interventions et Bons de Travail
        self.interventions_tab = InterventionsManagement(self.db, self)
        self.main_tabs.addTab(self.interventions_tab, "🔧 Interventions & Bons de Travail")
        
        # Onglet Planification Préventive (simplifié)
        self.preventive_tab = self.create_preventive_tab()
        self.main_tabs.addTab(self.preventive_tab, "📅 Planification Préventive")
        
        # Onglet Bons de Travail (simplifié)
        self.work_orders_tab = self.create_work_orders_tab()
        self.main_tabs.addTab(self.work_orders_tab, "📋 Bons de Travail")
        
        # Onglet Historique et Indicateurs (simplifié)
        self.history_tab = self.create_history_tab()
        self.main_tabs.addTab(self.history_tab, "📊 Historique & Indicateurs")
        
        # Onglet Indicateurs Avancés (simplifié)
        self.indicators_tab = self.create_indicators_tab()
        self.main_tabs.addTab(self.indicators_tab, "📈 Indicateurs Avancés")
        
        layout.addWidget(self.main_tabs)
        
        # Connecter les signaux
        self.interventions_tab.intervention_created.connect(self.on_intervention_created)
        self.interventions_tab.work_order_created.connect(self.on_work_order_created)
    
    def create_header(self):
        """Crée l'en-tête du menu maintenance"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        header_layout = QVBoxLayout(header_frame)
        
        # Titre principal
        title = QLabel("🔧 MENU DE MAINTENANCE")
        title.setStyleSheet("""
            font-size: 28px; font-weight: bold; color: white; 
            margin-bottom: 10px; text-align: center;
        """)
        title.setAlignment(Qt.AlignCenter)
        
        # Sous-titre
        subtitle = QLabel("Gestion complète des interventions, planification et indicateurs")
        subtitle.setStyleSheet("""
            font-size: 16px; color: #ecf0f1; text-align: center;
        """)
        subtitle.setAlignment(Qt.AlignCenter)
        
        header_layout.addWidget(title)
        header_layout.addWidget(subtitle)
        
        return header_frame
    
    def create_dashboard(self):
        """Crée le tableau de bord rapide"""
        dashboard_frame = QFrame()
        dashboard_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #dcdde1;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        dashboard_layout = QVBoxLayout(dashboard_frame)
        
        # Titre du tableau de bord
        dashboard_title = QLabel("📊 Tableau de Bord Rapide")
        dashboard_title.setStyleSheet("font-size: 20px; font-weight: bold; color: #2c3e50; margin-bottom: 15px;")
        dashboard_layout.addWidget(dashboard_title)
        
        # KPIs
        kpis_layout = QHBoxLayout()
        
        # KPI 1: Interventions en cours
        kpi1 = self.create_kpi_widget("🔧 Interventions en cours", "12", "#3498db")
        kpis_layout.addWidget(kpi1)
        
        # KPI 2: Bons de travail ouverts
        kpi2 = self.create_kpi_widget("📋 Bons de travail", "8", "#e74c3c")
        kpis_layout.addWidget(kpi2)
        
        # KPI 3: Planifiées cette semaine
        kpi3 = self.create_kpi_widget("📅 Planifiées", "15", "#27ae60")
        kpis_layout.addWidget(kpi3)
        
        # KPI 4: Urgentes
        kpi4 = self.create_kpi_widget("⚠️ Urgentes", "3", "#f39c12")
        kpis_layout.addWidget(kpi4)
        
        dashboard_layout.addLayout(kpis_layout)
        
        # Actions rapides
        actions_layout = QHBoxLayout()
        
        btn_new_intervention = QPushButton("➕ Nouvelle Intervention")
        btn_new_intervention.setStyleSheet("""
            QPushButton { 
                background-color: #3498db; color: white; border: none; 
                padding: 10px 20px; border-radius: 6px; font-weight: bold; 
            } 
            QPushButton:hover { background-color: #2980b9; }
        """)
        btn_new_intervention.clicked.connect(self.create_new_intervention)
        
        btn_new_work_order = QPushButton("📋 Nouveau Bon de Travail")
        btn_new_work_order.setStyleSheet("""
            QPushButton { 
                background-color: #e74c3c; color: white; border: none; 
                padding: 10px 20px; border-radius: 6px; font-weight: bold; 
            } 
            QPushButton:hover { background-color: #c0392b; }
        """)
        btn_new_work_order.clicked.connect(self.create_new_work_order)
        
        btn_schedule = QPushButton("📅 Planifier Maintenance")
        btn_schedule.setStyleSheet("""
            QPushButton { 
                background-color: #27ae60; color: white; border: none; 
                padding: 10px 20px; border-radius: 6px; font-weight: bold; 
            } 
            QPushButton:hover { background-color: #229954; }
        """)
        btn_schedule.clicked.connect(self.schedule_maintenance)
        
        actions_layout.addWidget(btn_new_intervention)
        actions_layout.addWidget(btn_new_work_order)
        actions_layout.addWidget(btn_schedule)
        
        dashboard_layout.addLayout(actions_layout)
        
        return dashboard_frame
    
    def create_kpi_widget(self, title, value, color):
        """Crée un widget KPI"""
        kpi_frame = QFrame()
        kpi_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {color};
                border-radius: 8px;
                padding: 15px;
                min-width: 120px;
            }}
        """)
        
        kpi_layout = QVBoxLayout(kpi_frame)
        
        kpi_value = QLabel(value)
        kpi_value.setStyleSheet("font-size: 24px; font-weight: bold; color: white; text-align: center;")
        kpi_value.setAlignment(Qt.AlignCenter)
        
        kpi_title = QLabel(title)
        kpi_title.setStyleSheet("font-size: 12px; color: white; text-align: center;")
        kpi_title.setAlignment(Qt.AlignCenter)
        kpi_title.setWordWrap(True)
        
        kpi_layout.addWidget(kpi_value)
        kpi_layout.addWidget(kpi_title)
        
        return kpi_frame
    
    def create_preventive_tab(self):
        """Crée l'onglet planification préventive simplifié"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        header = QLabel("📅 Planification Préventive")
        header.setStyleSheet("font-size: 20px; font-weight: bold; color: #2c3e50; margin: 20px;")
        header.setAlignment(Qt.AlignCenter)
        layout.addWidget(header)
        
        info = QLabel("✅ Module de planification préventive - Prêt pour l'intégration complète")
        info.setStyleSheet("font-size: 16px; color: #27ae60; margin: 20px;")
        info.setAlignment(Qt.AlignCenter)
        layout.addWidget(info)
        
        layout.addStretch()
        return tab
    
    def create_work_orders_tab(self):
        """Crée l'onglet bons de travail simplifié"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        header = QLabel("📋 Gestion des Bons de Travail")
        header.setStyleSheet("font-size: 20px; font-weight: bold; color: #2c3e50; margin: 20px;")
        header.setAlignment(Qt.AlignCenter)
        layout.addWidget(header)
        
        info = QLabel("✅ Module de gestion des bons de travail - Prêt pour l'intégration complète")
        info.setStyleSheet("font-size: 16px; color: #27ae60; margin: 20px;")
        info.setAlignment(Qt.AlignCenter)
        layout.addWidget(info)
        
        layout.addStretch()
        return tab
    
    def create_history_tab(self):
        """Crée l'onglet historique simplifié"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        header = QLabel("📊 Historique et Indicateurs")
        header.setStyleSheet("font-size: 20px; font-weight: bold; color: #2c3e50; margin: 20px;")
        header.setAlignment(Qt.AlignCenter)
        layout.addWidget(header)
        
        info = QLabel("✅ Module d'historique et d'indicateurs - Prêt pour l'intégration complète")
        info.setStyleSheet("font-size: 16px; color: #27ae60; margin: 20px;")
        info.setAlignment(Qt.AlignCenter)
        layout.addWidget(info)
        
        layout.addStretch()
        return tab
    
    def create_indicators_tab(self):
        """Crée l'onglet indicateurs simplifié"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        header = QLabel("📈 Indicateurs Avancés")
        header.setStyleSheet("font-size: 20px; font-weight: bold; color: #2c3e50; margin: 20px;")
        header.setAlignment(Qt.AlignCenter)
        layout.addWidget(header)
        
        info = QLabel("✅ Module d'indicateurs avancés - Prêt pour l'intégration complète")
        info.setStyleSheet("font-size: 16px; color: #27ae60; margin: 20px;")
        info.setAlignment(Qt.AlignCenter)
        layout.addWidget(info)
        
        layout.addStretch()
        return tab
    
    def load_dashboard_data(self):
        """Charge les données du tableau de bord"""
        print("📊 Chargement des données du tableau de bord...")
    
    def refresh_dashboard(self):
        """Actualise le tableau de bord"""
        print("🔄 Actualisation du tableau de bord...")
        self.load_dashboard_data()
    
    def on_intervention_created(self, intervention_id):
        """Appelé quand une intervention est créée"""
        print(f"✅ Intervention créée: {intervention_id}")
        self.intervention_created.emit(intervention_id)
        self.maintenance_updated.emit()
    
    def on_work_order_created(self, work_order_id):
        """Appelé quand un bon de travail est créé"""
        print(f"✅ Bon de travail créé: {work_order_id}")
        self.work_order_created.emit(work_order_id)
        self.maintenance_updated.emit()
    
    def create_new_intervention(self):
        """Crée une nouvelle intervention"""
        print("➕ Création d'une nouvelle intervention...")
        self.main_tabs.setCurrentIndex(0)  # Aller à l'onglet interventions
    
    def create_new_work_order(self):
        """Crée un nouveau bon de travail"""
        print("📋 Création d'un nouveau bon de travail...")
        self.main_tabs.setCurrentIndex(2)  # Aller à l'onglet bons de travail
    
    def schedule_maintenance(self):
        """Planifie une maintenance"""
        print("📅 Planification d'une maintenance...")
        self.main_tabs.setCurrentIndex(1)  # Aller à l'onglet planification
    
    def refresh_all_data(self):
        """Actualise toutes les données"""
        print("🔄 Actualisation de toutes les données...")
        self.refresh_dashboard()
        # Actualiser les autres onglets si nécessaire
