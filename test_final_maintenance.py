#!/usr/bin/env python3
"""
Test Final du Menu de Maintenance Complet
Vérifie l'intégration et le fonctionnement de tous les modules
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import Qt

# Ajouter le répertoire parent au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_maintenance_menu_complete():
    """Teste le menu de maintenance complet"""
    try:
        # Créer l'application
        app = QApplication(sys.argv)
        
        # Créer la fenêtre principale
        main_window = QMainWindow()
        main_window.setWindowTitle("Test Final - Menu de Maintenance Complet")
        main_window.setGeometry(100, 100, 1400, 900)
        
        # Widget central
        central_widget = QWidget()
        main_window.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Titre
        title = QLabel("🧪 Test du Menu de Maintenance Complet")
        title.setStyleSheet("font-size: 24px; font-weight: bold; margin: 20px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Test des imports
        print("🔍 Test des imports...")
        from gui.maintenance_menu import MaintenanceMenu
        from gui.maintenance_indicators import MaintenanceIndicators
        from gui.work_orders_manager import WorkOrdersManager
        from gui.interventions_management import InterventionsManagement
        from gui.preventive_maintenance import PreventiveMaintenance
        from gui.maintenance_history import MaintenanceHistoryView
        from gui.maintenance_checklist import MaintenanceChecklistView
        print("✅ Tous les modules importés avec succès")
        
        # Test de la base de données
        print("🔌 Test de la base de données...")
        from database import Database
        db = Database(":memory:")
        print("✅ Base de données créée")
        
        # Test de création des composants
        print("🏗️ Test de création des composants...")
        
        # Menu principal
        maintenance_menu = MaintenanceMenu(db, central_widget)
        layout.addWidget(maintenance_menu)
        print("✅ Menu de maintenance créé")
        
        # Test des onglets
        print("📑 Test des onglets...")
        tab_count = maintenance_menu.main_tabs.count()
        print(f"✅ {tab_count} onglets créés")
        
        # Test des fonctionnalités
        print("⚡ Test des fonctionnalités...")
        
        # Vérifier que le tableau de bord se charge
        maintenance_menu.load_dashboard_data()
        print("✅ Tableau de bord chargé")
        
        # Vérifier que les données se rafraîchissent
        maintenance_menu.refresh_all_data()
        print("✅ Données rafraîchies")
        
        # Afficher la fenêtre
        main_window.show()
        print("✅ Fenêtre affichée")
        
        # Message de succès
        success_label = QLabel("🎉 TOUS LES TESTS SONT RÉUSSIS !\nLe menu de maintenance est complètement fonctionnel.")
        success_label.setStyleSheet("font-size: 18px; color: green; margin: 20px; text-align: center;")
        success_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(success_label)
        
        print("\n🎯 MENU DE MAINTENANCE COMPLET - TEST RÉUSSI !")
        print("✅ Tous les modules sont intégrés et fonctionnels")
        print("✅ L'interface s'affiche correctement")
        print("✅ Les fonctionnalités sont accessibles")
        print("🚀 L'application est prête à être utilisée")
        
        # Lancer l'application
        return app.exec_()
        
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    print("🚀 Test Final du Menu de Maintenance Complet")
    print("=" * 60)
    
    result = test_maintenance_menu_complete()
    sys.exit(result)
