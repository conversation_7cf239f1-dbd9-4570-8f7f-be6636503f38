#!/usr/bin/env python3
"""
Test simple des fonctionnalités des équipements
"""

import sys
import os

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_equipment_methods():
    """Test des méthodes d'équipement"""
    print("🎯 TEST DES MÉTHODES ÉQUIPEMENT")
    print("=" * 50)
    
    try:
        from database import Database
        from export.excel_export import ExcelExporter
        
        # Initialiser la base de données
        db = Database()
        excel_exporter = ExcelExporter(db)
        
        print("✓ Base de données initialisée")
        
        # Vérifier les méthodes d'équipement
        if hasattr(db, 'add_equipment'):
            print("✓ Méthode add_equipment disponible")
        else:
            print("❌ Méthode add_equipment manquante")
            return False
        
        if hasattr(db, 'get_all_equipment'):
            print("✓ Méthode get_all_equipment disponible")
        else:
            print("❌ Méthode get_all_equipment manquante")
            return False
        
        if hasattr(excel_exporter, 'export_equipment_to_excel'):
            print("✓ Méthode export_equipment_to_excel disponible")
        else:
            print("❌ Méthode export_equipment_to_excel manquante")
            return False
        
        # Test de création d'équipement
        print("\n🧪 TEST DE CRÉATION D'ÉQUIPEMENT")
        print("-" * 30)
        
        equipment_data = {
            'name': 'Pompe Test',
            'model': 'Test Model',
            'manufacturer': 'Test Manufacturer',
            'location': 'Zone Test'
        }
        
        equipment_id = db.add_equipment(**equipment_data)
        print(f"✓ Équipement créé avec ID: {equipment_id}")
        
        # Vérifier la création
        equipment_list = db.get_all_equipment()
        print(f"✓ Nombre d'équipements en base: {len(equipment_list)}")
        
        # Test d'export
        print("\n🧪 TEST D'EXPORT EXCEL")
        print("-" * 30)
        
        filename = excel_exporter.export_equipment_to_excel()
        print(f"✓ Export réussi: {filename}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {str(e)}")
        return False

def test_maintenance_methods():
    """Test des méthodes de maintenance"""
    print("\n🔧 TEST DES MÉTHODES DE MAINTENANCE")
    print("=" * 50)
    
    try:
        from database import Database
        
        db = Database()
        
        # Vérifier les méthodes de maintenance
        if hasattr(db, 'add_maintenance_intervention'):
            print("✓ Méthode add_maintenance_intervention disponible")
        else:
            print("❌ Méthode add_maintenance_intervention manquante")
            return False
        
        # Créer une tâche de maintenance
        task_id = db.add_task(
            category_id=1,
            title="Maintenance Test",
            description="Test de maintenance",
            due_date="2025-12-31",
            priority="moyenne",
            status="À faire"
        )
        print(f"✓ Tâche créée avec ID: {task_id}")
        
        # Créer une intervention
        intervention_id = db.add_maintenance_intervention(
            task_id=task_id,
            details="Test intervention",
            technician_id=None
        )
        print(f"✓ Intervention créée avec ID: {intervention_id}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {str(e)}")
        return False

def main():
    """Fonction principale"""
    print("🎯 TEST SIMPLE DES FONCTIONNALITÉS ÉQUIPEMENTS")
    print("=" * 60)
    
    tests = [
        ("Méthodes équipement", test_equipment_methods),
        ("Méthodes maintenance", test_maintenance_methods)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Test : {test_name}")
        print("-" * 40)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur: {str(e)}")
            results.append((test_name, False))
    
    # Résumé
    print("\n" + "=" * 60)
    print("📋 RÉSUMÉ DES TESTS")
    print("=" * 60)
    
    success_count = 0
    for test_name, result in results:
        status = "✅ SUCCÈS" if result else "❌ ÉCHEC"
        print(f"{status} - {test_name}")
        if result:
            success_count += 1
    
    print(f"\n📊 RÉSULTATS : {success_count}/{len(results)} tests réussis")
    
    if success_count == len(results):
        print("🎉 TOUTES LES FONCTIONNALITÉS SONT OPÉRATIONNELLES !")
        print("\n✅ Fonctionnalités validées:")
        print("   • Création d'équipement")
        print("   • Export Excel des équipements")
        print("   • Planification de maintenance")
        print("   • Interventions de maintenance")
    else:
        print("⚠️ Certaines fonctionnalités nécessitent des corrections")
    
    return success_count == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
