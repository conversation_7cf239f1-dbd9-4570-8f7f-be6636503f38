"""
Module complet de gestion des équipements pour SOTRAMINE PHOSPHATE
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                             QTableWidgetItem, QPushButton, QLabel, QFrame, 
                             QComboBox, QLineEdit, QTextEdit, QDateEdit, 
                             QDialog, QFormLayout, QMessageBox, QHeaderView,
                             QSplitter, QTabWidget, QFileDialog, QListWidget,
                             QProgressBar, QGroupBox, QGridLayout)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QPixmap
from utils.data_refresh_manager import RefreshableWidget, notify_data_changed
import os

class EquipmentManagerComplete(QWidget, RefreshableWidget):
    """Gestionnaire complet des équipements"""
    
    def __init__(self, db, parent=None):
        super().__init__(parent)
        RefreshableWidget.__init__(self)
        self.db = db
        self.setup_ui()
        self.load_equipment()
        self.enable_auto_refresh(['equipment'])
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # En-tête
        header = self.create_header()
        layout.addWidget(header)
        
        # Contenu principal avec splitter
        splitter = QSplitter(Qt.Horizontal)
        
        # Panel de gauche - Liste des équipements
        left_panel = self.create_equipment_panel()
        splitter.addWidget(left_panel)
        
        # Panel de droite - Détails
        right_panel = self.create_details_panel()
        splitter.addWidget(right_panel)
        
        splitter.setSizes([700, 500])
        layout.addWidget(splitter)
    
    def create_header(self):
        """Crée l'en-tête du module"""
        header = QFrame()
        header.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #e67e22, stop:1 #d35400);
                padding: 20px;
                border-radius: 0;
            }
        """)
        header.setFixedHeight(80)
        
        layout = QHBoxLayout(header)
        
        # Titre
        title = QLabel("🔌 GESTION DES ÉQUIPEMENTS")
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24px;
                font-weight: bold;
            }
        """)
        layout.addWidget(title)
        
        layout.addStretch()
        
        # Boutons d'actions
        actions = [
            ("➕ Nouvel Équipement", "#27ae60", self.create_new_equipment),
            ("📊 Statistiques", "#3498db", self.show_statistics),
            ("📄 Exporter", "#9b59b6", self.export_equipment),
            ("📥 Importer", "#f39c12", self.import_equipment)
        ]
        
        for text, color, action in actions:
            btn = QPushButton(text)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 10px 15px;
                    font-weight: bold;
                    margin-left: 5px;
                }}
                QPushButton:hover {{
                    background-color: {color}dd;
                }}
            """)
            btn.clicked.connect(action)
            layout.addWidget(btn)
        
        return header
    
    def create_equipment_panel(self):
        """Crée le panel des équipements"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Filtres et recherche
        filters_frame = self.create_filters()
        layout.addWidget(filters_frame)
        
        # Table des équipements
        self.equipment_table = QTableWidget()
        self.equipment_table.setColumnCount(8)
        self.equipment_table.setHorizontalHeaderLabels([
            "ID", "Code", "Nom", "Type", "Statut", "Localisation", "Dernière Maintenance", "Prochaine Maintenance"
        ])
        
        # Style de la table
        self.equipment_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #ddd;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eee;
            }
            QTableWidget::item:selected {
                background-color: #e67e22;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)
        
        self.equipment_table.setAlternatingRowColors(True)
        self.equipment_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.equipment_table.horizontalHeader().setStretchLastSection(True)
        self.equipment_table.itemSelectionChanged.connect(self.on_equipment_selected)
        
        layout.addWidget(self.equipment_table)
        
        return panel
    
    def create_filters(self):
        """Crée les filtres"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 15px;
            }
        """)
        
        layout = QHBoxLayout(frame)
        
        # Filtre par type
        layout.addWidget(QLabel("Type:"))
        self.type_filter = QComboBox()
        self.type_filter.addItems(["Tous", "Pompe", "Moteur", "Compresseur", "Convoyeur", "Autre"])
        self.type_filter.currentTextChanged.connect(self.filter_equipment)
        layout.addWidget(self.type_filter)
        
        # Filtre par statut
        layout.addWidget(QLabel("Statut:"))
        self.status_filter = QComboBox()
        self.status_filter.addItems(["Tous", "Opérationnel", "En maintenance", "Hors service", "En attente"])
        self.status_filter.currentTextChanged.connect(self.filter_equipment)
        layout.addWidget(self.status_filter)
        
        # Recherche
        layout.addWidget(QLabel("Recherche:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Code, nom ou localisation...")
        self.search_edit.textChanged.connect(self.filter_equipment)
        layout.addWidget(self.search_edit)
        
        layout.addStretch()
        
        return frame
    
    def create_details_panel(self):
        """Crée le panel des détails"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Titre
        title = QLabel("🔧 Détails de l'Équipement")
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # Onglets de détails
        self.details_tabs = QTabWidget()
        self.details_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #ddd;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f8f9fa;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: #e67e22;
                color: white;
            }
        """)
        
        # Onglets
        self.info_tab = self.create_info_tab()
        self.details_tabs.addTab(self.info_tab, "Informations")
        
        self.maintenance_tab = self.create_maintenance_tab()
        self.details_tabs.addTab(self.maintenance_tab, "Maintenance")
        
        self.documents_tab = self.create_documents_tab()
        self.details_tabs.addTab(self.documents_tab, "Documents")
        
        self.history_tab = self.create_history_tab()
        self.details_tabs.addTab(self.history_tab, "Historique")
        
        layout.addWidget(self.details_tabs)
        
        # Boutons d'actions
        actions_frame = self.create_actions_frame()
        layout.addWidget(actions_frame)
        
        return panel
    
    def create_info_tab(self):
        """Crée l'onglet d'informations générales"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # Informations de base
        basic_group = QGroupBox("Informations de Base")
        basic_layout = QGridLayout(basic_group)
        
        self.info_labels = {}
        basic_fields = [
            ("ID:", "id_label", 0, 0),
            ("Code:", "code_label", 0, 1),
            ("Nom:", "name_label", 1, 0),
            ("Type:", "type_label", 1, 1),
            ("Modèle:", "model_label", 2, 0),
            ("N° Série:", "serial_label", 2, 1),
            ("Fabricant:", "manufacturer_label", 3, 0),
            ("Statut:", "status_label", 3, 1)
        ]
        
        for field_name, label_key, row, col in basic_fields:
            name_label = QLabel(field_name)
            name_label.setStyleSheet("font-weight: bold;")
            basic_layout.addWidget(name_label, row * 2, col * 2)
            
            value_label = QLabel("-")
            value_label.setStyleSheet("color: #495057; margin-bottom: 10px;")
            basic_layout.addWidget(value_label, row * 2 + 1, col * 2)
            
            self.info_labels[label_key] = value_label
        
        layout.addWidget(basic_group)
        
        # Informations techniques
        tech_group = QGroupBox("Informations Techniques")
        tech_layout = QGridLayout(tech_group)
        
        tech_fields = [
            ("Localisation:", "location_label", 0, 0),
            ("Date d'achat:", "purchase_date_label", 0, 1),
            ("Date d'installation:", "install_date_label", 1, 0),
            ("Garantie jusqu'au:", "warranty_label", 1, 1)
        ]
        
        for field_name, label_key, row, col in tech_fields:
            name_label = QLabel(field_name)
            name_label.setStyleSheet("font-weight: bold;")
            tech_layout.addWidget(name_label, row * 2, col * 2)
            
            value_label = QLabel("-")
            value_label.setStyleSheet("color: #495057; margin-bottom: 10px;")
            tech_layout.addWidget(value_label, row * 2 + 1, col * 2)
            
            self.info_labels[label_key] = value_label
        
        layout.addWidget(tech_group)
        
        # Notes
        notes_group = QGroupBox("Notes")
        notes_layout = QVBoxLayout(notes_group)
        
        self.notes_label = QLabel("-")
        self.notes_label.setStyleSheet("color: #495057; padding: 10px;")
        self.notes_label.setWordWrap(True)
        notes_layout.addWidget(self.notes_label)
        
        layout.addWidget(notes_group)
        layout.addStretch()
        
        return tab
    
    def create_maintenance_tab(self):
        """Crée l'onglet de maintenance"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # Statut de maintenance
        status_group = QGroupBox("Statut de Maintenance")
        status_layout = QGridLayout(status_group)
        
        # Labels de maintenance
        self.maintenance_labels = {}
        maintenance_fields = [
            ("Dernière maintenance:", "last_maintenance_label", 0, 0),
            ("Prochaine maintenance:", "next_maintenance_label", 0, 1),
            ("Fréquence:", "frequency_label", 1, 0),
            ("Type de maintenance:", "maintenance_type_label", 1, 1)
        ]
        
        for field_name, label_key, row, col in maintenance_fields:
            name_label = QLabel(field_name)
            name_label.setStyleSheet("font-weight: bold;")
            status_layout.addWidget(name_label, row * 2, col * 2)
            
            value_label = QLabel("-")
            value_label.setStyleSheet("color: #495057; margin-bottom: 10px;")
            status_layout.addWidget(value_label, row * 2 + 1, col * 2)
            
            self.maintenance_labels[label_key] = value_label
        
        layout.addWidget(status_group)
        
        # Interventions récentes
        recent_group = QGroupBox("Interventions Récentes")
        recent_layout = QVBoxLayout(recent_group)
        
        self.recent_interventions = QTableWidget()
        self.recent_interventions.setColumnCount(4)
        self.recent_interventions.setHorizontalHeaderLabels(["Date", "Type", "Technicien", "Statut"])
        self.recent_interventions.horizontalHeader().setStretchLastSection(True)
        self.recent_interventions.setMaximumHeight(150)
        
        recent_layout.addWidget(self.recent_interventions)
        layout.addWidget(recent_group)
        
        # Boutons de maintenance
        maintenance_actions = QHBoxLayout()
        
        btn_schedule = QPushButton("📅 Planifier Maintenance")
        btn_schedule.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        btn_schedule.clicked.connect(self.schedule_maintenance)
        maintenance_actions.addWidget(btn_schedule)
        
        btn_intervention = QPushButton("🔧 Nouvelle Intervention")
        btn_intervention.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        btn_intervention.clicked.connect(self.create_intervention)
        maintenance_actions.addWidget(btn_intervention)
        
        maintenance_actions.addStretch()
        layout.addLayout(maintenance_actions)
        
        layout.addStretch()
        
        return tab
    
    def create_documents_tab(self):
        """Crée l'onglet des documents"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # Toolbar pour documents
        doc_toolbar = QHBoxLayout()
        
        btn_add_doc = QPushButton("📎 Ajouter Document")
        btn_add_doc.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        btn_add_doc.clicked.connect(self.add_document)
        doc_toolbar.addWidget(btn_add_doc)
        
        btn_remove_doc = QPushButton("🗑️ Supprimer")
        btn_remove_doc.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        btn_remove_doc.clicked.connect(self.remove_document)
        doc_toolbar.addWidget(btn_remove_doc)
        
        doc_toolbar.addStretch()
        layout.addLayout(doc_toolbar)
        
        # Liste des documents
        self.documents_list = QListWidget()
        self.documents_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #ddd;
                background-color: white;
                padding: 5px;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eee;
            }
            QListWidget::item:selected {
                background-color: #e67e22;
                color: white;
            }
        """)
        
        # Ajouter quelques documents d'exemple
        example_docs = [
            "📄 Manuel d'utilisation.pdf",
            "📋 Fiche technique.pdf",
            "🔧 Procédure de maintenance.pdf",
            "📊 Rapport d'inspection.pdf"
        ]
        
        for doc in example_docs:
            self.documents_list.addItem(doc)
        
        layout.addWidget(self.documents_list)
        
        return tab
    
    def create_history_tab(self):
        """Crée l'onglet d'historique"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        
        self.history_table = QTableWidget()
        self.history_table.setColumnCount(5)
        self.history_table.setHorizontalHeaderLabels(["Date", "Type", "Description", "Technicien", "Coût"])
        self.history_table.horizontalHeader().setStretchLastSection(True)
        
        # Ajouter quelques entrées d'exemple
        history_data = [
            ("2025-08-01", "Maintenance préventive", "Changement d'huile et filtres", "Jean Dupont", "150€"),
            ("2025-07-15", "Réparation", "Remplacement roulement", "Marie Martin", "320€"),
            ("2025-07-01", "Inspection", "Contrôle général", "Pierre Durand", "80€"),
            ("2025-06-15", "Maintenance préventive", "Nettoyage et lubrification", "Jean Dupont", "120€")
        ]
        
        self.history_table.setRowCount(len(history_data))
        for row, data in enumerate(history_data):
            for col, value in enumerate(data):
                self.history_table.setItem(row, col, QTableWidgetItem(value))
        
        layout.addWidget(self.history_table)
        
        return tab
    
    def create_actions_frame(self):
        """Crée le cadre des actions"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                margin-top: 15px;
            }
        """)
        
        layout = QHBoxLayout(frame)
        
        # Boutons d'actions
        actions = [
            ("✏️ Modifier", "#f39c12", self.edit_equipment),
            ("🔧 Maintenance", "#3498db", self.schedule_maintenance),
            ("❌ Supprimer", "#e74c3c", self.delete_equipment)
        ]
        
        for text, color, action in actions:
            btn = QPushButton(text)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 16px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: {color}dd;
                }}
                QPushButton:disabled {{
                    background-color: #bdc3c7;
                }}
            """)
            btn.clicked.connect(action)
            btn.setEnabled(False)  # Désactivé par défaut
            layout.addWidget(btn)
            
            # Stocker les boutons
            if text == "✏️ Modifier":
                self.edit_btn = btn
            elif text == "🔧 Maintenance":
                self.maintenance_btn = btn
            elif text == "❌ Supprimer":
                self.delete_btn = btn
        
        layout.addStretch()
        
        return frame
    
    def load_equipment(self):
        """Charge les équipements depuis la base de données"""
        try:
            # Simuler des données d'équipements
            equipment_data = [
                (1, "EQ001", "Pompe centrifuge A1", "Pompe", "Opérationnel", "Zone A", "2025-07-15", "2025-09-15"),
                (2, "EQ002", "Moteur électrique B2", "Moteur", "En maintenance", "Zone B", "2025-06-20", "2025-08-20"),
                (3, "EQ003", "Compresseur C3", "Compresseur", "Opérationnel", "Zone C", "2025-07-01", "2025-10-01"),
                (4, "EQ004", "Convoyeur D4", "Convoyeur", "Hors service", "Zone D", "2025-05-15", "2025-08-15"),
                (5, "EQ005", "Pompe hydraulique E5", "Pompe", "Opérationnel", "Zone E", "2025-07-30", "2025-10-30")
            ]
            
            self.all_equipment = equipment_data
            self.populate_table(equipment_data)
            
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des équipements : {str(e)}")
    
    def populate_table(self, equipment_data):
        """Remplit la table avec les équipements"""
        self.equipment_table.setRowCount(len(equipment_data))
        
        for row, equipment in enumerate(equipment_data):
            for col, value in enumerate(equipment):
                item = QTableWidgetItem(str(value))
                
                # Colorer selon le statut
                if col == 4:  # Colonne statut
                    if value == "Opérationnel":
                        item.setBackground(QColor("#d4edda"))
                    elif value == "En maintenance":
                        item.setBackground(QColor("#fff3cd"))
                    elif value == "Hors service":
                        item.setBackground(QColor("#f8d7da"))
                
                self.equipment_table.setItem(row, col, item)
        
        # Ajuster les colonnes
        self.equipment_table.resizeColumnsToContents()
    
    def filter_equipment(self):
        """Filtre les équipements selon les critères"""
        type_filter = self.type_filter.currentText()
        status_filter = self.status_filter.currentText()
        search_text = self.search_edit.text().lower()
        
        filtered_equipment = []
        
        for equipment in self.all_equipment:
            # Filtre par type
            if type_filter != "Tous" and equipment[3] != type_filter:
                continue
            
            # Filtre par statut
            if status_filter != "Tous" and equipment[4] != status_filter:
                continue
            
            # Filtre par recherche
            if search_text:
                searchable_text = f"{equipment[1]} {equipment[2]} {equipment[5]}".lower()
                if search_text not in searchable_text:
                    continue
            
            filtered_equipment.append(equipment)
        
        self.populate_table(filtered_equipment)
    
    def on_equipment_selected(self):
        """Gère la sélection d'un équipement"""
        current_row = self.equipment_table.currentRow()
        if current_row >= 0:
            # Activer les boutons d'actions
            self.edit_btn.setEnabled(True)
            self.maintenance_btn.setEnabled(True)
            self.delete_btn.setEnabled(True)
            
            # Charger les détails
            self.load_equipment_details(current_row)
        else:
            # Désactiver les boutons
            self.edit_btn.setEnabled(False)
            self.maintenance_btn.setEnabled(False)
            self.delete_btn.setEnabled(False)
    
    def load_equipment_details(self, row):
        """Charge les détails d'un équipement"""
        # Récupérer les données de la ligne
        equipment_data = []
        for col in range(self.equipment_table.columnCount()):
            item = self.equipment_table.item(row, col)
            if item:
                equipment_data.append(item.text())
            else:
                equipment_data.append("")
        
        # Mettre à jour les labels d'informations
        if len(equipment_data) >= 8:
            self.info_labels["id_label"].setText(equipment_data[0])
            self.info_labels["code_label"].setText(equipment_data[1])
            self.info_labels["name_label"].setText(equipment_data[2])
            self.info_labels["type_label"].setText(equipment_data[3])
            self.info_labels["status_label"].setText(equipment_data[4])
            self.info_labels["location_label"].setText(equipment_data[5])
            
            # Informations supplémentaires (simulées)
            self.info_labels["model_label"].setText("Modèle XYZ-123")
            self.info_labels["serial_label"].setText("SN123456789")
            self.info_labels["manufacturer_label"].setText("Fabricant ABC")
            self.info_labels["purchase_date_label"].setText("2023-01-15")
            self.info_labels["install_date_label"].setText("2023-02-01")
            self.info_labels["warranty_label"].setText("2026-02-01")
            
            # Informations de maintenance
            self.maintenance_labels["last_maintenance_label"].setText(equipment_data[6])
            self.maintenance_labels["next_maintenance_label"].setText(equipment_data[7])
            self.maintenance_labels["frequency_label"].setText("Tous les 3 mois")
            self.maintenance_labels["maintenance_type_label"].setText("Préventive")
            
            # Notes
            self.notes_label.setText("Équipement en bon état général. Surveillance particulière des vibrations.")
    
    # Méthodes d'actions
    def create_new_equipment(self):
        """Crée un nouvel équipement"""
        dialog = EquipmentDialog(self.db, self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_equipment()
            notify_data_changed('equipment')
    
    def edit_equipment(self):
        """Modifie l'équipement sélectionné"""
        current_row = self.equipment_table.currentRow()
        if current_row >= 0:
            equipment_id = self.equipment_table.item(current_row, 0).text()
            dialog = EquipmentDialog(self.db, self, equipment_id)
            if dialog.exec_() == QDialog.Accepted:
                self.load_equipment()
                notify_data_changed('equipment')
    
    def delete_equipment(self):
        """Supprime l'équipement sélectionné"""
        current_row = self.equipment_table.currentRow()
        if current_row >= 0:
            equipment_name = self.equipment_table.item(current_row, 2).text()
            reply = QMessageBox.question(self, "Confirmer", 
                                       f"Êtes-vous sûr de vouloir supprimer l'équipement '{equipment_name}' ?",
                                       QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.Yes:
                self.equipment_table.removeRow(current_row)
                notify_data_changed('equipment')
                QMessageBox.information(self, "Succès", "Équipement supprimé !")
    
    def schedule_maintenance(self):
        """Planifie une maintenance"""
        QMessageBox.information(self, "Planification Maintenance", 
                               "📅 Planification de maintenance\n\n"
                               "Cette fonctionnalité ouvrira un dialog pour :\n"
                               "• Choisir le type de maintenance\n"
                               "• Sélectionner la date\n"
                               "• Assigner un technicien\n"
                               "• Définir les tâches à effectuer")
    
    def create_intervention(self):
        """Crée une nouvelle intervention"""
        QMessageBox.information(self, "Nouvelle Intervention", 
                               "🔧 Création d'intervention\n\n"
                               "Cette fonctionnalité ouvrira un dialog pour :\n"
                               "• Décrire le problème\n"
                               "• Définir la priorité\n"
                               "• Assigner un technicien\n"
                               "• Estimer la durée")
    
    def add_document(self):
        """Ajoute un document"""
        file_path, _ = QFileDialog.getOpenFileName(self, "Sélectionner un document", 
                                                  "", "Tous les fichiers (*.*)")
        if file_path:
            file_name = os.path.basename(file_path)
            self.documents_list.addItem(f"📄 {file_name}")
            QMessageBox.information(self, "Succès", f"Document '{file_name}' ajouté !")
    
    def remove_document(self):
        """Supprime un document"""
        current_item = self.documents_list.currentItem()
        if current_item:
            reply = QMessageBox.question(self, "Confirmer", 
                                       "Supprimer ce document ?",
                                       QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.Yes:
                self.documents_list.takeItem(self.documents_list.currentRow())
                QMessageBox.information(self, "Succès", "Document supprimé !")
    
    def show_statistics(self):
        """Affiche les statistiques des équipements"""
        QMessageBox.information(self, "Statistiques Équipements", 
                               "📊 Statistiques des équipements\n\n"
                               "• Total équipements : 5\n"
                               "• Opérationnels : 3 (60%)\n"
                               "• En maintenance : 1 (20%)\n"
                               "• Hors service : 1 (20%)\n\n"
                               "• Maintenances dues : 2\n"
                               "• Coût maintenance mensuel : 1,250€")
    
    def export_equipment(self):
        """Exporte les équipements vers Excel"""
        try:
            from export.excel_export import ExcelExporter
            exporter = ExcelExporter(self.db)
            
            file_path = "export_equipements.xlsx"
            success = exporter.export_equipment_list(file_path)
            
            if success:
                QMessageBox.information(self, "Export Réussi", 
                                       f"📄 Export Excel réussi !\n\nFichier : {file_path}")
            else:
                QMessageBox.warning(self, "Erreur Export", 
                                   "❌ Erreur lors de l'export Excel.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'export : {str(e)}")
    
    def import_equipment(self):
        """Importe des équipements depuis Excel"""
        file_path, _ = QFileDialog.getOpenFileName(self, "Importer équipements", 
                                                  "", "Fichiers Excel (*.xlsx *.xls)")
        if file_path:
            QMessageBox.information(self, "Import", 
                                   f"📥 Import depuis {os.path.basename(file_path)}\n\n"
                                   "Cette fonctionnalité sera implémentée pour :\n"
                                   "• Lire le fichier Excel\n"
                                   "• Valider les données\n"
                                   "• Importer les équipements\n"
                                   "• Afficher un rapport d'import")
    
    def refresh_data(self):
        """Actualise les données"""
        self.load_equipment()


class EquipmentDialog(QDialog):
    """Dialog pour créer/modifier un équipement"""
    
    def __init__(self, db, parent=None, equipment_id=None):
        super().__init__(parent)
        self.db = db
        self.equipment_id = equipment_id
        self.setup_ui()
        
        if equipment_id:
            self.setWindowTitle("Modifier l'Équipement")
            self.load_equipment_data()
        else:
            self.setWindowTitle("Nouvel Équipement")
    
    def setup_ui(self):
        """Configure l'interface du dialog"""
        self.setFixedSize(600, 700)
        layout = QVBoxLayout(self)
        
        # Onglets
        tabs = QTabWidget()
        
        # Onglet informations générales
        general_tab = QWidget()
        general_layout = QFormLayout(general_tab)
        
        self.code_edit = QLineEdit()
        general_layout.addRow("Code:", self.code_edit)
        
        self.name_edit = QLineEdit()
        general_layout.addRow("Nom:", self.name_edit)
        
        self.type_combo = QComboBox()
        self.type_combo.addItems(["Pompe", "Moteur", "Compresseur", "Convoyeur", "Autre"])
        general_layout.addRow("Type:", self.type_combo)
        
        self.model_edit = QLineEdit()
        general_layout.addRow("Modèle:", self.model_edit)
        
        self.serial_edit = QLineEdit()
        general_layout.addRow("N° Série:", self.serial_edit)
        
        self.manufacturer_edit = QLineEdit()
        general_layout.addRow("Fabricant:", self.manufacturer_edit)
        
        self.location_edit = QLineEdit()
        general_layout.addRow("Localisation:", self.location_edit)
        
        self.status_combo = QComboBox()
        self.status_combo.addItems(["Opérationnel", "En maintenance", "Hors service", "En attente"])
        general_layout.addRow("Statut:", self.status_combo)
        
        tabs.addTab(general_tab, "Général")
        
        # Onglet dates
        dates_tab = QWidget()
        dates_layout = QFormLayout(dates_tab)
        
        self.purchase_date = QDateEdit()
        self.purchase_date.setDate(QDate.currentDate())
        self.purchase_date.setCalendarPopup(True)
        dates_layout.addRow("Date d'achat:", self.purchase_date)
        
        self.install_date = QDateEdit()
        self.install_date.setDate(QDate.currentDate())
        self.install_date.setCalendarPopup(True)
        dates_layout.addRow("Date d'installation:", self.install_date)
        
        self.warranty_date = QDateEdit()
        self.warranty_date.setDate(QDate.currentDate().addYears(2))
        self.warranty_date.setCalendarPopup(True)
        dates_layout.addRow("Fin de garantie:", self.warranty_date)
        
        tabs.addTab(dates_tab, "Dates")
        
        # Onglet notes
        notes_tab = QWidget()
        notes_layout = QVBoxLayout(notes_tab)
        
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("Notes et observations...")
        notes_layout.addWidget(self.notes_edit)
        
        tabs.addTab(notes_tab, "Notes")
        
        layout.addWidget(tabs)
        
        # Boutons
        buttons_layout = QHBoxLayout()
        
        btn_save = QPushButton("💾 Enregistrer")
        btn_save.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        btn_save.clicked.connect(self.save_equipment)
        buttons_layout.addWidget(btn_save)
        
        btn_cancel = QPushButton("❌ Annuler")
        btn_cancel.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        btn_cancel.clicked.connect(self.reject)
        buttons_layout.addWidget(btn_cancel)
        
        layout.addLayout(buttons_layout)
    
    def load_equipment_data(self):
        """Charge les données de l'équipement à modifier"""
        # Simuler le chargement des données
        self.code_edit.setText("EQ001")
        self.name_edit.setText("Pompe centrifuge A1")
        self.type_combo.setCurrentText("Pompe")
        self.model_edit.setText("XYZ-123")
        self.serial_edit.setText("SN123456789")
        self.manufacturer_edit.setText("Fabricant ABC")
        self.location_edit.setText("Zone A")
        self.status_combo.setCurrentText("Opérationnel")
        self.notes_edit.setText("Équipement en bon état général.")
    
    def save_equipment(self):
        """Sauvegarde l'équipement"""
        if not self.code_edit.text().strip():
            QMessageBox.warning(self, "Erreur", "Le code est obligatoire !")
            return
        
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "Erreur", "Le nom est obligatoire !")
            return
        
        # Simuler la sauvegarde
        QMessageBox.information(self, "Succès", "Équipement sauvegardé avec succès !")
        self.accept()
