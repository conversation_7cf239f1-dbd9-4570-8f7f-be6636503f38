#!/usr/bin/env python3
"""
Script de réinitialisation manuelle de l'application SOTRAMINE PHOSPHATE
"""

import os
import shutil
import sqlite3
import sys
from datetime import datetime

def create_backup():
    """Crée une sauvegarde de la base de données"""
    try:
        if os.path.exists('sotramine.db'):
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f'backup_sotramine_{timestamp}.db'
            shutil.copy2('sotramine.db', backup_name)
            print(f"✓ Sauvegarde créée : {backup_name}")
            return backup_name
        else:
            print("⚠️ Aucune base de données à sauvegarder")
            return None
    except Exception as e:
        print(f"❌ Erreur lors de la sauvegarde : {str(e)}")
        return None

def check_current_state():
    """Vérifie l'état actuel de la base de données"""
    print("🔍 ÉTAT ACTUEL DE LA BASE DE DONNÉES")
    print("-" * 40)
    
    if not os.path.exists('sotramine.db'):
        print("❌ Base de données non trouvée")
        return {}
    
    try:
        conn = sqlite3.connect('sotramine.db')
        cursor = conn.cursor()
        
        # Vérifier les tables existantes
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [table[0] for table in cursor.fetchall()]
        
        if not tables:
            print("⚠️ Base de données vide (aucune table)")
            conn.close()
            return {}
        
        print(f"📋 Tables trouvées : {', '.join(tables)}")
        
        # Compter les données dans chaque table
        data_counts = {}
        for table in tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                data_counts[table] = count
                print(f"   • {table} : {count} enregistrement(s)")
            except Exception as e:
                print(f"   • {table} : Erreur lecture ({str(e)})")
                data_counts[table] = -1
        
        conn.close()
        return data_counts
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification : {str(e)}")
        return {}

def reset_database():
    """Réinitialise complètement la base de données"""
    print("\n🗑️ RÉINITIALISATION DE LA BASE DE DONNÉES")
    print("-" * 40)
    
    # Fichiers de base de données SQLite à supprimer
    db_files = [
        'sotramine.db',
        'sotramine.db-journal',
        'sotramine.db-wal',
        'sotramine.db-shm'
    ]
    
    deleted_files = []
    for db_file in db_files:
        if os.path.exists(db_file):
            try:
                os.remove(db_file)
                deleted_files.append(db_file)
                print(f"✓ Supprimé : {db_file}")
            except Exception as e:
                print(f"❌ Erreur suppression {db_file} : {str(e)}")
        else:
            print(f"⚪ {db_file} : N'existe pas")
    
    return len(deleted_files) > 0

def reset_exports():
    """Supprime les fichiers d'export"""
    print("\n📊 SUPPRESSION DES EXPORTS")
    print("-" * 40)
    
    export_dir = 'export'
    if os.path.exists(export_dir):
        try:
            # Lister les fichiers avant suppression
            files = os.listdir(export_dir)
            if files:
                print(f"📁 Fichiers dans {export_dir} :")
                for file in files:
                    print(f"   • {file}")
                
                # Supprimer le dossier
                shutil.rmtree(export_dir)
                print(f"✓ Dossier {export_dir} supprimé")
                return True
            else:
                print(f"⚪ Dossier {export_dir} vide")
                os.rmdir(export_dir)
                print(f"✓ Dossier {export_dir} vide supprimé")
                return True
        except Exception as e:
            print(f"❌ Erreur suppression exports : {str(e)}")
            return False
    else:
        print(f"⚪ Dossier {export_dir} n'existe pas")
        return True

def reset_config():
    """Supprime les fichiers de configuration"""
    print("\n⚙️ SUPPRESSION DE LA CONFIGURATION")
    print("-" * 40)
    
    config_files = [
        'config.ini',
        'settings.json',
        'user_preferences.json',
        'app_config.json'
    ]
    
    deleted_count = 0
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                os.remove(config_file)
                print(f"✓ Supprimé : {config_file}")
                deleted_count += 1
            except Exception as e:
                print(f"❌ Erreur suppression {config_file} : {str(e)}")
        else:
            print(f"⚪ {config_file} : N'existe pas")
    
    return deleted_count > 0

def verify_reset():
    """Vérifie que la réinitialisation a bien fonctionné"""
    print("\n✅ VÉRIFICATION DE LA RÉINITIALISATION")
    print("-" * 40)
    
    # Vérifier que la base de données n'existe plus
    if os.path.exists('sotramine.db'):
        print("❌ La base de données existe encore")
        return False
    else:
        print("✓ Base de données supprimée")
    
    # Vérifier que le dossier export n'existe plus
    if os.path.exists('export'):
        print("❌ Le dossier export existe encore")
        return False
    else:
        print("✓ Dossier export supprimé")
    
    print("✅ Réinitialisation vérifiée avec succès")
    return True

def main():
    """Point d'entrée principal"""
    print("🔄 SOTRAMINE PHOSPHATE - RÉINITIALISATION MANUELLE")
    print("Version 2.1 - Script de Réinitialisation Robuste")
    print("=" * 60)
    
    # Vérifier l'état actuel
    current_state = check_current_state()
    
    # Demander confirmation
    print(f"\n⚠️ ATTENTION - OPÉRATION IRRÉVERSIBLE !")
    print("Cette opération va supprimer TOUTES les données de l'application.")
    print("Assurez-vous que l'application SOTRAMINE PHOSPHATE est fermée.")
    
    response = input("\nÊtes-vous sûr de vouloir continuer ? (tapez 'sotramine' pour confirmer) : ")
    
    if response != 'sotramine':
        print("❌ Réinitialisation annulée")
        return
    
    print(f"\n🔄 DÉMARRAGE DE LA RÉINITIALISATION...")
    
    # Créer une sauvegarde
    backup_file = create_backup()
    
    # Réinitialiser la base de données
    db_reset = reset_database()
    
    # Réinitialiser les exports
    exports_reset = reset_exports()
    
    # Demander pour la configuration
    config_response = input("\nVoulez-vous aussi supprimer la configuration ? (o/N) : ").lower()
    config_reset = False
    if config_response in ['o', 'oui', 'y', 'yes']:
        config_reset = reset_config()
    
    # Vérifier la réinitialisation
    verification_ok = verify_reset()
    
    # Résumé final
    print(f"\n" + "=" * 60)
    print("📋 RÉSUMÉ DE LA RÉINITIALISATION")
    print("=" * 60)
    
    print(f"💾 Sauvegarde : {'✓' if backup_file else '❌'}")
    if backup_file:
        print(f"   Fichier : {backup_file}")
    
    print(f"🗄️ Base de données : {'✓' if db_reset else '❌'}")
    print(f"📊 Exports : {'✓' if exports_reset else '❌'}")
    print(f"⚙️ Configuration : {'✓' if config_reset else '⚪ Ignorée'}")
    print(f"✅ Vérification : {'✓' if verification_ok else '❌'}")
    
    if verification_ok:
        print(f"\n🎉 RÉINITIALISATION TERMINÉE AVEC SUCCÈS !")
        print("✅ L'application a été complètement réinitialisée")
        print("🚀 Vous pouvez maintenant relancer l'application")
        
        if backup_file:
            print(f"\n💾 SAUVEGARDE DISPONIBLE :")
            print(f"   Fichier : {backup_file}")
            print(f"   Pour restaurer : renommez ce fichier en 'sotramine.db'")
    else:
        print(f"\n⚠️ RÉINITIALISATION INCOMPLÈTE")
        print("Certains éléments n'ont pas pu être supprimés")
        print("Vérifiez les erreurs ci-dessus et réessayez")
        
        if backup_file:
            print(f"\n💾 Sauvegarde disponible : {backup_file}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Réinitialisation interrompue par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur critique : {str(e)}")
        import traceback
        traceback.print_exc()
