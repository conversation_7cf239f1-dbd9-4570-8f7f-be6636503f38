#!/usr/bin/env python3
"""
Test de l'importation des équipements depuis un fichier CSV
"""

import sys
import os
import csv
from datetime import datetime

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_csv():
    """Crée un fichier CSV de test avec des équipements"""
    test_data = [
        {
            'nom': 'Compresseur Principal',
            'code': 'COMP-001',
            'modele': 'Atlas Copco GA55',
            'numero_serie': 'AC123456',
            'fabricant': 'Atlas Copco',
            'date_achat': '2020-01-15',
            'date_installation': '2020-02-01',
            'localisation': 'Atelier Principal',
            'statut': 'En service',
            'derniere_maintenance': '2024-06-15',
            'prochaine_maintenance': '2024-12-15',
            'notes': 'Compresseur principal pour l\'air comprimé'
        },
        {
            'nom': 'Pompe Centrifuge A',
            'code': 'PUMP-A01',
            'modele': 'Grundfos CR32',
            'numero_serie': 'GR789012',
            'fabricant': 'Grundfos',
            'date_achat': '2021-03-10',
            'date_installation': '2021-03-20',
            'localisation': 'Station de Pompage',
            'statut': 'En service',
            'derniere_maintenance': '2024-05-20',
            'prochaine_maintenance': '2024-11-20',
            'notes': 'Pompe principale circuit A'
        },
        {
            'nom': 'Moteur Électrique B2',
            'code': 'MOT-B02',
            'modele': 'Siemens 1LA7',
            'numero_serie': 'SM345678',
            'fabricant': 'Siemens',
            'date_achat': '2019-08-25',
            'date_installation': '2019-09-05',
            'localisation': 'Zone Production B',
            'statut': 'En maintenance',
            'derniere_maintenance': '2024-07-10',
            'prochaine_maintenance': '2025-01-10',
            'notes': 'Moteur de convoyeur principal'
        },
        {
            'nom': 'Équipement Test Invalide',
            'code': '',  # Code vide pour tester
            'modele': 'Test Model',
            'numero_serie': '',
            'fabricant': 'Test Manufacturer',
            'date_achat': 'date_invalide',  # Date invalide pour tester
            'date_installation': '2024-13-45',  # Date invalide
            'localisation': 'Zone Test',
            'statut': 'Statut Invalide',  # Statut invalide
            'derniere_maintenance': '',
            'prochaine_maintenance': '',
            'notes': 'Équipement de test avec erreurs'
        }
    ]
    
    csv_file = 'test_equipment_import.csv'
    
    with open(csv_file, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = test_data[0].keys()
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        writer.writeheader()
        for row in test_data:
            writer.writerow(row)
    
    print(f"✓ Fichier CSV de test créé : {csv_file}")
    return csv_file

def test_equipment_import():
    """Test de l'importation des équipements"""
    print("🔧 TEST DE L'IMPORTATION DES ÉQUIPEMENTS")
    print("=" * 50)
    
    try:
        from database import Database
        
        # Créer le fichier CSV de test
        csv_file = create_test_csv()
        
        # Initialiser la base de données
        db = Database()
        print("✓ Base de données initialisée")
        
        # Compter les équipements avant import
        equipment_before = db.get_all_equipment()
        count_before = len(equipment_before)
        print(f"✓ Équipements avant import : {count_before}")
        
        # Tester l'importation
        print("\n📥 Lancement de l'importation...")
        result = db.import_equipment_from_csv(csv_file)
        
        # Analyser les résultats
        print(f"\n📊 RÉSULTATS DE L'IMPORTATION :")
        print(f"   • Succès : {'✅ Oui' if result['success'] else '❌ Non'}")
        print(f"   • Équipements importés : {result['imported_count']}")
        print(f"   • Lignes traitées : {result['total_rows']}")
        print(f"   • Erreurs : {len(result['errors'])}")
        
        if result['errors']:
            print(f"\n⚠️ ERREURS DÉTECTÉES :")
            for i, error in enumerate(result['errors'][:5], 1):
                print(f"   {i}. {error}")
            if len(result['errors']) > 5:
                print(f"   ... et {len(result['errors']) - 5} autres erreurs")
        
        # Vérifier les équipements après import
        equipment_after = db.get_all_equipment()
        count_after = len(equipment_after)
        print(f"\n✓ Équipements après import : {count_after}")
        print(f"✓ Nouveaux équipements : {count_after - count_before}")
        
        # Afficher les équipements importés
        if count_after > count_before:
            print(f"\n📋 ÉQUIPEMENTS IMPORTÉS :")
            for equipment in equipment_after[count_before:]:
                print(f"   • {equipment[1]} ({equipment[2] if equipment[2] else 'Sans code'}) - {equipment[8]}")
        
        # Test de recherche des équipements importés
        print(f"\n🔍 TEST DE RECHERCHE :")
        search_results = db.search_equipment("Compresseur")
        print(f"   • Recherche 'Compresseur' : {len(search_results)} résultat(s)")
        
        search_results = db.search_equipment("Pompe")
        print(f"   • Recherche 'Pompe' : {len(search_results)} résultat(s)")
        
        # Nettoyer le fichier de test
        try:
            os.remove(csv_file)
            print(f"✓ Fichier de test supprimé")
        except:
            pass
        
        db.close()
        
        # Évaluer le succès du test
        success = (
            result['success'] and 
            result['imported_count'] > 0 and 
            count_after > count_before
        )
        
        return success
        
    except Exception as e:
        print(f"\n❌ ERREUR DANS LE TEST : {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_csv_formats():
    """Test avec différents formats de CSV"""
    print("\n📄 TEST DES FORMATS CSV")
    print("-" * 30)
    
    try:
        from database import Database
        
        # Test avec format anglais
        english_data = [
            {
                'name': 'Test Equipment EN',
                'code': 'TEST-EN-001',
                'model': 'Test Model EN',
                'serial_number': 'SN123EN',
                'manufacturer': 'Test Manufacturer EN',
                'purchase_date': '2024-01-15',
                'installation_date': '2024-02-01',
                'location': 'Test Location EN',
                'status': 'En service',
                'last_maintenance_date': '2024-06-15',
                'next_maintenance_date': '2024-12-15',
                'notes': 'Test equipment with English headers'
            }
        ]
        
        csv_file_en = 'test_equipment_english.csv'
        
        with open(csv_file_en, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = english_data[0].keys()
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for row in english_data:
                writer.writerow(row)
        
        print("✓ Fichier CSV anglais créé")
        
        # Test d'importation
        db = Database()
        result = db.import_equipment_from_csv(csv_file_en)
        
        print(f"✓ Import format anglais : {result['imported_count']} équipement(s)")
        
        # Nettoyer
        try:
            os.remove(csv_file_en)
        except:
            pass
        
        db.close()
        return result['imported_count'] > 0
        
    except Exception as e:
        print(f"❌ Erreur test formats : {str(e)}")
        return False

def main():
    """Point d'entrée principal"""
    print("🔧 SOTRAMINE PHOSPHATE - TEST IMPORTATION ÉQUIPEMENTS")
    print("Version 2.1 - Importation CSV")
    print("=" * 60)
    
    tests = [
        ("Importation équipements", test_equipment_import),
        ("Formats CSV", test_csv_formats)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\n🎯 {test_name.upper()}")
            success = test_func()
            results.append((test_name, success))
            
            if success:
                print(f"✅ {test_name} - SUCCÈS")
            else:
                print(f"❌ {test_name} - ÉCHEC")
                
        except Exception as e:
            print(f"❌ Erreur critique dans {test_name} : {str(e)}")
            results.append((test_name, False))
    
    # Résumé final
    print("\n" + "=" * 60)
    print("📋 RÉSUMÉ DES TESTS D'IMPORTATION")
    print("=" * 60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ SUCCÈS" if success else "❌ ÉCHEC"
        print(f"{status:<12} {test_name}")
    
    print(f"\n📊 RÉSULTATS FINAUX :")
    print(f"✅ Tests réussis : {passed}/{total}")
    print(f"📈 Taux de réussite : {(passed/total*100):.1f}%")
    
    if passed == total:
        print("\n🎉 IMPORTATION DES ÉQUIPEMENTS FONCTIONNELLE !")
        print("✅ L'importation CSV des équipements fonctionne correctement")
        
        print("\n📝 FONCTIONNALITÉS VALIDÉES :")
        print("   • Importation depuis fichier CSV")
        print("   • Validation des données")
        print("   • Gestion des erreurs")
        print("   • Mapping automatique des colonnes")
        print("   • Formats de dates multiples")
        print("   • Détection des doublons")
        
        print("\n🚀 Pour importer des équipements :")
        print("   1. Préparer un fichier CSV avec les colonnes appropriées")
        print("   2. Utiliser la méthode import_equipment_from_csv()")
        print("   3. Vérifier les résultats et erreurs")
    else:
        print(f"\n⚠️ {total - passed} test(s) ont échoué")
        print("🔧 L'importation des équipements nécessite des corrections")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        print(f"\n👋 Test d'importation terminé - {'Succès' if success else 'Échec'}")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 Test interrompu par l'utilisateur")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Erreur critique : {str(e)}")
        sys.exit(1)
