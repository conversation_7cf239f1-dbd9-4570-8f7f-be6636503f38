"""
Module complet de gestion du personnel pour SOTRAMINE PHOSPHATE
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                             QTableWidgetItem, QPushButton, QLabel, QFrame, 
                             QComboBox, QLineEdit, QTextEdit, QDateEdit, 
                             QDialog, QFormLayout, QMessageBox, QHeaderView,
                             QSplitter, QTabWidget, QGroupBox, QGridLayout,
                             QSpinBox, QDoubleSpinBox, QCheckBox)
from PyQt5.QtCore import Qt, QDate, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QPixmap
from utils.data_refresh_manager import RefreshableWidget, notify_data_changed

class PersonnelManagerComplete(QWidget, RefreshableWidget):
    """Gestionnaire complet du personnel"""
    
    def __init__(self, db, parent=None):
        super().__init__(parent)
        RefreshableWidget.__init__(self)
        self.db = db
        self.setup_ui()
        self.load_personnel()
        self.enable_auto_refresh(['personnel'])
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # En-tête
        header = self.create_header()
        layout.addWidget(header)
        
        # Contenu principal avec splitter
        splitter = QSplitter(Qt.Horizontal)
        
        # Panel de gauche - Liste du personnel
        left_panel = self.create_personnel_panel()
        splitter.addWidget(left_panel)
        
        # Panel de droite - Détails
        right_panel = self.create_details_panel()
        splitter.addWidget(right_panel)
        
        splitter.setSizes([700, 500])
        layout.addWidget(splitter)
    
    def create_header(self):
        """Crée l'en-tête du module"""
        header = QFrame()
        header.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #27ae60, stop:1 #229954);
                padding: 20px;
                border-radius: 0;
            }
        """)
        header.setFixedHeight(80)
        
        layout = QHBoxLayout(header)
        
        # Titre
        title = QLabel("👥 GESTION DU PERSONNEL")
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24px;
                font-weight: bold;
            }
        """)
        layout.addWidget(title)
        
        layout.addStretch()
        
        # Boutons d'actions
        actions = [
            ("➕ Nouveau Personnel", "#3498db", self.create_new_person),
            ("📊 Statistiques", "#9b59b6", self.show_statistics),
            ("📄 Exporter", "#f39c12", self.export_personnel),
            ("📋 Organigramme", "#e74c3c", self.show_organigramme)
        ]
        
        for text, color, action in actions:
            btn = QPushButton(text)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 10px 15px;
                    font-weight: bold;
                    margin-left: 5px;
                }}
                QPushButton:hover {{
                    background-color: {color}dd;
                }}
            """)
            btn.clicked.connect(action)
            layout.addWidget(btn)
        
        return header
    
    def create_personnel_panel(self):
        """Crée le panel du personnel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Filtres et recherche
        filters_frame = self.create_filters()
        layout.addWidget(filters_frame)
        
        # Table du personnel
        self.personnel_table = QTableWidget()
        self.personnel_table.setColumnCount(8)
        self.personnel_table.setHorizontalHeaderLabels([
            "ID", "Nom", "Prénom", "Poste", "Département", "Email", "Téléphone", "Statut"
        ])
        
        # Style de la table
        self.personnel_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #ddd;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eee;
            }
            QTableWidget::item:selected {
                background-color: #27ae60;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)
        
        self.personnel_table.setAlternatingRowColors(True)
        self.personnel_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.personnel_table.horizontalHeader().setStretchLastSection(True)
        self.personnel_table.itemSelectionChanged.connect(self.on_person_selected)
        
        layout.addWidget(self.personnel_table)
        
        return panel
    
    def create_filters(self):
        """Crée les filtres"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 15px;
            }
        """)
        
        layout = QHBoxLayout(frame)
        
        # Filtre par département
        layout.addWidget(QLabel("Département:"))
        self.department_filter = QComboBox()
        self.department_filter.addItems(["Tous", "Production", "Maintenance", "Qualité", "Administration", "Sécurité"])
        self.department_filter.currentTextChanged.connect(self.filter_personnel)
        layout.addWidget(self.department_filter)
        
        # Filtre par statut
        layout.addWidget(QLabel("Statut:"))
        self.status_filter = QComboBox()
        self.status_filter.addItems(["Tous", "Actif", "Congé", "Formation", "Inactif"])
        self.status_filter.currentTextChanged.connect(self.filter_personnel)
        layout.addWidget(self.status_filter)
        
        # Recherche
        layout.addWidget(QLabel("Recherche:"))
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Nom, prénom ou poste...")
        self.search_edit.textChanged.connect(self.filter_personnel)
        layout.addWidget(self.search_edit)
        
        layout.addStretch()
        
        return frame
    
    def create_details_panel(self):
        """Crée le panel des détails"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Titre
        title = QLabel("👤 Détails du Personnel")
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # Onglets de détails
        self.details_tabs = QTabWidget()
        self.details_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #ddd;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f8f9fa;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: #27ae60;
                color: white;
            }
        """)
        
        # Onglets
        self.info_tab = self.create_info_tab()
        self.details_tabs.addTab(self.info_tab, "Informations")
        
        self.competences_tab = self.create_competences_tab()
        self.details_tabs.addTab(self.competences_tab, "Compétences")
        
        self.formations_tab = self.create_formations_tab()
        self.details_tabs.addTab(self.formations_tab, "Formations")
        
        self.evaluations_tab = self.create_evaluations_tab()
        self.details_tabs.addTab(self.evaluations_tab, "Évaluations")
        
        layout.addWidget(self.details_tabs)
        
        # Boutons d'actions
        actions_frame = self.create_actions_frame()
        layout.addWidget(actions_frame)
        
        return panel
    
    def create_info_tab(self):
        """Crée l'onglet d'informations personnelles"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # Informations personnelles
        personal_group = QGroupBox("Informations Personnelles")
        personal_layout = QGridLayout(personal_group)
        
        self.info_labels = {}
        personal_fields = [
            ("ID:", "id_label", 0, 0),
            ("Nom:", "last_name_label", 0, 1),
            ("Prénom:", "first_name_label", 1, 0),
            ("Date de naissance:", "birth_date_label", 1, 1),
            ("Email:", "email_label", 2, 0),
            ("Téléphone:", "phone_label", 2, 1),
            ("Adresse:", "address_label", 3, 0)
        ]
        
        for field_name, label_key, row, col in personal_fields:
            name_label = QLabel(field_name)
            name_label.setStyleSheet("font-weight: bold;")
            personal_layout.addWidget(name_label, row * 2, col * 2)
            
            value_label = QLabel("-")
            value_label.setStyleSheet("color: #495057; margin-bottom: 10px;")
            personal_layout.addWidget(value_label, row * 2 + 1, col * 2)
            
            self.info_labels[label_key] = value_label
        
        layout.addWidget(personal_group)
        
        # Informations professionnelles
        professional_group = QGroupBox("Informations Professionnelles")
        professional_layout = QGridLayout(professional_group)
        
        professional_fields = [
            ("Poste:", "position_label", 0, 0),
            ("Département:", "department_label", 0, 1),
            ("Manager:", "manager_label", 1, 0),
            ("Date d'embauche:", "hire_date_label", 1, 1),
            ("Statut:", "status_label", 2, 0),
            ("Salaire:", "salary_label", 2, 1)
        ]
        
        for field_name, label_key, row, col in professional_fields:
            name_label = QLabel(field_name)
            name_label.setStyleSheet("font-weight: bold;")
            professional_layout.addWidget(name_label, row * 2, col * 2)
            
            value_label = QLabel("-")
            value_label.setStyleSheet("color: #495057; margin-bottom: 10px;")
            professional_layout.addWidget(value_label, row * 2 + 1, col * 2)
            
            self.info_labels[label_key] = value_label
        
        layout.addWidget(professional_group)
        layout.addStretch()
        
        return tab
    
    def create_competences_tab(self):
        """Crée l'onglet des compétences"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # Compétences techniques
        tech_group = QGroupBox("Compétences Techniques")
        tech_layout = QVBoxLayout(tech_group)
        
        self.tech_competences = QTableWidget()
        self.tech_competences.setColumnCount(3)
        self.tech_competences.setHorizontalHeaderLabels(["Compétence", "Niveau", "Certifié"])
        self.tech_competences.horizontalHeader().setStretchLastSection(True)
        self.tech_competences.setMaximumHeight(150)
        
        # Ajouter quelques compétences d'exemple
        tech_data = [
            ("Maintenance électrique", "Expert", "Oui"),
            ("Soudure", "Intermédiaire", "Oui"),
            ("Hydraulique", "Débutant", "Non"),
            ("Automatisme", "Avancé", "Oui")
        ]
        
        self.tech_competences.setRowCount(len(tech_data))
        for row, data in enumerate(tech_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(value)
                if col == 1:  # Niveau
                    if value == "Expert":
                        item.setBackground(QColor("#d4edda"))
                    elif value == "Avancé":
                        item.setBackground(QColor("#d1ecf1"))
                    elif value == "Intermédiaire":
                        item.setBackground(QColor("#fff3cd"))
                    else:
                        item.setBackground(QColor("#f8d7da"))
                self.tech_competences.setItem(row, col, item)
        
        tech_layout.addWidget(self.tech_competences)
        layout.addWidget(tech_group)
        
        # Compétences transversales
        soft_group = QGroupBox("Compétences Transversales")
        soft_layout = QVBoxLayout(soft_group)
        
        self.soft_competences = QTableWidget()
        self.soft_competences.setColumnCount(2)
        self.soft_competences.setHorizontalHeaderLabels(["Compétence", "Évaluation"])
        self.soft_competences.horizontalHeader().setStretchLastSection(True)
        self.soft_competences.setMaximumHeight(120)
        
        # Ajouter quelques compétences transversales
        soft_data = [
            ("Leadership", "Excellent"),
            ("Communication", "Bon"),
            ("Travail d'équipe", "Excellent"),
            ("Résolution de problèmes", "Très bon")
        ]
        
        self.soft_competences.setRowCount(len(soft_data))
        for row, data in enumerate(soft_data):
            for col, value in enumerate(data):
                self.soft_competences.setItem(row, col, QTableWidgetItem(value))
        
        soft_layout.addWidget(self.soft_competences)
        layout.addWidget(soft_group)
        
        layout.addStretch()
        
        return tab
    
    def create_formations_tab(self):
        """Crée l'onglet des formations"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # Toolbar pour formations
        formation_toolbar = QHBoxLayout()
        
        btn_add_formation = QPushButton("➕ Ajouter Formation")
        btn_add_formation.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        btn_add_formation.clicked.connect(self.add_formation)
        formation_toolbar.addWidget(btn_add_formation)
        
        formation_toolbar.addStretch()
        layout.addLayout(formation_toolbar)
        
        # Table des formations
        self.formations_table = QTableWidget()
        self.formations_table.setColumnCount(5)
        self.formations_table.setHorizontalHeaderLabels(["Formation", "Organisme", "Date", "Durée", "Statut"])
        self.formations_table.horizontalHeader().setStretchLastSection(True)
        
        # Ajouter quelques formations d'exemple
        formations_data = [
            ("Sécurité au travail", "INRS", "2025-01-15", "2 jours", "Terminée"),
            ("Maintenance préventive", "AFPA", "2025-03-10", "5 jours", "Terminée"),
            ("Automatisme industriel", "CNAM", "2025-06-01", "3 semaines", "En cours"),
            ("Gestion d'équipe", "Formation Pro", "2025-09-15", "2 jours", "Planifiée")
        ]
        
        self.formations_table.setRowCount(len(formations_data))
        for row, data in enumerate(formations_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(value)
                if col == 4:  # Statut
                    if value == "Terminée":
                        item.setBackground(QColor("#d4edda"))
                    elif value == "En cours":
                        item.setBackground(QColor("#fff3cd"))
                    elif value == "Planifiée":
                        item.setBackground(QColor("#d1ecf1"))
                self.formations_table.setItem(row, col, item)
        
        layout.addWidget(self.formations_table)
        
        return tab
    
    def create_evaluations_tab(self):
        """Crée l'onglet des évaluations"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # Dernière évaluation
        last_eval_group = QGroupBox("Dernière Évaluation")
        last_eval_layout = QGridLayout(last_eval_group)
        
        self.eval_labels = {}
        eval_fields = [
            ("Date:", "eval_date_label", 0, 0),
            ("Évaluateur:", "evaluator_label", 0, 1),
            ("Note globale:", "global_score_label", 1, 0),
            ("Objectifs atteints:", "objectives_label", 1, 1)
        ]
        
        for field_name, label_key, row, col in eval_fields:
            name_label = QLabel(field_name)
            name_label.setStyleSheet("font-weight: bold;")
            last_eval_layout.addWidget(name_label, row * 2, col * 2)
            
            value_label = QLabel("-")
            value_label.setStyleSheet("color: #495057; margin-bottom: 10px;")
            last_eval_layout.addWidget(value_label, row * 2 + 1, col * 2)
            
            self.eval_labels[label_key] = value_label
        
        layout.addWidget(last_eval_group)
        
        # Historique des évaluations
        history_group = QGroupBox("Historique des Évaluations")
        history_layout = QVBoxLayout(history_group)
        
        self.evaluations_table = QTableWidget()
        self.evaluations_table.setColumnCount(4)
        self.evaluations_table.setHorizontalHeaderLabels(["Date", "Évaluateur", "Note", "Commentaires"])
        self.evaluations_table.horizontalHeader().setStretchLastSection(True)
        
        # Ajouter quelques évaluations d'exemple
        eval_data = [
            ("2025-01-15", "Marie Dupont", "4.2/5", "Excellent travail, très autonome"),
            ("2024-07-15", "Pierre Martin", "3.8/5", "Bon niveau, à améliorer en communication"),
            ("2024-01-15", "Sophie Bernard", "4.0/5", "Très professionnel, ponctuel")
        ]
        
        self.evaluations_table.setRowCount(len(eval_data))
        for row, data in enumerate(eval_data):
            for col, value in enumerate(data):
                self.evaluations_table.setItem(row, col, QTableWidgetItem(value))
        
        history_layout.addWidget(self.evaluations_table)
        layout.addWidget(history_group)
        
        return tab
    
    def create_actions_frame(self):
        """Crée le cadre des actions"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                margin-top: 15px;
            }
        """)
        
        layout = QHBoxLayout(frame)
        
        # Boutons d'actions
        actions = [
            ("✏️ Modifier", "#f39c12", self.edit_person),
            ("📋 Évaluer", "#3498db", self.evaluate_person),
            ("🎓 Formation", "#9b59b6", self.add_formation),
            ("❌ Supprimer", "#e74c3c", self.delete_person)
        ]
        
        for text, color, action in actions:
            btn = QPushButton(text)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 16px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: {color}dd;
                }}
                QPushButton:disabled {{
                    background-color: #bdc3c7;
                }}
            """)
            btn.clicked.connect(action)
            btn.setEnabled(False)  # Désactivé par défaut
            layout.addWidget(btn)
            
            # Stocker les boutons
            if text == "✏️ Modifier":
                self.edit_btn = btn
            elif text == "📋 Évaluer":
                self.evaluate_btn = btn
            elif text == "🎓 Formation":
                self.formation_btn = btn
            elif text == "❌ Supprimer":
                self.delete_btn = btn
        
        layout.addStretch()
        
        return frame
    
    def load_personnel(self):
        """Charge le personnel depuis la base de données"""
        try:
            # Simuler des données de personnel
            personnel_data = [
                (1, "Dupont", "Jean", "Technicien Maintenance", "Maintenance", "<EMAIL>", "***********.89", "Actif"),
                (2, "Martin", "Marie", "Ingénieur Qualité", "Qualité", "<EMAIL>", "***********.90", "Actif"),
                (3, "Durand", "Pierre", "Opérateur Production", "Production", "<EMAIL>", "***********.91", "Congé"),
                (4, "Bernard", "Sophie", "Responsable Sécurité", "Sécurité", "<EMAIL>", "***********.92", "Actif"),
                (5, "Moreau", "Luc", "Chef d'Équipe", "Production", "<EMAIL>", "***********.93", "Formation")
            ]
            
            self.all_personnel = personnel_data
            self.populate_table(personnel_data)
            
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement du personnel : {str(e)}")
    
    def populate_table(self, personnel_data):
        """Remplit la table avec le personnel"""
        self.personnel_table.setRowCount(len(personnel_data))
        
        for row, person in enumerate(personnel_data):
            for col, value in enumerate(person):
                item = QTableWidgetItem(str(value))
                
                # Colorer selon le statut
                if col == 7:  # Colonne statut
                    if value == "Actif":
                        item.setBackground(QColor("#d4edda"))
                    elif value == "Congé":
                        item.setBackground(QColor("#fff3cd"))
                    elif value == "Formation":
                        item.setBackground(QColor("#d1ecf1"))
                    elif value == "Inactif":
                        item.setBackground(QColor("#f8d7da"))
                
                self.personnel_table.setItem(row, col, item)
        
        # Ajuster les colonnes
        self.personnel_table.resizeColumnsToContents()
    
    def filter_personnel(self):
        """Filtre le personnel selon les critères"""
        department_filter = self.department_filter.currentText()
        status_filter = self.status_filter.currentText()
        search_text = self.search_edit.text().lower()
        
        filtered_personnel = []
        
        for person in self.all_personnel:
            # Filtre par département
            if department_filter != "Tous" and person[4] != department_filter:
                continue
            
            # Filtre par statut
            if status_filter != "Tous" and person[7] != status_filter:
                continue
            
            # Filtre par recherche
            if search_text:
                searchable_text = f"{person[1]} {person[2]} {person[3]}".lower()
                if search_text not in searchable_text:
                    continue
            
            filtered_personnel.append(person)
        
        self.populate_table(filtered_personnel)
    
    def on_person_selected(self):
        """Gère la sélection d'une personne"""
        current_row = self.personnel_table.currentRow()
        if current_row >= 0:
            # Activer les boutons d'actions
            self.edit_btn.setEnabled(True)
            self.evaluate_btn.setEnabled(True)
            self.formation_btn.setEnabled(True)
            self.delete_btn.setEnabled(True)
            
            # Charger les détails
            self.load_person_details(current_row)
        else:
            # Désactiver les boutons
            self.edit_btn.setEnabled(False)
            self.evaluate_btn.setEnabled(False)
            self.formation_btn.setEnabled(False)
            self.delete_btn.setEnabled(False)
    
    def load_person_details(self, row):
        """Charge les détails d'une personne"""
        # Récupérer les données de la ligne
        person_data = []
        for col in range(self.personnel_table.columnCount()):
            item = self.personnel_table.item(row, col)
            if item:
                person_data.append(item.text())
            else:
                person_data.append("")
        
        # Mettre à jour les labels d'informations
        if len(person_data) >= 8:
            self.info_labels["id_label"].setText(person_data[0])
            self.info_labels["last_name_label"].setText(person_data[1])
            self.info_labels["first_name_label"].setText(person_data[2])
            self.info_labels["position_label"].setText(person_data[3])
            self.info_labels["department_label"].setText(person_data[4])
            self.info_labels["email_label"].setText(person_data[5])
            self.info_labels["phone_label"].setText(person_data[6])
            self.info_labels["status_label"].setText(person_data[7])
            
            # Informations supplémentaires (simulées)
            self.info_labels["birth_date_label"].setText("15/03/1985")
            self.info_labels["address_label"].setText("123 Rue de la Paix, 75001 Paris")
            self.info_labels["manager_label"].setText("Directeur Technique")
            self.info_labels["hire_date_label"].setText("01/09/2020")
            self.info_labels["salary_label"].setText("3500€/mois")
            
            # Dernière évaluation
            self.eval_labels["eval_date_label"].setText("15/01/2025")
            self.eval_labels["evaluator_label"].setText("Marie Dupont")
            self.eval_labels["global_score_label"].setText("4.2/5")
            self.eval_labels["objectives_label"].setText("85%")
    
    # Méthodes d'actions
    def create_new_person(self):
        """Crée une nouvelle personne"""
        dialog = PersonDialog(self.db, self)
        if dialog.exec_() == QDialog.Accepted:
            self.load_personnel()
            notify_data_changed('personnel')
    
    def edit_person(self):
        """Modifie la personne sélectionnée"""
        current_row = self.personnel_table.currentRow()
        if current_row >= 0:
            person_id = self.personnel_table.item(current_row, 0).text()
            dialog = PersonDialog(self.db, self, person_id)
            if dialog.exec_() == QDialog.Accepted:
                self.load_personnel()
                notify_data_changed('personnel')
    
    def delete_person(self):
        """Supprime la personne sélectionnée"""
        current_row = self.personnel_table.currentRow()
        if current_row >= 0:
            person_name = f"{self.personnel_table.item(current_row, 2).text()} {self.personnel_table.item(current_row, 1).text()}"
            reply = QMessageBox.question(self, "Confirmer", 
                                       f"Êtes-vous sûr de vouloir supprimer '{person_name}' ?",
                                       QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.Yes:
                self.personnel_table.removeRow(current_row)
                notify_data_changed('personnel')
                QMessageBox.information(self, "Succès", "Personne supprimée !")
    
    def evaluate_person(self):
        """Évalue la personne sélectionnée"""
        QMessageBox.information(self, "Évaluation", 
                               "📋 Évaluation du personnel\n\n"
                               "Cette fonctionnalité ouvrira un dialog pour :\n"
                               "• Saisir les critères d'évaluation\n"
                               "• Attribuer des notes\n"
                               "• Ajouter des commentaires\n"
                               "• Définir des objectifs")
    
    def add_formation(self):
        """Ajoute une formation"""
        QMessageBox.information(self, "Formation", 
                               "🎓 Ajout de formation\n\n"
                               "Cette fonctionnalité ouvrira un dialog pour :\n"
                               "• Sélectionner le type de formation\n"
                               "• Choisir l'organisme\n"
                               "• Planifier les dates\n"
                               "• Estimer les coûts")
    
    def show_statistics(self):
        """Affiche les statistiques du personnel"""
        QMessageBox.information(self, "Statistiques Personnel", 
                               "📊 Statistiques du personnel\n\n"
                               "• Total employés : 5\n"
                               "• Actifs : 3 (60%)\n"
                               "• En congé : 1 (20%)\n"
                               "• En formation : 1 (20%)\n\n"
                               "Par département :\n"
                               "• Production : 2\n"
                               "• Maintenance : 1\n"
                               "• Qualité : 1\n"
                               "• Sécurité : 1")
    
    def export_personnel(self):
        """Exporte le personnel vers Excel"""
        try:
            from export.excel_export import ExcelExporter
            exporter = ExcelExporter(self.db)
            
            file_path = "export_personnel.xlsx"
            success = exporter.export_personnel_list(file_path)
            
            if success:
                QMessageBox.information(self, "Export Réussi", 
                                       f"📄 Export Excel réussi !\n\nFichier : {file_path}")
            else:
                QMessageBox.warning(self, "Erreur Export", 
                                   "❌ Erreur lors de l'export Excel.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'export : {str(e)}")
    
    def show_organigramme(self):
        """Affiche l'organigramme"""
        QMessageBox.information(self, "Organigramme", 
                               "📋 Organigramme de l'entreprise\n\n"
                               "Cette fonctionnalité affichera :\n"
                               "• Structure hiérarchique\n"
                               "• Relations managériales\n"
                               "• Répartition par département\n"
                               "• Postes vacants")
    
    def refresh_data(self):
        """Actualise les données"""
        self.load_personnel()


class PersonDialog(QDialog):
    """Dialog pour créer/modifier une personne"""
    
    def __init__(self, db, parent=None, person_id=None):
        super().__init__(parent)
        self.db = db
        self.person_id = person_id
        self.setup_ui()
        
        if person_id:
            self.setWindowTitle("Modifier le Personnel")
            self.load_person_data()
        else:
            self.setWindowTitle("Nouveau Personnel")
    
    def setup_ui(self):
        """Configure l'interface du dialog"""
        self.setFixedSize(600, 700)
        layout = QVBoxLayout(self)
        
        # Onglets
        tabs = QTabWidget()
        
        # Onglet informations personnelles
        personal_tab = QWidget()
        personal_layout = QFormLayout(personal_tab)
        
        self.first_name_edit = QLineEdit()
        personal_layout.addRow("Prénom:", self.first_name_edit)
        
        self.last_name_edit = QLineEdit()
        personal_layout.addRow("Nom:", self.last_name_edit)
        
        self.birth_date = QDateEdit()
        self.birth_date.setDate(QDate.currentDate().addYears(-30))
        self.birth_date.setCalendarPopup(True)
        personal_layout.addRow("Date de naissance:", self.birth_date)
        
        self.email_edit = QLineEdit()
        personal_layout.addRow("Email:", self.email_edit)
        
        self.phone_edit = QLineEdit()
        personal_layout.addRow("Téléphone:", self.phone_edit)
        
        self.address_edit = QTextEdit()
        self.address_edit.setMaximumHeight(60)
        personal_layout.addRow("Adresse:", self.address_edit)
        
        tabs.addTab(personal_tab, "Personnel")
        
        # Onglet professionnel
        professional_tab = QWidget()
        professional_layout = QFormLayout(professional_tab)
        
        self.position_edit = QLineEdit()
        professional_layout.addRow("Poste:", self.position_edit)
        
        self.department_combo = QComboBox()
        self.department_combo.addItems(["Production", "Maintenance", "Qualité", "Administration", "Sécurité"])
        professional_layout.addRow("Département:", self.department_combo)
        
        self.manager_edit = QLineEdit()
        professional_layout.addRow("Manager:", self.manager_edit)
        
        self.hire_date = QDateEdit()
        self.hire_date.setDate(QDate.currentDate())
        self.hire_date.setCalendarPopup(True)
        professional_layout.addRow("Date d'embauche:", self.hire_date)
        
        self.salary_spin = QDoubleSpinBox()
        self.salary_spin.setRange(1000, 10000)
        self.salary_spin.setValue(2500)
        self.salary_spin.setSuffix(" €")
        professional_layout.addRow("Salaire:", self.salary_spin)
        
        self.status_combo = QComboBox()
        self.status_combo.addItems(["Actif", "Congé", "Formation", "Inactif"])
        professional_layout.addRow("Statut:", self.status_combo)
        
        tabs.addTab(professional_tab, "Professionnel")
        
        layout.addWidget(tabs)
        
        # Boutons
        buttons_layout = QHBoxLayout()
        
        btn_save = QPushButton("💾 Enregistrer")
        btn_save.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        btn_save.clicked.connect(self.save_person)
        buttons_layout.addWidget(btn_save)
        
        btn_cancel = QPushButton("❌ Annuler")
        btn_cancel.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        btn_cancel.clicked.connect(self.reject)
        buttons_layout.addWidget(btn_cancel)
        
        layout.addLayout(buttons_layout)
    
    def load_person_data(self):
        """Charge les données de la personne à modifier"""
        # Simuler le chargement des données
        self.first_name_edit.setText("Jean")
        self.last_name_edit.setText("Dupont")
        self.email_edit.setText("<EMAIL>")
        self.phone_edit.setText("***********.89")
        self.address_edit.setText("123 Rue de la Paix, 75001 Paris")
        self.position_edit.setText("Technicien Maintenance")
        self.department_combo.setCurrentText("Maintenance")
        self.manager_edit.setText("Directeur Technique")
        self.status_combo.setCurrentText("Actif")
        self.salary_spin.setValue(3500)
    
    def save_person(self):
        """Sauvegarde la personne"""
        if not self.first_name_edit.text().strip():
            QMessageBox.warning(self, "Erreur", "Le prénom est obligatoire !")
            return
        
        if not self.last_name_edit.text().strip():
            QMessageBox.warning(self, "Erreur", "Le nom est obligatoire !")
            return
        
        # Simuler la sauvegarde
        QMessageBox.information(self, "Succès", "Personnel sauvegardé avec succès !")
        self.accept()
