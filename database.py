import sqlite3
from datetime import datetime, timedelta
import os
import threading
import time
from functools import lru_cache
from config import DATABASE_CONFIG, get_database_path
from utils.cache_manager import cached, invalidate_cache
from utils.performance import monitor_performance
from utils.logger import get_logger

logger = get_logger("DATABASE")

class Database:
    def __init__(self, db_path="data/tasks.db"):
        # Cache pour les données fréquemment utilisées
        self._cache = {}
        self._cache_timestamps = {}
        self._cache_ttl = 300  # 5 minutes de TTL pour le cache
        self._cache_lock = threading.RLock()
        # Créer le dossier data s'il n'existe pas
        os.makedirs(os.path.dirname(db_path), exist_ok=True)

        # Optimisations de connexion SQLite
        self.conn = sqlite3.connect(
            db_path,
            timeout=30.0,
            check_same_thread=False,
            isolation_level=None  # Autocommit mode pour de meilleures performances
        )

        # Optimisations SQLite pour de meilleures performances
        self.conn.execute("PRAGMA journal_mode=WAL")  # Write-Ahead Logging
        self.conn.execute("PRAGMA synchronous=NORMAL")  # Balance entre sécurité et performance
        self.conn.execute("PRAGMA cache_size=10000")  # Cache plus important
        self.conn.execute("PRAGMA temp_store=MEMORY")  # Tables temporaires en mémoire
        self.conn.execute("PRAGMA mmap_size=268435456")  # Memory-mapped I/O (256MB)
        self.conn.execute("PRAGMA foreign_keys=ON")  # Activer les clés étrangères

        self.cursor = self.conn.cursor()
        self.operation_count = 0  # Compteur pour l'optimisation périodique
        self.create_tables()
        self.update_database_schema()

    def create_tables(self):
        # Table des personnes
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS persons (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            role TEXT
        )
        ''')

        # Table des catégories
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            color TEXT
        )
        ''')

        # Table des équipements
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS equipment (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code TEXT UNIQUE,
            name TEXT NOT NULL,
            model TEXT,
            serial_number TEXT,
            manufacturer TEXT,
            purchase_date TEXT,
            installation_date TEXT,
            location TEXT,
            status TEXT CHECK(status IN ('En service', 'En maintenance', 'Hors service', 'En stock')),
            last_maintenance_date TEXT,
            next_maintenance_date TEXT,
            notes TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Table des documents techniques
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS equipment_documents (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            equipment_id INTEGER,
            name TEXT NOT NULL,
            file_path TEXT NOT NULL,
            document_type TEXT CHECK(document_type IN ('Manuel', 'Schéma', 'Procédure', 'Fiche technique', 'Autre')),
            description TEXT,
            upload_date TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (equipment_id) REFERENCES equipment (id) ON DELETE CASCADE
        )
        ''')

        # Table des tâches
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS tasks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            category_id INTEGER,
            title TEXT NOT NULL,
            description TEXT,
            due_date TEXT,
            priority TEXT CHECK(priority IN ('basse', 'moyenne', 'haute')),
            status TEXT CHECK(status IN ('À faire', 'En cours', 'Terminée', 'En attente')),
            waiting_reason TEXT,
            estimated_time INTEGER,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            is_recurring INTEGER DEFAULT 0,
            recurrence_type TEXT CHECK(recurrence_type IN ('daily', 'weekly', 'monthly', 'yearly') OR recurrence_type IS NULL),
            recurrence_end_date TEXT,
            parent_task_id INTEGER,
            is_maintenance_checklist INTEGER DEFAULT 0,
            equipment_id INTEGER,
            FOREIGN KEY (category_id) REFERENCES categories (id),
            FOREIGN KEY (parent_task_id) REFERENCES tasks (id),
            FOREIGN KEY (equipment_id) REFERENCES equipment (id)
        )
        ''')

        # Table des sous-tâches
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS subtasks (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            task_id INTEGER,
            title TEXT NOT NULL,
            status TEXT CHECK(status IN ('À faire', 'En cours', 'Terminée', 'En attente')),
            assigned_to_id INTEGER,
            estimated_time INTEGER,
            FOREIGN KEY (task_id) REFERENCES tasks (id) ON DELETE CASCADE,
            FOREIGN KEY (assigned_to_id) REFERENCES persons (id)
        )
        ''')

        # Table des affectations de tâches
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS task_assignments (
            task_id INTEGER,
            person_id INTEGER,
            PRIMARY KEY (task_id, person_id),
            FOREIGN KEY (task_id) REFERENCES tasks (id) ON DELETE CASCADE,
            FOREIGN KEY (person_id) REFERENCES persons (id) ON DELETE CASCADE
        )
        ''')

        # Table des modèles de checklists de maintenance
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS maintenance_checklist_templates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Table des éléments de checklist
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS checklist_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            template_id INTEGER,
            task_id INTEGER,
            title TEXT NOT NULL,
            description TEXT,
            order_index INTEGER,
            is_required INTEGER DEFAULT 1,
            FOREIGN KEY (template_id) REFERENCES maintenance_checklist_templates (id) ON DELETE CASCADE,
            FOREIGN KEY (task_id) REFERENCES tasks (id) ON DELETE CASCADE
        )
        ''')

        # Table des résultats de checklist
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS checklist_results (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            item_id INTEGER,
            task_id INTEGER,
            status TEXT CHECK(status IN ('Non vérifié', 'Conforme', 'Non conforme', 'Non applicable')),
            notes TEXT,
            completed_by INTEGER,
            completed_at TEXT,
            FOREIGN KEY (item_id) REFERENCES checklist_items (id) ON DELETE CASCADE,
            FOREIGN KEY (task_id) REFERENCES tasks (id) ON DELETE CASCADE,
            FOREIGN KEY (completed_by) REFERENCES persons (id)
        )
        ''')

        self.conn.commit()
        
        # Créer les index pour optimiser les performances
        self.create_indexes()

        # Vérifier si les colonnes assigned_to_id existent dans les tables tasks et subtasks
        try:
            self.cursor.execute("SELECT assigned_to_id FROM tasks LIMIT 1")
        except sqlite3.OperationalError:
            # Ajouter la colonne assigned_to_id à la table tasks
            self.cursor.execute("ALTER TABLE tasks ADD COLUMN assigned_to_id INTEGER REFERENCES persons(id)")

        try:
            self.cursor.execute("SELECT assigned_to_id FROM subtasks LIMIT 1")
        except sqlite3.OperationalError:
            # Ajouter la colonne assigned_to_id à la table subtasks
            self.cursor.execute("ALTER TABLE subtasks ADD COLUMN assigned_to_id INTEGER REFERENCES persons(id)")

        self.conn.commit()

        # Vérifier si la colonne color existe dans la table categories
        self.cursor.execute("PRAGMA table_info(categories)")
        columns = [column[1] for column in self.cursor.fetchall()]
        if 'color' not in columns:
            self.cursor.execute('ALTER TABLE categories ADD COLUMN color TEXT')

        # Vérifier si la colonne estimated_time existe dans la table tasks
        self.cursor.execute("PRAGMA table_info(tasks)")
        columns = [column[1] for column in self.cursor.fetchall()]
        if 'estimated_time' not in columns:
            self.cursor.execute('ALTER TABLE tasks ADD COLUMN estimated_time INTEGER')

        # Vérifier si la colonne estimated_time existe dans la table subtasks
        self.cursor.execute("PRAGMA table_info(subtasks)")
        columns = [column[1] for column in self.cursor.fetchall()]
        if 'estimated_time' not in columns:
            self.cursor.execute('ALTER TABLE subtasks ADD COLUMN estimated_time INTEGER')

        # Vérifier si la colonne waiting_reason existe dans la table tasks
        self.cursor.execute("PRAGMA table_info(tasks)")
        columns = [column[1] for column in self.cursor.fetchall()]
        if 'waiting_reason' not in columns:
            self.cursor.execute('ALTER TABLE tasks ADD COLUMN waiting_reason TEXT')

        self.conn.commit()

    def _periodic_optimize(self):
        """Optimise périodiquement la base de données"""
        self.operation_count += 1
        if self.operation_count % 1000 == 0:  # Optimiser toutes les 1000 opérations
            try:
                self.cursor.execute("PRAGMA optimize")
                self.cursor.execute("PRAGMA incremental_vacuum")
                print(f"Optimisation périodique effectuée après {self.operation_count} opérations")
            except Exception as e:
                print(f"Erreur lors de l'optimisation périodique : {str(e)}")

    def _get_from_cache(self, key):
        """Récupère une valeur du cache si elle est valide"""
        with self._cache_lock:
            if key in self._cache:
                timestamp = self._cache_timestamps.get(key, 0)
                if time.time() - timestamp < self._cache_ttl:
                    return self._cache[key]
                else:
                    # Cache expiré, le supprimer
                    del self._cache[key]
                    del self._cache_timestamps[key]
            return None

    def _set_cache(self, key, value):
        """Met une valeur en cache"""
        with self._cache_lock:
            self._cache[key] = value
            self._cache_timestamps[key] = time.time()

    def _clear_cache(self, pattern=None):
        """Vide le cache, optionnellement selon un pattern"""
        with self._cache_lock:
            if pattern is None:
                self._cache.clear()
                self._cache_timestamps.clear()
            else:
                keys_to_remove = [k for k in self._cache.keys() if pattern in k]
                for key in keys_to_remove:
                    del self._cache[key]
                    del self._cache_timestamps[key]

    def _get_cache_key(self, method_name, *args):
        """Génère une clé de cache basée sur le nom de méthode et les arguments"""
        if args:
            return f"{method_name}_{hash(args)}"
        return method_name

    def create_indexes(self):
        """Crée des index pour optimiser les performances des requêtes"""
        try:
            # Index composites pour les tâches (plus efficaces pour les requêtes complexes)
            self.cursor.execute("CREATE INDEX IF NOT EXISTS idx_tasks_category_status ON tasks(category_id, status)")
            self.cursor.execute("CREATE INDEX IF NOT EXISTS idx_tasks_due_date_status ON tasks(due_date, status)")
            self.cursor.execute("CREATE INDEX IF NOT EXISTS idx_tasks_status_priority ON tasks(status, priority)")
            self.cursor.execute("CREATE INDEX IF NOT EXISTS idx_tasks_equipment_status ON tasks(equipment_id, status)")
            self.cursor.execute("CREATE INDEX IF NOT EXISTS idx_tasks_created_at ON tasks(created_at)")

            # Index simples pour les tâches
            self.cursor.execute("CREATE INDEX IF NOT EXISTS idx_tasks_category_id ON tasks(category_id)")
            self.cursor.execute("CREATE INDEX IF NOT EXISTS idx_tasks_due_date ON tasks(due_date)")
            self.cursor.execute("CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status)")
            self.cursor.execute("CREATE INDEX IF NOT EXISTS idx_tasks_priority ON tasks(priority)")
            self.cursor.execute("CREATE INDEX IF NOT EXISTS idx_tasks_equipment_id ON tasks(equipment_id)")
            self.cursor.execute("CREATE INDEX IF NOT EXISTS idx_tasks_parent_id ON tasks(parent_task_id)")

            # Index pour les sous-tâches
            self.cursor.execute("CREATE INDEX IF NOT EXISTS idx_subtasks_task_id ON subtasks(task_id)")
            self.cursor.execute("CREATE INDEX IF NOT EXISTS idx_subtasks_assigned_to_id ON subtasks(assigned_to_id)")
            self.cursor.execute("CREATE INDEX IF NOT EXISTS idx_subtasks_status ON subtasks(status)")

            # Index pour les assignations de tâches
            self.cursor.execute("CREATE INDEX IF NOT EXISTS idx_task_assignments_task_id ON task_assignments(task_id)")
            self.cursor.execute("CREATE INDEX IF NOT EXISTS idx_task_assignments_person_id ON task_assignments(person_id)")

            # Index pour le pointage
            self.cursor.execute("CREATE INDEX IF NOT EXISTS idx_attendance_person_date ON attendance(person_id, date)")
            self.cursor.execute("CREATE INDEX IF NOT EXISTS idx_attendance_date_status ON attendance(date, status)")

            # Index pour les équipements (vérifier que les colonnes existent)
            try:
                self.cursor.execute("CREATE INDEX IF NOT EXISTS idx_equipment_status ON equipment(status)")
                self.cursor.execute("CREATE INDEX IF NOT EXISTS idx_equipment_location ON equipment(location)")
                self.cursor.execute("CREATE INDEX IF NOT EXISTS idx_equipment_next_maintenance ON equipment(next_maintenance_date)")
                # Vérifier si la colonne code existe avant de créer l'index
                self.cursor.execute("PRAGMA table_info(equipment)")
                equipment_columns = [col[1] for col in self.cursor.fetchall()]
                if 'code' in equipment_columns:
                    self.cursor.execute("CREATE INDEX IF NOT EXISTS idx_equipment_code ON equipment(code)")
            except Exception as e:
                print(f"Erreur lors de la création des index équipements : {e}")

            # Index pour les pièces de rechange (vérifier que les colonnes existent)
            try:
                self.cursor.execute("PRAGMA table_info(spare_parts)")
                spare_parts_columns = [col[1] for col in self.cursor.fetchall()]
                if 'code' in spare_parts_columns:
                    self.cursor.execute("CREATE INDEX IF NOT EXISTS idx_spare_parts_code ON spare_parts(code)")
                if 'part_type' in spare_parts_columns:
                    self.cursor.execute("CREATE INDEX IF NOT EXISTS idx_spare_parts_part_type ON spare_parts(part_type)")
                if 'quantity' in spare_parts_columns and 'min_threshold' in spare_parts_columns:
                    self.cursor.execute("CREATE INDEX IF NOT EXISTS idx_spare_parts_quantity_threshold ON spare_parts(quantity, min_threshold)")
            except Exception as e:
                print(f"Erreur lors de la création des index pièces de rechange : {e}")

            # Index pour les documents d'équipement
            self.cursor.execute("CREATE INDEX IF NOT EXISTS idx_equipment_docs_equipment_id ON equipment_documents(equipment_id)")
            self.cursor.execute("CREATE INDEX IF NOT EXISTS idx_equipment_docs_type ON equipment_documents(document_type)")

            # Index pour les checklists
            self.cursor.execute("CREATE INDEX IF NOT EXISTS idx_checklist_items_task_id ON checklist_items(task_id)")
            self.cursor.execute("CREATE INDEX IF NOT EXISTS idx_checklist_items_template_id ON checklist_items(template_id)")
            self.cursor.execute("CREATE INDEX IF NOT EXISTS idx_checklist_results_task_id ON checklist_results(task_id)")

            # Analyser les tables pour optimiser les statistiques
            self.cursor.execute("ANALYZE")

            self.conn.commit()
            print("Index optimisés créés pour améliorer les performances")
        except Exception as e:
            print(f"Erreur lors de la création des index : {str(e)}")

    def _get_cache_key(self, method_name, *args):
        """Génère une clé de cache unique pour une méthode et ses arguments"""
        return f"{method_name}:{':'.join(str(arg) for arg in args)}"

    def _is_cache_valid(self, key):
        """Vérifie si l'entrée du cache est encore valide"""
        if key not in self._cache_timestamps:
            return False
        return time.time() - self._cache_timestamps[key] < self._cache_ttl

    def _get_from_cache(self, key):
        """Récupère une valeur du cache si elle est valide"""
        with self._cache_lock:
            if key in self._cache and self._is_cache_valid(key):
                return self._cache[key]
            return None

    def _set_cache(self, key, value):
        """Met une valeur en cache"""
        with self._cache_lock:
            self._cache[key] = value
            self._cache_timestamps[key] = time.time()

    def _clear_cache(self, pattern=None):
        """Vide le cache, optionnellement selon un pattern"""
        with self._cache_lock:
            if pattern is None:
                self._cache.clear()
                self._cache_timestamps.clear()
            else:
                keys_to_remove = [k for k in self._cache.keys() if pattern in k]
                for key in keys_to_remove:
                    del self._cache[key]
                    del self._cache_timestamps[key]

    @monitor_performance("add_category")
    def add_category(self, name, color=None):
        try:
            self.cursor.execute(
                "INSERT INTO categories (name, color) VALUES (?, ?)",
                (name, color)
            )
            self.conn.commit()
            
            # Invalider le cache des catégories
            invalidate_cache("get_all_categories")
            logger.info(f"Catégorie '{name}' ajoutée avec succès")
            
            return self.cursor.lastrowid
        except Exception as e:
            logger.error(f"Erreur lors de l'ajout de la catégorie : {str(e)}")
            self.conn.rollback()
            raise e

    def add_task(self, category_id, title, description="", due_date=None, priority="moyenne", status="À faire",
                assigned_to_ids=None, estimated_time=0, is_recurring=0, recurrence_type=None, recurrence_end_date=None,
                parent_task_id=None, is_maintenance_checklist=0, checklist_template_id=None, waiting_reason=None, equipment_id=None):
        try:
            self.cursor.execute(
                """INSERT INTO tasks
                   (category_id, title, description, due_date, priority, status, waiting_reason, estimated_time,
                    is_recurring, recurrence_type, recurrence_end_date, parent_task_id, is_maintenance_checklist, equipment_id)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (category_id, title, description, due_date, priority, status, waiting_reason, estimated_time,
                 is_recurring, recurrence_type, recurrence_end_date, parent_task_id, is_maintenance_checklist, equipment_id)
            )
            task_id = self.cursor.lastrowid

            # Gérer les affectations multiples
            if assigned_to_ids:
                for person_id in assigned_to_ids:
                    self.cursor.execute(
                        "INSERT INTO task_assignments (task_id, person_id) VALUES (?, ?)",
                        (task_id, person_id)
                    )

            # Si c'est une tâche de maintenance avec un modèle de checklist, copier les éléments du modèle
            if is_maintenance_checklist and checklist_template_id:
                self.apply_checklist_template(task_id, checklist_template_id)

            self.conn.commit()
            return task_id
        except Exception as e:
            print(f"Erreur lors de l'ajout de la tâche : {str(e)}")
            self.conn.rollback()
            raise

    def add_subtask(self, task_id, title, status="À faire", assigned_to_id=None, estimated_time=0):
        try:
            # Vérifier que la tâche existe
            self.cursor.execute("SELECT id FROM tasks WHERE id = ?", (task_id,))
            if not self.cursor.fetchone():
                raise ValueError("Tâche invalide")

            # Vérifier que le titre n'est pas vide
            if not title.strip():
                raise ValueError("Le titre ne peut pas être vide")

            # Vérifier que la personne existe si assignée
            if assigned_to_id and not self.get_person_by_id(assigned_to_id):
                raise ValueError("Personne assignée invalide")

            # Insérer la sous-tâche
            self.cursor.execute(
                "INSERT INTO subtasks (task_id, title, status, assigned_to_id, estimated_time) VALUES (?, ?, ?, ?, ?)",
                (task_id, title.strip(), status, assigned_to_id, estimated_time)
            )
            self.conn.commit()
            return self.cursor.lastrowid
        except Exception as e:
            print(f"Erreur lors de l'ajout de la sous-tâche : {str(e)}")
            self.conn.rollback()
            raise

    @cached(ttl=600, persist=True)  # Cache pendant 10 minutes
    @monitor_performance("get_all_categories")
    def get_all_categories(self):
        """Récupère toutes les catégories avec mise en cache"""
        self.cursor.execute("SELECT * FROM categories ORDER BY name")
        result = self.cursor.fetchall()
        logger.debug(f"Récupération de {len(result)} catégories depuis la base de données")
        return result

    def get_tasks_by_category(self, category_id):
        self.cursor.execute(
            "SELECT * FROM tasks WHERE category_id = ? ORDER BY due_date",
            (category_id,)
        )
        return self.cursor.fetchall()

    def get_subtasks(self, task_id):
        self.cursor.execute(
            "SELECT * FROM subtasks WHERE task_id = ?",
            (task_id,)
        )
        return self.cursor.fetchall()

    def update_task_status(self, task_id, status, waiting_reason=None):
        if status == "En attente" and waiting_reason:
            self.cursor.execute(
                "UPDATE tasks SET status = ?, waiting_reason = ? WHERE id = ?",
                (status, waiting_reason, task_id)
            )
        else:
            self.cursor.execute(
                "UPDATE tasks SET status = ?, waiting_reason = NULL WHERE id = ?",
                (status, task_id)
            )
        self.conn.commit()

    def update_subtask_status(self, subtask_id, status):
        self.cursor.execute(
            "UPDATE subtasks SET status = ? WHERE id = ?",
            (status, subtask_id)
        )
        self.conn.commit()

    def delete_task(self, task_id):
        """Supprime une tâche et toutes ses données associées"""
        try:
            print(f"Tentative de suppression de la tâche {task_id}")  # Log

            # Vérifier si la tâche existe
            self.cursor.execute("SELECT id FROM tasks WHERE id = ?", (task_id,))
            if not self.cursor.fetchone():
                print(f"La tâche {task_id} n'existe pas")  # Log
                return False

            # Vérifier les relations
            self.cursor.execute("SELECT COUNT(*) FROM subtasks WHERE task_id = ?", (task_id,))
            subtasks_count = self.cursor.fetchone()[0]
            print(f"Nombre de sous-tâches trouvées : {subtasks_count}")  # Log

            self.cursor.execute("SELECT COUNT(*) FROM task_assignments WHERE task_id = ?", (task_id,))
            assignments_count = self.cursor.fetchone()[0]
            print(f"Nombre d'assignations trouvées : {assignments_count}")  # Log

            self.cursor.execute("SELECT COUNT(*) FROM checklist_results WHERE task_id = ?", (task_id,))
            checklist_results_count = self.cursor.fetchone()[0]
            print(f"Nombre de résultats de checklist trouvés : {checklist_results_count}")  # Log

            self.cursor.execute("SELECT COUNT(*) FROM checklist_items WHERE task_id = ?", (task_id,))
            checklist_items_count = self.cursor.fetchone()[0]
            print(f"Nombre d'éléments de checklist trouvés : {checklist_items_count}")  # Log

            # Supprimer d'abord les données associées
            print("Suppression des données associées...")  # Log
            self.cursor.execute("DELETE FROM subtasks WHERE task_id = ?", (task_id,))
            self.cursor.execute("DELETE FROM task_assignments WHERE task_id = ?", (task_id,))
            self.cursor.execute("DELETE FROM checklist_results WHERE task_id = ?", (task_id,))
            self.cursor.execute("DELETE FROM checklist_items WHERE task_id = ?", (task_id,))
            
            # Supprimer la tâche elle-même
            print("Suppression de la tâche...")  # Log
            self.cursor.execute("DELETE FROM tasks WHERE id = ?", (task_id,))
            
            # Vérifier si la suppression a réussi
            self.cursor.execute("SELECT id FROM tasks WHERE id = ?", (task_id,))
            if self.cursor.fetchone():
                print(f"La tâche {task_id} existe toujours après la suppression")  # Log
                self.conn.rollback()
                return False

            self.conn.commit()
            print(f"Tâche {task_id} supprimée avec succès")  # Log
            return True
        except Exception as e:
            print(f"Erreur lors de la suppression de la tâche {task_id} : {str(e)}")  # Log
            self.conn.rollback()
            return False

    def delete_category(self, category_id):
        try:
            # Vérifier si la catégorie existe
            self.cursor.execute("SELECT id FROM categories WHERE id = ?", (category_id,))
            if not self.cursor.fetchone():
                raise Exception("Catégorie non trouvée")

            # Supprimer la catégorie
            self.cursor.execute("DELETE FROM categories WHERE id = ?", (category_id,))
            self.conn.commit()
        except Exception as e:
            print(f"Erreur lors de la suppression de la catégorie : {str(e)}")
            self.conn.rollback()
            raise e

    def search_tasks(self, query):
        self.cursor.execute(
            """SELECT * FROM tasks
               WHERE title LIKE ? OR description LIKE ?
               ORDER BY due_date""",
            (f"%{query}%", f"%{query}%")
        )
        return self.cursor.fetchall()

    def advanced_search_tasks(self, filters=None):
        """
        Recherche avancée de tâches avec plusieurs filtres combinés.

        Args:
            filters (dict): Dictionnaire contenant les filtres à appliquer
                Clés possibles: 'category_id', 'priority', 'status', 'person_id',
                               'due_date_start', 'due_date_end', 'search_text',
                               'equipment_id', 'is_recurring'

        Returns:
            list: Liste des tâches correspondant aux critères
        """
        if not filters:
            return self.get_all_tasks()

        query = """
            SELECT DISTINCT t.* FROM tasks t
            LEFT JOIN task_assignments ta ON t.id = ta.task_id
            WHERE 1=1
        """
        params = []

        if 'category_id' in filters and filters['category_id']:
            query += " AND t.category_id = ?"
            params.append(filters['category_id'])

        if 'priority' in filters and filters['priority']:
            query += " AND t.priority = ?"
            params.append(filters['priority'])

        if 'status' in filters and filters['status']:
            query += " AND t.status = ?"
            params.append(filters['status'])

        if 'person_id' in filters and filters['person_id']:
            query += " AND ta.person_id = ?"
            params.append(filters['person_id'])

        if 'equipment_id' in filters and filters['equipment_id']:
            query += " AND t.equipment_id = ?"
            params.append(filters['equipment_id'])

        if 'is_recurring' in filters and filters['is_recurring'] is not None:
            query += " AND t.is_recurring = ?"
            params.append(1 if filters['is_recurring'] else 0)

        if 'due_date_start' in filters and filters['due_date_start']:
            query += " AND date(t.due_date) >= date(?)"
            params.append(filters['due_date_start'])

        if 'due_date_end' in filters and filters['due_date_end']:
            query += " AND date(t.due_date) <= date(?)"
            params.append(filters['due_date_end'])

        if 'search_text' in filters and filters['search_text']:
            query += " AND (t.title LIKE ? OR t.description LIKE ?)"
            search_text = f"%{filters['search_text']}%"
            params.append(search_text)
            params.append(search_text)

        # Tri personnalisé
        if 'sort_by' in filters and filters['sort_by']:
            sort_field = filters['sort_by']
            sort_order = filters.get('sort_order', 'ASC')

            # Vérifier que le champ de tri est valide
            valid_fields = ['title', 'due_date', 'priority', 'status', 'created_at']
            if sort_field in valid_fields:
                query += f" ORDER BY t.{sort_field} {sort_order}"
            else:
                query += " ORDER BY t.due_date"
        else:
            query += " ORDER BY t.due_date"

        self.cursor.execute(query, params)
        return self.cursor.fetchall()

    def get_tasks_by_status(self, status):
        self.cursor.execute(
            "SELECT * FROM tasks WHERE status = ? ORDER BY due_date",
            (status,)
        )
        return self.cursor.fetchall()

    def get_tasks_by_priority(self, priority):
        self.cursor.execute(
            "SELECT * FROM tasks WHERE priority = ? ORDER BY due_date",
            (priority,)
        )
        return self.cursor.fetchall()

    def get_category_by_id(self, category_id):
        self.cursor.execute('SELECT * FROM categories WHERE id = ?', (category_id,))
        return self.cursor.fetchone()

    def get_all_tasks(self):
        self.cursor.execute('SELECT * FROM tasks ORDER BY due_date')
        return self.cursor.fetchall()

    def delete_subtask(self, subtask_id):
        try:
            self.cursor.execute("DELETE FROM subtasks WHERE id = ?", (subtask_id,))
            self.conn.commit()
        except Exception as e:
            print(f"Erreur lors de la suppression de la sous-tâche : {str(e)}")
            raise

    @cached(ttl=300, persist=True)  # Cache pendant 5 minutes
    @monitor_performance("get_all_persons")
    def get_all_persons(self):
        """Récupère toutes les personnes avec mise en cache"""
        self.cursor.execute("SELECT * FROM persons ORDER BY name")
        result = self.cursor.fetchall()
        logger.debug(f"Récupération de {len(result)} personnes depuis la base de données")
        return result

    def get_person_by_id(self, person_id):
        self.cursor.execute("SELECT * FROM persons WHERE id = ?", (person_id,))
        return self.cursor.fetchone()

    def get_task(self, task_id):
        """Récupère une tâche par son ID"""
        self.cursor.execute("SELECT * FROM tasks WHERE id = ?", (task_id,))
        return self.cursor.fetchone()

    def get_task_assignments(self, task_id):
        """Récupère les assignations d'une tâche"""
        self.cursor.execute(
            """SELECT ta.*, p.name as person_name 
               FROM task_assignments ta
               JOIN persons p ON ta.person_id = p.id
               WHERE ta.task_id = ?""",
            (task_id,)
        )
        return self.cursor.fetchall()

    def add_person(self, name, role=""):
        try:
            self.cursor.execute(
                "INSERT INTO persons (name, role) VALUES (?, ?)",
                (name, role)
            )
            self.conn.commit()
            return self.cursor.lastrowid
        except sqlite3.IntegrityError:
            return None

    def update_person(self, person_id, name, role=""):
        try:
            # Vérifier que le nom n'est pas vide
            if not name.strip():
                raise ValueError("Le nom ne peut pas être vide")

            # Vérifier que la personne existe
            if not self.get_person_by_id(person_id):
                raise ValueError("Personne invalide")

            # Mettre à jour la personne
            self.cursor.execute(
                "UPDATE persons SET name = ?, role = ? WHERE id = ?",
                (name.strip(), role.strip(), person_id)
            )
            self.conn.commit()
        except Exception as e:
            print(f"Erreur lors de la mise à jour de la personne : {str(e)}")
            self.conn.rollback()
            raise

    def delete_person(self, person_id):
        try:
            # Vérifier que la personne existe
            if not self.get_person_by_id(person_id):
                raise ValueError("Personne invalide")

            # Mettre à jour les tâches et sous-tâches assignées à cette personne
            self.cursor.execute(
                "UPDATE tasks SET assigned_to_id = NULL WHERE assigned_to_id = ?",
                (person_id,)
            )
            self.cursor.execute(
                "UPDATE subtasks SET assigned_to_id = NULL WHERE assigned_to_id = ?",
                (person_id,)
            )

            # Supprimer la personne
            self.cursor.execute("DELETE FROM persons WHERE id = ?", (person_id,))
            self.conn.commit()
        except Exception as e:
            print(f"Erreur lors de la suppression de la personne : {str(e)}")
            self.conn.rollback()
            raise

    def update_category(self, category_id, name, color=None):
        try:
            self.cursor.execute(
                "UPDATE categories SET name = ?, color = ? WHERE id = ?",
                (name, color, category_id)
            )
            self.conn.commit()
        except Exception as e:
            print(f"Erreur lors de la modification de la catégorie : {str(e)}")
            self.conn.rollback()
            raise e

    def close(self):
        """Ferme la connexion à la base de données"""
        if hasattr(self, 'conn') and self.conn:
            try:
                # Vider le cache avant de fermer
                self._clear_cache()
                # Optimiser la base de données avant fermeture
                self.conn.execute("PRAGMA optimize")
                self.conn.close()
                self.conn = None  # Marquer comme fermé
            except Exception as e:
                print(f"Erreur lors de la fermeture de la base de données : {str(e)}")
                if hasattr(self, 'conn'):
                    self.conn = None

    def __del__(self):
        """Destructeur pour s'assurer que la connexion est fermée"""
        try:
            self.close()
        except:
            pass  # Ignorer les erreurs lors de la fermeture

    def __enter__(self):
        """Support pour le context manager"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Support pour le context manager"""
        if exc_type is not None:
            self.conn.rollback()
        else:
            self.conn.commit()
        return False

    def vacuum_database(self):
        """Optimise et compacte la base de données"""
        try:
            print("Optimisation de la base de données en cours...")
            self.conn.execute("VACUUM")
            self.conn.execute("PRAGMA optimize")
            print("Optimisation terminée")
        except Exception as e:
            print(f"Erreur lors de l'optimisation : {str(e)}")

    def get_database_stats(self):
        """Retourne des statistiques sur la base de données"""
        try:
            stats = {}

            # Taille de la base de données
            self.cursor.execute("PRAGMA page_count")
            page_count = self.cursor.fetchone()[0]
            self.cursor.execute("PRAGMA page_size")
            page_size = self.cursor.fetchone()[0]
            stats['database_size_mb'] = (page_count * page_size) / (1024 * 1024)

            # Nombre d'enregistrements par table
            tables = ['tasks', 'subtasks', 'categories', 'persons', 'equipment', 'spare_parts', 'attendance']
            for table in tables:
                try:
                    self.cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    stats[f'{table}_count'] = self.cursor.fetchone()[0]
                except:
                    stats[f'{table}_count'] = 0

            # Statistiques du cache
            stats['cache_entries'] = len(self._cache)
            stats['cache_hit_ratio'] = getattr(self, '_cache_hits', 0) / max(getattr(self, '_cache_requests', 1), 1)

            return stats
        except Exception as e:
            print(f"Erreur lors de la récupération des statistiques : {str(e)}")
            return {}

    # Méthodes pour les checklists de maintenance

    def add_checklist_template(self, name, description=""):
        """Ajoute un nouveau modèle de checklist de maintenance"""
        try:
            self.cursor.execute(
                "INSERT INTO maintenance_checklist_templates (name, description) VALUES (?, ?)",
                (name, description)
            )
            self.conn.commit()
            return self.cursor.lastrowid
        except Exception as e:
            print(f"Erreur lors de l'ajout du modèle de checklist : {str(e)}")
            self.conn.rollback()
            raise

    def add_checklist_item(self, template_id, title, description="", order_index=0, is_required=1):
        """Ajoute un élément à un modèle de checklist"""
        try:
            self.cursor.execute(
                """INSERT INTO checklist_items
                   (template_id, title, description, order_index, is_required)
                   VALUES (?, ?, ?, ?, ?)""",
                (template_id, title, description, order_index, is_required)
            )
            self.conn.commit()
            return self.cursor.lastrowid
        except Exception as e:
            print(f"Erreur lors de l'ajout de l'élément de checklist : {str(e)}")
            self.conn.rollback()
            raise

    def add_task_checklist_item(self, task_id, title, description="", order_index=0, is_required=1):
        """Ajoute un élément de checklist directement à une tâche"""
        try:
            self.cursor.execute(
                """INSERT INTO checklist_items
                   (task_id, title, description, order_index, is_required)
                   VALUES (?, ?, ?, ?, ?)""",
                (task_id, title, description, order_index, is_required)
            )
            self.conn.commit()
            return self.cursor.lastrowid
        except Exception as e:
            print(f"Erreur lors de l'ajout de l'élément de checklist à la tâche : {str(e)}")
            self.conn.rollback()
            raise

    def apply_checklist_template(self, task_id, template_id):
        """Applique un modèle de checklist à une tâche"""
        try:
            # Récupérer tous les éléments du modèle
            self.cursor.execute(
                "SELECT title, description, order_index, is_required FROM checklist_items WHERE template_id = ? ORDER BY order_index",
                (template_id,)
            )
            items = self.cursor.fetchall()

            # Ajouter chaque élément à la tâche
            for item in items:
                title, description, order_index, is_required = item
                self.add_task_checklist_item(task_id, title, description, order_index, is_required)

            return True
        except Exception as e:
            print(f"Erreur lors de l'application du modèle de checklist : {str(e)}")
            self.conn.rollback()
            raise

    def get_all_checklist_templates(self):
        """Récupère tous les modèles de checklist"""
        self.cursor.execute("SELECT * FROM maintenance_checklist_templates ORDER BY name")
        return self.cursor.fetchall()

    def get_checklist_template(self, template_id):
        """Récupère un modèle de checklist par son ID"""
        self.cursor.execute("SELECT * FROM maintenance_checklist_templates WHERE id = ?", (template_id,))
        return self.cursor.fetchone()

    def get_checklist_template_items(self, template_id):
        """Récupère tous les éléments d'un modèle de checklist"""
        self.cursor.execute(
            "SELECT * FROM checklist_items WHERE template_id = ? ORDER BY order_index",
            (template_id,)
        )
        return self.cursor.fetchall()

    def get_task_checklist_items(self, task_id):
        """Récupère tous les éléments de checklist d'une tâche"""
        self.cursor.execute(
            "SELECT * FROM checklist_items WHERE task_id = ? ORDER BY order_index",
            (task_id,)
        )
        return self.cursor.fetchall()

    def update_checklist_item_result(self, item_id, task_id, status, notes="", completed_by=None):
        """Met à jour le résultat d'un élément de checklist"""
        try:
            # Vérifier si un résultat existe déjà
            self.cursor.execute(
                "SELECT id FROM checklist_results WHERE item_id = ? AND task_id = ?",
                (item_id, task_id)
            )
            result = self.cursor.fetchone()

            completed_at = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            if result:
                # Mettre à jour le résultat existant
                self.cursor.execute(
                    """UPDATE checklist_results
                       SET status = ?, notes = ?, completed_by = ?, completed_at = ?
                       WHERE item_id = ? AND task_id = ?""",
                    (status, notes, completed_by, completed_at, item_id, task_id)
                )
            else:
                # Créer un nouveau résultat
                self.cursor.execute(
                    """INSERT INTO checklist_results
                       (item_id, task_id, status, notes, completed_by, completed_at)
                       VALUES (?, ?, ?, ?, ?, ?)""",
                    (item_id, task_id, status, notes, completed_by, completed_at)
                )

            self.conn.commit()
            return True
        except Exception as e:
            print(f"Erreur lors de la mise à jour du résultat de checklist : {str(e)}")
            self.conn.rollback()
            raise

    def get_checklist_results(self, task_id):
        """Récupère tous les résultats de checklist pour une tâche"""
        self.cursor.execute(
            """SELECT cr.*, ci.title, ci.description, ci.is_required, p.name as completed_by_name
               FROM checklist_results cr
               JOIN checklist_items ci ON cr.item_id = ci.id
               LEFT JOIN persons p ON cr.completed_by = p.id
               WHERE cr.task_id = ?
               ORDER BY ci.order_index""",
            (task_id,)
        )
        return self.cursor.fetchall()

    def get_checklist_summary(self, task_id):
        """Récupère un résumé des résultats de checklist pour une tâche"""
        self.cursor.execute(
            """SELECT
                (SELECT COUNT(*) FROM checklist_items WHERE task_id = ?) as total_items,
                (SELECT COUNT(*) FROM checklist_results WHERE task_id = ? AND status = 'Conforme') as conformes,
                (SELECT COUNT(*) FROM checklist_results WHERE task_id = ? AND status = 'Non conforme') as non_conformes,
                (SELECT COUNT(*) FROM checklist_results WHERE task_id = ? AND status = 'Non applicable') as non_applicables,
                (SELECT COUNT(*) FROM checklist_results WHERE task_id = ? AND status = 'Non vérifié') as non_verifies
            """,
            (task_id, task_id, task_id, task_id, task_id)
        )
        return self.cursor.fetchone()

    def delete_checklist_template(self, template_id):
        """Supprime un modèle de checklist"""
        try:
            self.cursor.execute("DELETE FROM maintenance_checklist_templates WHERE id = ?", (template_id,))
            self.conn.commit()
            return True
        except Exception as e:
            print(f"Erreur lors de la suppression du modèle de checklist : {str(e)}")
            self.conn.rollback()
            raise

    def delete_checklist_item(self, item_id):
        """Supprime un élément de checklist"""
        try:
            self.cursor.execute("DELETE FROM checklist_items WHERE id = ?", (item_id,))
            self.conn.commit()
            return True
        except Exception as e:
            print(f"Erreur lors de la suppression de l'élément de checklist : {str(e)}")
            self.conn.rollback()
            raise

    def get_maintenance_tasks(self):
        """Récupère toutes les tâches de maintenance"""
        self.cursor.execute(
            """SELECT t.* FROM tasks t
               JOIN categories c ON t.category_id = c.id
               WHERE c.name = 'Maintenance'
               ORDER BY t.due_date"""
        )
        return self.cursor.fetchall()

    def get_maintenance_checklist_tasks(self):
        """Récupère toutes les tâches avec des checklists de maintenance"""
        self.cursor.execute(
            "SELECT * FROM tasks WHERE is_maintenance_checklist = 1 ORDER BY due_date"
        )
        return self.cursor.fetchall()

    # Méthodes pour les interventions de maintenance et pièces de rechange

    def add_maintenance_intervention(self, task_id, details, technician_id=None, duration=0, intervention_date=None):
        """Ajoute une intervention de maintenance pour une tâche"""
        try:
            if intervention_date is None:
                intervention_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            self.cursor.execute(
                """INSERT INTO maintenance_interventions
                   (task_id, details, technician_id, duration, intervention_date)
                   VALUES (?, ?, ?, ?, ?)""",
                (task_id, details, technician_id, duration, intervention_date)
            )
            self.conn.commit()
            return self.cursor.lastrowid
        except Exception as e:
            print(f"Erreur lors de l'ajout de l'intervention de maintenance : {str(e)}")
            self.conn.rollback()
            raise

    def get_maintenance_interventions(self, task_id=None):
        """Récupère les interventions de maintenance pour une tâche ou toutes les interventions"""
        if task_id:
            self.cursor.execute(
                """SELECT mi.*, p.name as technician_name, t.title as task_title
                   FROM maintenance_interventions mi
                   LEFT JOIN persons p ON mi.technician_id = p.id
                   JOIN tasks t ON mi.task_id = t.id
                   WHERE mi.task_id = ?
                   ORDER BY mi.intervention_date DESC""",
                (task_id,)
            )
        else:
            self.cursor.execute(
                """SELECT mi.*, p.name as technician_name, t.title as task_title
                   FROM maintenance_interventions mi
                   LEFT JOIN persons p ON mi.technician_id = p.id
                   JOIN tasks t ON mi.task_id = t.id
                   ORDER BY mi.intervention_date DESC"""
            )
        return self.cursor.fetchall()

    def get_spare_parts(self, intervention_id):
        """Récupère les pièces de rechange utilisées lors d'une intervention"""
        self.cursor.execute(
            """SELECT * FROM spare_parts
               WHERE intervention_id = ?
               ORDER BY name""",
            (intervention_id,)
        )
        return self.cursor.fetchall()

    def get_intervention(self, intervention_id):
        """Récupère une intervention spécifique"""
        self.cursor.execute(
            """SELECT mi.*, p.name as technician_name, t.title as task_title
               FROM maintenance_interventions mi
               LEFT JOIN persons p ON mi.technician_id = p.id
               JOIN tasks t ON mi.task_id = t.id
               WHERE mi.id = ?""",
            (intervention_id,)
        )
        return self.cursor.fetchone()

    def update_intervention(self, intervention_id, details, technician_id=None, duration=None, intervention_date=None):
        """Met à jour une intervention de maintenance"""
        try:
            # Récupérer les valeurs actuelles
            self.cursor.execute("SELECT * FROM maintenance_interventions WHERE id = ?", (intervention_id,))
            current = self.cursor.fetchone()

            if not current:
                raise ValueError("Intervention non trouvée")

            # Utiliser les valeurs actuelles si non spécifiées
            if details is None:
                details = current[3]
            if technician_id is None:
                technician_id = current[4]
            if duration is None:
                duration = current[5]
            if intervention_date is None:
                intervention_date = current[2]

            self.cursor.execute(
                """UPDATE maintenance_interventions
                   SET details = ?, technician_id = ?, duration = ?, intervention_date = ?
                   WHERE id = ?""",
                (details, technician_id, duration, intervention_date, intervention_id)
            )
            self.conn.commit()
            return True
        except Exception as e:
            print(f"Erreur lors de la mise à jour de l'intervention : {str(e)}")
            self.conn.rollback()
            raise

    def delete_intervention(self, intervention_id):
        """Supprime une intervention et ses pièces de rechange associées"""
        try:
            # Les pièces de rechange seront supprimées automatiquement grâce à ON DELETE CASCADE
            self.cursor.execute("DELETE FROM maintenance_interventions WHERE id = ?", (intervention_id,))
            self.conn.commit()
            return True
        except Exception as e:
            print(f"Erreur lors de la suppression de l'intervention : {str(e)}")
            self.conn.rollback()
            raise

    def delete_spare_part(self, spare_part_id):
        """Supprime une pièce de rechange"""
        try:
            self.cursor.execute("DELETE FROM spare_parts WHERE id = ?", (spare_part_id,))
            self.conn.commit()
            return True
        except Exception as e:
            print(f"Erreur lors de la suppression de la pièce de rechange : {str(e)}")
            self.conn.rollback()
            raise

    def split_task(self, task_id, subtasks_data):
        """
        Divise une tâche en plusieurs sous-tâches.

        Args:
            task_id (int): ID de la tâche à diviser
            subtasks_data (list): Liste de dictionnaires contenant les données des sous-tâches
                Chaque dictionnaire doit contenir: 'title', 'status', 'assigned_to_id', 'estimated_time'

        Returns:
            list: Liste des IDs des sous-tâches créées
        """
        try:
            # Vérifier que la tâche existe
            task = self.get_task(task_id)
            if not task:
                raise ValueError("Tâche invalide")

            # Créer les sous-tâches
            subtask_ids = []
            for subtask_data in subtasks_data:
                subtask_id = self.add_subtask(
                    task_id=task_id,
                    title=subtask_data.get('title', ''),
                    status=subtask_data.get('status', 'À faire'),
                    assigned_to_id=subtask_data.get('assigned_to_id'),
                    estimated_time=subtask_data.get('estimated_time', 0)
                )
                subtask_ids.append(subtask_id)

            # Mettre à jour le statut de la tâche principale si nécessaire
            if task[6] == "À faire":  # Si la tâche est "À faire"
                self.update_task_status(task_id, "En cours")

            self.conn.commit()
            return subtask_ids
        except Exception as e:
            print(f"Erreur lors de la division de la tâche : {str(e)}")
            self.conn.rollback()
            raise

    # Méthodes pour la gestion des équipements

    def add_equipment(self, name, code=None, model=None, serial_number=None, manufacturer=None, purchase_date=None,
                     installation_date=None, location=None, status="En service", last_maintenance_date=None,
                     next_maintenance_date=None, notes=None):
        """Ajoute un nouvel équipement à la base de données"""
        try:
            self.cursor.execute(
                """INSERT INTO equipment
                   (name, code, model, serial_number, manufacturer, purchase_date, installation_date,
                    location, status, last_maintenance_date, next_maintenance_date, notes)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (name, code, model, serial_number, manufacturer, purchase_date, installation_date,
                 location, status, last_maintenance_date, next_maintenance_date, notes)
            )
            self.conn.commit()
            return self.cursor.lastrowid
        except sqlite3.IntegrityError:
            raise ValueError(f"Un équipement avec le code '{code}' existe déjà")
        except Exception as e:
            print(f"Erreur lors de l'ajout de l'équipement : {str(e)}")
            self.conn.rollback()
            raise

    def update_equipment(self, equipment_id, name, code=None, model=None, serial_number=None, manufacturer=None,
                        purchase_date=None, installation_date=None, location=None, status="En service",
                        last_maintenance_date=None, next_maintenance_date=None, notes=None):
        """Met à jour les informations d'un équipement"""
        try:
            self.cursor.execute(
                """UPDATE equipment SET
                   name = ?, code = ?, model = ?, serial_number = ?, manufacturer = ?, purchase_date = ?,
                   installation_date = ?, location = ?, status = ?, last_maintenance_date = ?,
                   next_maintenance_date = ?, notes = ?
                   WHERE id = ?""",
                (name, code, model, serial_number, manufacturer, purchase_date, installation_date,
                 location, status, last_maintenance_date, next_maintenance_date, notes, equipment_id)
            )
            self.conn.commit()
        except sqlite3.IntegrityError:
            raise ValueError(f"Un équipement avec le code '{code}' existe déjà")
        except Exception as e:
            print(f"Erreur lors de la mise à jour de l'équipement : {str(e)}")
            self.conn.rollback()
            raise

    def delete_equipment(self, equipment_id):
        """Supprime un équipement et tous ses documents associés"""
        try:
            # Supprimer les documents associés
            self.cursor.execute("DELETE FROM equipment_documents WHERE equipment_id = ?", (equipment_id,))

            # Supprimer l'équipement
            self.cursor.execute("DELETE FROM equipment WHERE id = ?", (equipment_id,))
            self.conn.commit()
        except Exception as e:
            print(f"Erreur lors de la suppression de l'équipement : {str(e)}")
            self.conn.rollback()
            raise

    def get_equipment(self, equipment_id):
        """Récupère les informations d'un équipement par son ID"""
        self.cursor.execute("SELECT * FROM equipment WHERE id = ?", (equipment_id,))
        return self.cursor.fetchone()

    def get_all_equipment(self):
        """Récupère tous les équipements"""
        self.cursor.execute("SELECT * FROM equipment ORDER BY name")
        return self.cursor.fetchall()

    def search_equipment(self, query):
        """Recherche des équipements par nom, modèle, numéro de série ou fabricant"""
        self.cursor.execute(
            """SELECT * FROM equipment
               WHERE name LIKE ? OR model LIKE ? OR serial_number LIKE ? OR manufacturer LIKE ?
               ORDER BY name""",
            (f"%{query}%", f"%{query}%", f"%{query}%", f"%{query}%")
        )
        return self.cursor.fetchall()

    # Méthodes pour la gestion des documents techniques

    def add_equipment_document(self, equipment_id, name, file_path, document_type="Autre", description=None):
        """Ajoute un document technique pour un équipement"""
        try:
            self.cursor.execute(
                """INSERT INTO equipment_documents
                   (equipment_id, name, file_path, document_type, description)
                   VALUES (?, ?, ?, ?, ?)""",
                (equipment_id, name, file_path, document_type, description)
            )
            self.conn.commit()
            return self.cursor.lastrowid
        except Exception as e:
            print(f"Erreur lors de l'ajout du document : {str(e)}")
            self.conn.rollback()
            raise

    def get_equipment_documents(self, equipment_id):
        """Récupère tous les documents associés à un équipement"""
        self.cursor.execute(
            "SELECT * FROM equipment_documents WHERE equipment_id = ? ORDER BY upload_date DESC",
            (equipment_id,)
        )
        return self.cursor.fetchall()

    def delete_equipment_document(self, document_id):
        """Supprime un document technique"""
        try:
            self.cursor.execute("DELETE FROM equipment_documents WHERE id = ?", (document_id,))
            self.conn.commit()
        except Exception as e:
            print(f"Erreur lors de la suppression du document : {str(e)}")
            self.conn.rollback()
            raise

    def get_equipment_document(self, document_id):
        """Récupère les informations d'un document par son ID"""
        self.cursor.execute("SELECT * FROM equipment_documents WHERE id = ?", (document_id,))
        return self.cursor.fetchone()

    # Méthodes pour lier les tâches aux équipements

    def get_tasks_by_equipment(self, equipment_id):
        """Récupère toutes les tâches associées à un équipement"""
        self.cursor.execute(
            "SELECT * FROM tasks WHERE equipment_id = ? ORDER BY due_date DESC",
            (equipment_id,)
        )
        return self.cursor.fetchall()

    def update_equipment_maintenance_dates(self, equipment_id, last_maintenance_date=None, next_maintenance_date=None):
        """Met à jour les dates de maintenance d'un équipement"""
        try:
            if last_maintenance_date and next_maintenance_date:
                self.cursor.execute(
                    "UPDATE equipment SET last_maintenance_date = ?, next_maintenance_date = ? WHERE id = ?",
                    (last_maintenance_date, next_maintenance_date, equipment_id)
                )
            elif last_maintenance_date:
                self.cursor.execute(
                    "UPDATE equipment SET last_maintenance_date = ? WHERE id = ?",
                    (last_maintenance_date, equipment_id)
                )
            elif next_maintenance_date:
                self.cursor.execute(
                    "UPDATE equipment SET next_maintenance_date = ? WHERE id = ?",
                    (next_maintenance_date, equipment_id)
                )
            self.conn.commit()
        except Exception as e:
            print(f"Erreur lors de la mise à jour des dates de maintenance : {str(e)}")
            self.conn.rollback()
            raise

    # Méthodes pour la gestion du pointage

    def set_attendance(self, person_id, date, status, overtime_hours=0, notes=None):
        """Enregistre ou met à jour le pointage d'une personne pour une date donnée"""
        try:
            # Vérifier si un enregistrement existe déjà pour cette personne et cette date
            self.cursor.execute(
                "SELECT id FROM attendance WHERE person_id = ? AND date = ?",
                (person_id, date)
            )
            existing = self.cursor.fetchone()

            if existing:
                # Mettre à jour l'enregistrement existant
                self.cursor.execute(
                    """UPDATE attendance SET
                       status = ?, overtime_hours = ?, notes = ?
                       WHERE person_id = ? AND date = ?""",
                    (status, overtime_hours, notes, person_id, date)
                )
            else:
                # Créer un nouvel enregistrement
                self.cursor.execute(
                    """INSERT INTO attendance
                       (person_id, date, status, overtime_hours, notes)
                       VALUES (?, ?, ?, ?, ?)""",
                    (person_id, date, status, overtime_hours, notes)
                )

            self.conn.commit()
        except Exception as e:
            print(f"Erreur lors de l'enregistrement du pointage : {str(e)}")
            self.conn.rollback()
            raise

    def get_attendance(self, person_id, date):
        """Récupère le pointage d'une personne pour une date donnée"""
        self.cursor.execute(
            "SELECT * FROM attendance WHERE person_id = ? AND date = ?",
            (person_id, date)
        )
        return self.cursor.fetchone()

    def get_month_attendance(self, month, year):
        """Récupère tous les pointages pour un mois et une année donnés"""
        # Format de date: YYYY-MM-DD
        date_prefix = f"{year}-{month:02d}-"

        self.cursor.execute(
            """SELECT a.*, p.name
               FROM attendance a
               JOIN persons p ON a.person_id = p.id
               WHERE a.date LIKE ?
               ORDER BY p.name, a.date""",
            (f"{date_prefix}%",)
        )
        return self.cursor.fetchall()

    def get_person_month_attendance(self, person_id, month, year):
        """Récupère tous les pointages d'une personne pour un mois et une année donnés"""
        # Format de date: YYYY-MM-DD
        date_prefix = f"{year}-{month:02d}-"

        self.cursor.execute(
            """SELECT * FROM attendance
               WHERE person_id = ? AND date LIKE ?
               ORDER BY date""",
            (person_id, f"{date_prefix}%")
        )
        return self.cursor.fetchall()

    def calculate_attendance_summary(self, person_id, month, year):
        """Calcule les statistiques de pointage pour une personne et un mois donnés"""
        attendance_records = self.get_person_month_attendance(person_id, month, year)

        present_days = sum(1 for record in attendance_records if record[3] == 'P')
        absent_days = sum(1 for record in attendance_records if record[3] == 'A')
        leave_days = sum(1 for record in attendance_records if record[3] == 'C')
        holiday_days = sum(1 for record in attendance_records if record[3] == 'F')
        overtime_hours = sum(record[4] for record in attendance_records if record[4])

        # Total des jours = présences + congés + (heures supplémentaires / 8)
        total_days = present_days + leave_days + (overtime_hours / 8)

        return {
            'present_days': present_days,
            'absent_days': absent_days,
            'leave_days': leave_days,
            'holiday_days': holiday_days,
            'overtime_hours': overtime_hours,
            'total_days': total_days
        }

    def update_database_schema(self):
        """Met à jour le schéma de la base de données si nécessaire"""
        try:
            # Vérifier si les colonnes nécessaires existent dans la table tasks
            self.cursor.execute("PRAGMA table_info(tasks)")
            columns = {column[1] for column in self.cursor.fetchall()}

            # Ajouter les colonnes manquantes à la table tasks
            if 'is_recurring' not in columns:
                self.cursor.execute("ALTER TABLE tasks ADD COLUMN is_recurring INTEGER DEFAULT 0")
                print("Colonne is_recurring ajoutée à la table tasks")

            if 'recurrence_type' not in columns:
                self.cursor.execute("ALTER TABLE tasks ADD COLUMN recurrence_type TEXT CHECK(recurrence_type IN ('daily', 'weekly', 'monthly', 'yearly') OR recurrence_type IS NULL)")
                print("Colonne recurrence_type ajoutée à la table tasks")

            if 'recurrence_end_date' not in columns:
                self.cursor.execute("ALTER TABLE tasks ADD COLUMN recurrence_end_date TEXT")
                print("Colonne recurrence_end_date ajoutée à la table tasks")

            if 'parent_task_id' not in columns:
                self.cursor.execute("ALTER TABLE tasks ADD COLUMN parent_task_id INTEGER REFERENCES tasks(id)")
                print("Colonne parent_task_id ajoutée à la table tasks")

            if 'is_maintenance_checklist' not in columns:
                self.cursor.execute("ALTER TABLE tasks ADD COLUMN is_maintenance_checklist INTEGER DEFAULT 0")
                print("Colonne is_maintenance_checklist ajoutée à la table tasks")

            if 'equipment_id' not in columns:
                self.cursor.execute("ALTER TABLE tasks ADD COLUMN equipment_id INTEGER REFERENCES equipment(id)")
                print("Colonne equipment_id ajoutée à la table tasks")

            # Vérifier et créer les tables manquantes
            self._create_missing_tables()
            
            # Mettre à jour la table spare_parts avec le nouveau schéma
            self._update_spare_parts_table()

            self.conn.commit()
            print("Mise à jour du schéma de la base de données terminée")

        except Exception as e:
            print(f"Erreur lors de la mise à jour du schéma de la base de données : {str(e)}")
            self.conn.rollback()

    def _create_missing_tables(self):
        """Crée les tables manquantes dans la base de données"""
        # Vérifier si les tables equipment et equipment_documents existent
        self.cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='equipment'")
        if not self.cursor.fetchone():
            # Créer les tables pour les équipements
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS equipment (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE,
                name TEXT NOT NULL,
                model TEXT,
                serial_number TEXT,
                manufacturer TEXT,
                purchase_date TEXT,
                installation_date TEXT,
                location TEXT,
                status TEXT CHECK(status IN ('En service', 'En maintenance', 'Hors service', 'En stock')),
                last_maintenance_date TEXT,
                next_maintenance_date TEXT,
                notes TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            print("Table equipment créée")

            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS equipment_documents (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                equipment_id INTEGER,
                name TEXT NOT NULL,
                file_path TEXT NOT NULL,
                document_type TEXT CHECK(document_type IN ('Manuel', 'Schéma', 'Procédure', 'Fiche technique', 'Autre')),
                description TEXT,
                upload_date TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (equipment_id) REFERENCES equipment (id) ON DELETE CASCADE
            )
            ''')
            print("Table equipment_documents créée")

        # Vérifier si la table de pointage existe
        self.cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='attendance'")
        if not self.cursor.fetchone():
            # Créer la table pour le pointage
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS attendance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                person_id INTEGER NOT NULL,
                date TEXT NOT NULL,
                status TEXT CHECK(status IN ('P', 'A', 'C', 'F')),
                overtime_hours REAL DEFAULT 0,
                notes TEXT,
                FOREIGN KEY (person_id) REFERENCES persons (id) ON DELETE CASCADE,
                UNIQUE(person_id, date)
            )
            ''')
            print("Table attendance créée")

            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS attendance_summary (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                person_id INTEGER NOT NULL,
                month INTEGER NOT NULL,
                year INTEGER NOT NULL,
                present_days INTEGER DEFAULT 0,
                absent_days INTEGER DEFAULT 0,
                leave_days INTEGER DEFAULT 0,
                holiday_days INTEGER DEFAULT 0,
                overtime_hours REAL DEFAULT 0,
                total_days REAL DEFAULT 0,
                FOREIGN KEY (person_id) REFERENCES persons (id) ON DELETE CASCADE,
                UNIQUE(person_id, month, year)
            )
            ''')
            print("Table attendance_summary créée")

        # Vérifier si les tables de détails de maintenance existent
        self.cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='maintenance_interventions'")
        if not self.cursor.fetchone():
            # Créer les tables pour les détails d'intervention
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS maintenance_interventions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_id INTEGER NOT NULL,
                intervention_date TEXT DEFAULT CURRENT_TIMESTAMP,
                details TEXT,
                technician_id INTEGER,
                duration INTEGER, -- Durée en minutes
                FOREIGN KEY (task_id) REFERENCES tasks (id) ON DELETE CASCADE,
                FOREIGN KEY (technician_id) REFERENCES persons (id)
            )
            ''')
            print("Table maintenance_interventions créée")

        # Créer la table des mouvements de stock
        self.cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='stock_movements'")
        if not self.cursor.fetchone():
            self.cursor.execute('''
            CREATE TABLE stock_movements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                part_id INTEGER NOT NULL,
                movement_type TEXT NOT NULL CHECK(movement_type IN ('entree', 'sortie')),
                quantity INTEGER NOT NULL,
                previous_quantity INTEGER NOT NULL,
                new_quantity INTEGER NOT NULL,
                reason TEXT,
                user_id INTEGER,
                movement_date TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (part_id) REFERENCES spare_parts (id) ON DELETE CASCADE,
                FOREIGN KEY (user_id) REFERENCES persons (id)
            )
            ''')
            print("Table stock_movements créée")

        # Créer la table des notifications
        self.cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='notifications'")
        if not self.cursor.fetchone():
            self.cursor.execute('''
            CREATE TABLE notifications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                message TEXT,
                type TEXT DEFAULT 'info' CHECK(type IN ('info', 'warning', 'error', 'success')),
                user_id INTEGER,
                related_table TEXT,
                related_id INTEGER,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                is_read INTEGER DEFAULT 0,
                read_at TEXT,
                FOREIGN KEY (user_id) REFERENCES persons (id)
            )
            ''')
            print("Table notifications créée")

        # Créer la table des logs d'audit
        self.cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='audit_logs'")
        if not self.cursor.fetchone():
            self.cursor.execute('''
            CREATE TABLE audit_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                action TEXT NOT NULL,
                table_name TEXT NOT NULL,
                record_id INTEGER,
                user_id INTEGER,
                details TEXT,
                timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES persons (id)
            )
            ''')
            print("Table audit_logs créée")

        # Créer la table des paramètres de l'application
        self.cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='app_settings'")
        if not self.cursor.fetchone():
            self.cursor.execute('''
            CREATE TABLE app_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_key TEXT UNIQUE NOT NULL,
                setting_value TEXT,
                setting_type TEXT DEFAULT 'string',
                description TEXT,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            print("Table app_settings créée")

            # Insérer les paramètres par défaut
            default_settings = [
                ('app_version', '2.1', 'string', 'Version de l\'application'),
                ('auto_backup', 'true', 'boolean', 'Sauvegarde automatique activée'),
                ('backup_frequency', '7', 'integer', 'Fréquence de sauvegarde en jours'),
                ('notification_enabled', 'true', 'boolean', 'Notifications activées'),
                ('theme', 'default', 'string', 'Thème de l\'interface'),
                ('language', 'fr', 'string', 'Langue de l\'interface'),
                ('max_recent_items', '10', 'integer', 'Nombre maximum d\'éléments récents'),
                ('session_timeout', '480', 'integer', 'Timeout de session en minutes'),
                ('debug_mode', 'false', 'boolean', 'Mode debug activé')
            ]

            for key, value, type_val, desc in default_settings:
                self.cursor.execute(
                    "INSERT OR IGNORE INTO app_settings (setting_key, setting_value, setting_type, description) VALUES (?, ?, ?, ?)",
                    (key, value, type_val, desc)
                )

        # Créer la table des favoris
        self.cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='favorites'")
        if not self.cursor.fetchone():
            self.cursor.execute('''
            CREATE TABLE favorites (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                item_type TEXT NOT NULL,
                item_id INTEGER NOT NULL,
                item_name TEXT NOT NULL,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES persons (id),
                UNIQUE(user_id, item_type, item_id)
            )
            ''')
            print("Table favorites créée")

        # Vérifier si les tables de maintenance existent (pour compatibilité)
        self.cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='maintenance_checklist_templates'")
        if not self.cursor.fetchone():
            # Créer les tables de maintenance
            self.cursor.execute('''
            CREATE TABLE maintenance_checklist_templates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            print("Table maintenance_checklist_templates créée")

            self.cursor.execute('''
            CREATE TABLE checklist_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                template_id INTEGER,
                task_id INTEGER,
                title TEXT NOT NULL,
                description TEXT,
                order_index INTEGER,
                is_required INTEGER DEFAULT 1,
                FOREIGN KEY (template_id) REFERENCES maintenance_checklist_templates (id) ON DELETE CASCADE,
                FOREIGN KEY (task_id) REFERENCES tasks (id) ON DELETE CASCADE
            )
            ''')
            print("Table checklist_items créée")

            self.cursor.execute('''
            CREATE TABLE checklist_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                item_id INTEGER,
                task_id INTEGER,
                status TEXT CHECK(status IN ('Non vérifié', 'Conforme', 'Non conforme', 'Non applicable')),
                notes TEXT,
                completed_by INTEGER,
                completed_at TEXT,
                FOREIGN KEY (item_id) REFERENCES checklist_items (id) ON DELETE CASCADE,
                FOREIGN KEY (task_id) REFERENCES tasks (id) ON DELETE CASCADE,
                FOREIGN KEY (completed_by) REFERENCES persons (id)
            )
            ''')
            print("Table checklist_results créée")

    def _update_spare_parts_table(self):
        """Met à jour la table spare_parts avec le nouveau schéma"""
        # Vérifier si la table spare_parts existe déjà
        self.cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='spare_parts'")
        table_exists = self.cursor.fetchone()

        # Sauvegarder les données existantes si la table existe
        spare_parts_data = []
        if table_exists:
            try:
                self.cursor.execute("SELECT * FROM spare_parts")
                spare_parts_data = self.cursor.fetchall()
                print(f"Sauvegarde de {len(spare_parts_data)} pièces de rechange")
            except sqlite3.OperationalError as e:
                print(f"Erreur lors de la récupération des pièces existantes : {str(e)}")

            # Supprimer la table existante
            self.cursor.execute("DROP TABLE IF EXISTS spare_parts")
            self.conn.commit()
            print("Table spare_parts supprimée pour recréation")

        # Créer la table des pièces de rechange avec le schéma complet
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS spare_parts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code TEXT UNIQUE,
            name TEXT NOT NULL,
            quantity INTEGER DEFAULT 0,
            location TEXT,
            part_type TEXT,
            min_threshold INTEGER DEFAULT 0,
            unit_price REAL DEFAULT 0,
            last_updated TEXT DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        self.conn.commit()
        print("Table spare_parts créée avec succès")

        # Restaurer les données si nécessaire
        if spare_parts_data:
            try:
                # Déterminer les colonnes disponibles dans les données sauvegardées
                # et les insérer dans la nouvelle table
                for part in spare_parts_data:
                    try:
                        # Essayer d'extraire les valeurs avec des valeurs par défaut si elles n'existent pas
                        part_id = part[0] if len(part) > 0 else None
                        code = part[1] if len(part) > 1 else ""
                        name = part[2] if len(part) > 2 else ""
                        quantity = part[3] if len(part) > 3 else 0
                        location = part[4] if len(part) > 4 else ""
                        part_type = part[5] if len(part) > 5 else ""
                        min_threshold = part[6] if len(part) > 6 else 0
                        unit_price = part[7] if len(part) > 7 else 0
                        last_updated = part[8] if len(part) > 8 else None

                        # Insérer les données dans la nouvelle table
                        self.cursor.execute(
                            """INSERT INTO spare_parts
                               (id, code, name, quantity, location, part_type, min_threshold, unit_price, last_updated)
                               VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                            (part_id, code, name, quantity, location, part_type, min_threshold, unit_price, last_updated)
                        )
                    except Exception as e:
                        print(f"Erreur lors de la restauration d'une pièce : {str(e)}")
                        continue

                self.conn.commit()
                print(f"Restauration de {len(spare_parts_data)} pièces de rechange terminée")
            except Exception as e:
                print(f"Erreur lors de la restauration des données : {str(e)}")

    def get_recurring_tasks(self):
        """Récupère toutes les tâches récurrentes"""
        self.cursor.execute(
            "SELECT * FROM tasks WHERE is_recurring = 1 ORDER BY due_date"
        )
        return self.cursor.fetchall()

    def get_child_tasks(self, parent_task_id):
        """Récupère toutes les tâches enfants d'une tâche récurrente"""
        self.cursor.execute(
            "SELECT * FROM tasks WHERE parent_task_id = ? ORDER BY due_date",
            (parent_task_id,)
        )
        return self.cursor.fetchall()

    def create_next_recurring_task(self, task_id):
        """Crée la prochaine occurrence d'une tâche récurrente"""
        # Récupérer la tâche récurrente
        task = self.get_task(task_id)
        if not task or not task[9]:  # is_recurring
            return None

        # Récupérer les assignations
        assignees = self.get_task_assignees(task_id)
        assigned_to_ids = [assignee[0] for assignee in assignees] if assignees else None

        # Calculer la prochaine date d'échéance
        current_due_date = datetime.strptime(task[4], '%Y-%m-%d %H:%M')  # due_date
        recurrence_type = task[10]  # recurrence_type

        if recurrence_type == 'daily':
            next_due_date = current_due_date + timedelta(days=1)
        elif recurrence_type == 'weekly':
            next_due_date = current_due_date + timedelta(weeks=1)
        elif recurrence_type == 'monthly':
            # Ajouter un mois (approximativement)
            if current_due_date.month == 12:
                next_due_date = current_due_date.replace(year=current_due_date.year + 1, month=1)
            else:
                next_due_date = current_due_date.replace(month=current_due_date.month + 1)
        elif recurrence_type == 'yearly':
            next_due_date = current_due_date.replace(year=current_due_date.year + 1)
        else:
            return None

        # Vérifier si la date de fin de récurrence est dépassée
        if task[11] and datetime.strptime(task[11], '%Y-%m-%d') < next_due_date.date():
            return None

        # Créer la nouvelle tâche
        return self.add_task(
            category_id=task[1],
            title=task[2],
            description=task[3],
            due_date=next_due_date.strftime('%Y-%m-%d %H:%M'),
            priority=task[5],
            status="À faire",  # Toujours commencer par "À faire"
            assigned_to_ids=assigned_to_ids,
            estimated_time=task[7],
            is_recurring=0,  # Les tâches enfants ne sont pas récurrentes
            parent_task_id=task_id
        )

    def check_and_create_recurring_tasks(self):
        """Vérifie et crée les prochaines occurrences des tâches récurrentes si nécessaire"""
        recurring_tasks = self.get_recurring_tasks()
        created_tasks = []

        for task in recurring_tasks:
            # Vérifier si la tâche est terminée
            if task[6] == "Terminée":  # status
                # Créer la prochaine occurrence
                new_task_id = self.create_next_recurring_task(task[0])
                if new_task_id:
                    created_tasks.append(new_task_id)

        return created_tasks

    def get_task(self, task_id):
        """Récupère une tâche par son ID"""
        try:
            print(f"Recherche de la tâche {task_id}")  # Log
            
            # Vérifier la structure de la table
            self.cursor.execute("PRAGMA table_info(tasks)")
            columns = self.cursor.fetchall()
            print(f"Structure de la table tasks : {columns}")  # Log
            
            # Vérifier toutes les tâches existantes
            self.cursor.execute("SELECT id, title FROM tasks")
            all_tasks = self.cursor.fetchall()
            print(f"Tâches existantes : {all_tasks}")  # Log
            
            # Rechercher la tâche spécifique
            self.cursor.execute("SELECT * FROM tasks WHERE id = ?", (task_id,))
            task = self.cursor.fetchone()
            
            if task:
                print(f"Tâche {task_id} trouvée : {task}")  # Log
            else:
                print(f"Tâche {task_id} non trouvée")  # Log
                
            return task
        except Exception as e:
            print(f"Erreur lors de la récupération de la tâche {task_id} : {str(e)}")  # Log
            return None

    def update_task(self, task_id, category_id, title, description="", due_date=None, priority="moyenne", status="À faire",
                  assigned_to_ids=None, estimated_time=0, is_recurring=0, recurrence_type=None, recurrence_end_date=None,
                  is_maintenance_checklist=0, waiting_reason=None, equipment_id=None):
        try:
            print(f"Début de la mise à jour de la tâche {task_id}")  # Log
            print(f"Données reçues : category_id={category_id}, title={title}, status={status}")  # Log

            # Vérifier que la tâche existe
            self.cursor.execute("SELECT id FROM tasks WHERE id = ?", (task_id,))
            if not self.cursor.fetchone():
                print(f"Tâche {task_id} non trouvée dans la base de données")  # Log
                raise ValueError("Tâche non trouvée")

            # Vérifier que le titre n'est pas vide
            if not title.strip():
                print("Le titre est vide")  # Log
                raise ValueError("Le titre ne peut pas être vide")

            # Si le statut est "En attente", on conserve la raison d'attente
            waiting_reason_value = waiting_reason if status == "En attente" else None

            # Mettre à jour la tâche
            print("Exécution de la requête UPDATE")  # Log
            self.cursor.execute(
                """UPDATE tasks SET
                   category_id = ?, title = ?, description = ?, due_date = ?,
                   priority = ?, status = ?, waiting_reason = ?, estimated_time = ?,
                   is_recurring = ?, recurrence_type = ?, recurrence_end_date = ?,
                   is_maintenance_checklist = ?, equipment_id = ?
                   WHERE id = ?""",
                (category_id, title.strip(), description.strip(), due_date, priority, status, 
                 waiting_reason_value, estimated_time, is_recurring, recurrence_type, 
                 recurrence_end_date, is_maintenance_checklist, equipment_id, task_id)
            )

            # Mettre à jour les affectations
            print("Suppression des anciennes affectations")  # Log
            self.cursor.execute("DELETE FROM task_assignments WHERE task_id = ?", (task_id,))
            if assigned_to_ids:
                print(f"Ajout des nouvelles affectations : {assigned_to_ids}")  # Log
                for person_id in assigned_to_ids:
                    # Vérifier que la personne existe
                    self.cursor.execute("SELECT id FROM persons WHERE id = ?", (person_id,))
                    if self.cursor.fetchone():
                        self.cursor.execute(
                            "INSERT INTO task_assignments (task_id, person_id) VALUES (?, ?)",
                            (task_id, person_id)
                        )

            self.conn.commit()
            print("Mise à jour terminée avec succès")  # Log
            return True
        except Exception as e:
            print(f"Erreur lors de la mise à jour de la tâche : {str(e)}")  # Log
            self.conn.rollback()
            raise

    def get_task_assignees(self, task_id):
        self.cursor.execute("""
            SELECT p.* FROM persons p
            INNER JOIN task_assignments ta ON p.id = ta.person_id
            WHERE ta.task_id = ?
            ORDER BY p.name
        """, (task_id,))
        return self.cursor.fetchall()

    def migrate_task_assignments(self):
        try:
            # Récupérer toutes les tâches avec leurs assignations actuelles
            self.cursor.execute("SELECT id, assigned_to_id FROM tasks WHERE assigned_to_id IS NOT NULL")
            tasks = self.cursor.fetchall()

            # Pour chaque tâche, créer une nouvelle entrée dans task_assignments
            for task_id, person_id in tasks:
                self.cursor.execute(
                    "INSERT OR IGNORE INTO task_assignments (task_id, person_id) VALUES (?, ?)",
                    (task_id, person_id)
                )

            # Supprimer la colonne assigned_to_id de la table tasks
            self.cursor.execute("CREATE TABLE tasks_temp AS SELECT id, category_id, title, description, due_date, priority, status, waiting_reason, estimated_time, created_at, is_recurring, recurrence_type, recurrence_end_date, parent_task_id, is_maintenance_checklist, equipment_id FROM tasks")
            self.cursor.execute("DROP TABLE tasks")
            self.cursor.execute("ALTER TABLE tasks_temp RENAME TO tasks")

            self.conn.commit()

        except Exception as e:
            print(f"Erreur lors de la migration : {str(e)}")
            self.conn.rollback()
            raise



    # Méthodes pour la gestion des pièces de rechange
    def add_spare_part(self, code, name, quantity=0, location="", part_type="", min_threshold=0, unit_price=0):
        """Ajoute une nouvelle pièce de rechange"""
        try:
            # Vérifier que le code n'est pas vide
            if not code.strip():
                raise ValueError("Le code ne peut pas être vide")

            # Vérifier que le nom n'est pas vide
            if not name.strip():
                raise ValueError("Le nom ne peut pas être vide")

            # Insérer la pièce
            self.cursor.execute(
                """INSERT INTO spare_parts
                   (code, name, quantity, location, part_type, min_threshold, unit_price, last_updated)
                   VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now', 'localtime'))""",
                (code.strip(), name.strip(), quantity, location.strip(), part_type.strip(), min_threshold, unit_price)
            )
            self.conn.commit()
            return self.cursor.lastrowid
        except sqlite3.IntegrityError:
            raise ValueError(f"Une pièce avec le code '{code}' existe déjà")
        except Exception as e:
            print(f"Erreur lors de l'ajout de la pièce : {str(e)}")
            self.conn.rollback()
            raise

    def update_spare_part(self, part_id, code, name, quantity=0, location="", part_type="", min_threshold=0, unit_price=0):
        """Met à jour une pièce de rechange existante"""
        try:
            # Vérifier que la pièce existe
            self.cursor.execute("SELECT id FROM spare_parts WHERE id = ?", (part_id,))
            if not self.cursor.fetchone():
                raise ValueError("Pièce non trouvée")

            # Vérifier que le code n'est pas vide
            if not code.strip():
                raise ValueError("Le code ne peut pas être vide")

            # Vérifier que le nom n'est pas vide
            if not name.strip():
                raise ValueError("Le nom ne peut pas être vide")

            # Mettre à jour la pièce
            self.cursor.execute(
                """UPDATE spare_parts SET
                   code = ?, name = ?, quantity = ?, location = ?,
                   part_type = ?, min_threshold = ?, unit_price = ?,
                   last_updated = datetime('now', 'localtime')
                   WHERE id = ?""",
                (code.strip(), name.strip(), quantity, location.strip(),
                 part_type.strip(), min_threshold, unit_price, part_id)
            )
            self.conn.commit()
        except sqlite3.IntegrityError:
            raise ValueError(f"Une autre pièce avec le code '{code}' existe déjà")
        except Exception as e:
            print(f"Erreur lors de la mise à jour de la pièce : {str(e)}")
            self.conn.rollback()
            raise

    def delete_spare_part(self, part_id):
        """Supprime une pièce de rechange"""
        try:
            # Vérifier que la pièce existe
            self.cursor.execute("SELECT id FROM spare_parts WHERE id = ?", (part_id,))
            if not self.cursor.fetchone():
                raise ValueError("Pièce non trouvée")

            # Supprimer la pièce
            self.cursor.execute("DELETE FROM spare_parts WHERE id = ?", (part_id,))
            self.conn.commit()
        except Exception as e:
            print(f"Erreur lors de la suppression de la pièce : {str(e)}")
            self.conn.rollback()
            raise

    def get_spare_part(self, part_id):
        """Récupère une pièce de rechange par son ID"""
        self.cursor.execute("SELECT * FROM spare_parts WHERE id = ?", (part_id,))
        return self.cursor.fetchone()

    def get_spare_part_by_code(self, code):
        """Récupère une pièce de rechange par son code"""
        self.cursor.execute("SELECT * FROM spare_parts WHERE code = ?", (code,))
        return self.cursor.fetchone()

    def get_all_spare_parts(self, order_by="name"):
        """Récupère toutes les pièces de rechange, triées selon le critère spécifié"""
        try:
            valid_order_fields = ["id", "code", "name", "quantity", "location", "part_type", "min_threshold", "unit_price", "last_updated"]

            if order_by not in valid_order_fields:
                order_by = "name"  # Valeur par défaut

            self.cursor.execute(f"SELECT * FROM spare_parts ORDER BY {order_by}")
            return self.cursor.fetchall()
        except sqlite3.OperationalError as e:
            print(f"Erreur lors de la récupération des pièces : {str(e)}")
            return []

    def search_spare_parts(self, query):
        """Recherche des pièces de rechange par code ou nom"""
        try:
            self.cursor.execute(
                """SELECT * FROM spare_parts
                   WHERE code LIKE ? OR name LIKE ?
                   ORDER BY name""",
                (f"%{query}%", f"%{query}%")
            )
            return self.cursor.fetchall()
        except sqlite3.OperationalError as e:
            print(f"Erreur lors de la recherche de pièces : {str(e)}")
            return []

    def get_spare_parts_by_type(self, part_type):
        """Récupère les pièces de rechange par type"""
        try:
            self.cursor.execute(
                "SELECT * FROM spare_parts WHERE part_type = ? ORDER BY name",
                (part_type,)
            )
            return self.cursor.fetchall()
        except sqlite3.OperationalError as e:
            print(f"Erreur lors de la récupération des pièces par type : {str(e)}")
            return []

    def get_spare_parts_below_threshold(self):
        """Récupère les pièces de rechange dont la quantité est inférieure au seuil d'alerte"""
        try:
            self.cursor.execute(
                "SELECT * FROM spare_parts WHERE quantity < min_threshold ORDER BY quantity"
            )
            return self.cursor.fetchall()
        except sqlite3.OperationalError as e:
            print(f"Erreur lors de la récupération des pièces sous le seuil : {str(e)}")
            return []

    def get_spare_part_types(self):
        """Récupère tous les types de pièces de rechange distincts"""
        try:
            self.cursor.execute("SELECT DISTINCT part_type FROM spare_parts WHERE part_type != '' ORDER BY part_type")
            return [row[0] for row in self.cursor.fetchall()]
        except sqlite3.OperationalError as e:
            print(f"Erreur lors de la récupération des types de pièces : {str(e)}")
            return []

    def update_spare_part_quantity(self, part_id, new_quantity):
        """Met à jour la quantité d'une pièce de rechange"""
        try:
            # Vérifier que la pièce existe
            self.cursor.execute("SELECT id FROM spare_parts WHERE id = ?", (part_id,))
            if not self.cursor.fetchone():
                raise ValueError("Pièce non trouvée")

            # Mettre à jour la quantité
            self.cursor.execute(
                """UPDATE spare_parts SET
                   quantity = ?, last_updated = datetime('now', 'localtime')
                   WHERE id = ?""",
                (new_quantity, part_id)
            )
            self.conn.commit()
        except Exception as e:
            print(f"Erreur lors de la mise à jour de la quantité : {str(e)}")
            self.conn.rollback()
            raise

    def get_total_stock_value(self):
        """Calcule la valeur totale du stock de pièces de rechange"""
        self.cursor.execute("SELECT SUM(quantity * unit_price) FROM spare_parts")
        result = self.cursor.fetchone()[0]
        return result if result else 0

    # Méthodes pour la gestion des mouvements de stock
    def add_stock_movement(self, part_id, movement_type, quantity, reason="", user_id=None):
        """Ajoute un mouvement de stock (entrée/sortie)"""
        try:
            # Vérifier que la pièce existe
            part = self.get_spare_part(part_id)
            if not part:
                raise ValueError("Pièce non trouvée")

            # Calculer la nouvelle quantité
            current_quantity = part[3]  # quantity est à l'index 3
            if movement_type == "sortie" and quantity > current_quantity:
                raise ValueError("Quantité insuffisante en stock")

            new_quantity = current_quantity + quantity if movement_type == "entree" else current_quantity - quantity

            # Enregistrer le mouvement
            self.cursor.execute(
                """INSERT INTO stock_movements
                   (part_id, movement_type, quantity, previous_quantity, new_quantity, reason, user_id, movement_date)
                   VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now', 'localtime'))""",
                (part_id, movement_type, quantity, current_quantity, new_quantity, reason, user_id)
            )

            # Mettre à jour la quantité de la pièce
            self.update_spare_part_quantity(part_id, new_quantity)

            self.conn.commit()
            return self.cursor.lastrowid

        except Exception as e:
            print(f"Erreur lors de l'ajout du mouvement de stock : {str(e)}")
            self.conn.rollback()
            raise

    def get_stock_movements(self, part_id=None, limit=100):
        """Récupère l'historique des mouvements de stock"""
        try:
            if part_id:
                self.cursor.execute(
                    """SELECT sm.*, sp.code, sp.name
                       FROM stock_movements sm
                       JOIN spare_parts sp ON sm.part_id = sp.id
                       WHERE sm.part_id = ?
                       ORDER BY sm.movement_date DESC
                       LIMIT ?""",
                    (part_id, limit)
                )
            else:
                self.cursor.execute(
                    """SELECT sm.*, sp.code, sp.name
                       FROM stock_movements sm
                       JOIN spare_parts sp ON sm.part_id = sp.id
                       ORDER BY sm.movement_date DESC
                       LIMIT ?""",
                    (limit,)
                )
            return self.cursor.fetchall()
        except Exception as e:
            print(f"Erreur lors de la récupération des mouvements : {str(e)}")
            return []

    # Méthodes pour les rapports et statistiques
    def get_task_statistics(self, start_date=None, end_date=None):
        """Récupère les statistiques des tâches"""
        try:
            stats = {}

            # Clause WHERE pour les dates
            date_clause = ""
            params = []
            if start_date and end_date:
                date_clause = "WHERE created_at BETWEEN ? AND ?"
                params = [start_date, end_date]

            # Total des tâches
            self.cursor.execute(f"SELECT COUNT(*) FROM tasks {date_clause}", params)
            stats['total_tasks'] = self.cursor.fetchone()[0]

            # Tâches par statut
            self.cursor.execute(f"""
                SELECT status, COUNT(*)
                FROM tasks {date_clause}
                GROUP BY status
            """, params)
            stats['by_status'] = dict(self.cursor.fetchall())

            # Tâches par priorité
            self.cursor.execute(f"""
                SELECT priority, COUNT(*)
                FROM tasks {date_clause}
                GROUP BY priority
            """, params)
            stats['by_priority'] = dict(self.cursor.fetchall())

            # Tâches par catégorie
            self.cursor.execute(f"""
                SELECT c.name, COUNT(t.id)
                FROM categories c
                LEFT JOIN tasks t ON c.id = t.category_id {date_clause.replace('WHERE', 'AND') if date_clause else ''}
                GROUP BY c.id, c.name
            """, params)
            stats['by_category'] = dict(self.cursor.fetchall())

            # Tâches en retard
            self.cursor.execute(f"""
                SELECT COUNT(*) FROM tasks
                WHERE due_date < date('now') AND status != 'Terminée' {date_clause.replace('WHERE', 'AND') if date_clause else ''}
            """, params)
            stats['overdue_tasks'] = self.cursor.fetchone()[0]

            return stats

        except Exception as e:
            print(f"Erreur lors du calcul des statistiques : {str(e)}")
            return {}

    def get_equipment_statistics(self):
        """Récupère les statistiques des équipements"""
        try:
            stats = {}

            # Total des équipements
            self.cursor.execute("SELECT COUNT(*) FROM equipment")
            stats['total_equipment'] = self.cursor.fetchone()[0]

            # Équipements par statut
            self.cursor.execute("SELECT status, COUNT(*) FROM equipment GROUP BY status")
            stats['by_status'] = dict(self.cursor.fetchall())

            # Équipements par localisation
            self.cursor.execute("SELECT location, COUNT(*) FROM equipment GROUP BY location")
            stats['by_location'] = dict(self.cursor.fetchall())

            # Équipements nécessitant une maintenance
            self.cursor.execute("""
                SELECT COUNT(*) FROM equipment
                WHERE next_maintenance_date <= date('now', '+7 days')
            """)
            stats['maintenance_due'] = self.cursor.fetchone()[0]

            return stats

        except Exception as e:
            print(f"Erreur lors du calcul des statistiques équipements : {str(e)}")
            return {}

    def get_attendance_statistics(self, month=None, year=None):
        """Récupère les statistiques de pointage"""
        try:
            from datetime import datetime

            if not month or not year:
                now = datetime.now()
                month = now.month
                year = now.year

            stats = {}

            # Taux de présence par personne
            self.cursor.execute("""
                SELECT p.name,
                       COUNT(CASE WHEN a.status = 'P' THEN 1 END) as present_days,
                       COUNT(*) as total_days,
                       ROUND(COUNT(CASE WHEN a.status = 'P' THEN 1 END) * 100.0 / COUNT(*), 2) as attendance_rate
                FROM persons p
                LEFT JOIN attendance a ON p.id = a.person_id
                WHERE strftime('%m', a.date) = ? AND strftime('%Y', a.date) = ?
                GROUP BY p.id, p.name
                ORDER BY attendance_rate DESC
            """, (f"{month:02d}", str(year)))
            stats['by_person'] = self.cursor.fetchall()

            # Statistiques globales
            self.cursor.execute("""
                SELECT
                    COUNT(CASE WHEN status = 'P' THEN 1 END) as present,
                    COUNT(CASE WHEN status = 'A' THEN 1 END) as absent,
                    COUNT(CASE WHEN status = 'C' THEN 1 END) as leave,
                    COUNT(CASE WHEN status = 'F' THEN 1 END) as holiday,
                    COUNT(*) as total
                FROM attendance
                WHERE strftime('%m', date) = ? AND strftime('%Y', date) = ?
            """, (f"{month:02d}", str(year)))
            result = self.cursor.fetchone()
            stats['global'] = {
                'present': result[0],
                'absent': result[1],
                'leave': result[2],
                'holiday': result[3],
                'total': result[4]
            }

            return stats

        except Exception as e:
            print(f"Erreur lors du calcul des statistiques de pointage : {str(e)}")
            return {}

    # Méthodes pour la sauvegarde et restauration
    def backup_database(self, backup_path):
        """Crée une sauvegarde de la base de données"""
        try:
            import shutil
            shutil.copy2(self.conn.execute("PRAGMA database_list").fetchone()[2], backup_path)
            return True
        except Exception as e:
            print(f"Erreur lors de la sauvegarde : {str(e)}")
            return False

    def restore_database(self, backup_path):
        """Restaure la base de données depuis une sauvegarde"""
        try:
            import shutil
            import os

            # Fermer la connexion actuelle
            self.close()

            # Remplacer le fichier de base de données
            db_path = self.conn.execute("PRAGMA database_list").fetchone()[2]
            shutil.copy2(backup_path, db_path)

            # Rouvrir la connexion
            self.__init__(db_path)
            return True

        except Exception as e:
            print(f"Erreur lors de la restauration : {str(e)}")
            return False

    # Méthodes pour l'import/export de données
    def export_to_csv(self, table_name, file_path):
        """Exporte une table vers un fichier CSV"""
        try:
            import csv

            # Récupérer les données
            self.cursor.execute(f"SELECT * FROM {table_name}")
            data = self.cursor.fetchall()

            # Récupérer les noms de colonnes
            self.cursor.execute(f"PRAGMA table_info({table_name})")
            columns = [col[1] for col in self.cursor.fetchall()]

            # Écrire le fichier CSV
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(columns)  # En-têtes
                writer.writerows(data)    # Données

            return True

        except Exception as e:
            print(f"Erreur lors de l'export CSV : {str(e)}")
            return False

    def import_from_csv(self, table_name, file_path, mapping=None):
        """Importe des données depuis un fichier CSV"""
        try:
            import csv

            with open(file_path, 'r', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)

                for row in reader:
                    # Appliquer le mapping si fourni
                    if mapping:
                        mapped_row = {}
                        for csv_col, db_col in mapping.items():
                            if csv_col in row:
                                mapped_row[db_col] = row[csv_col]
                        row = mapped_row

                    # Construire la requête d'insertion
                    columns = list(row.keys())
                    placeholders = ['?' for _ in columns]
                    values = list(row.values())

                    query = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"
                    self.cursor.execute(query, values)

            self.conn.commit()
            return True

        except Exception as e:
            print(f"Erreur lors de l'import CSV : {str(e)}")
            self.conn.rollback()
            return False

    # Méthodes pour les notifications et alertes
    def add_notification(self, title, message, notification_type="info", user_id=None, related_table=None, related_id=None):
        """Ajoute une notification"""
        try:
            self.cursor.execute(
                """INSERT INTO notifications
                   (title, message, type, user_id, related_table, related_id, created_at, is_read)
                   VALUES (?, ?, ?, ?, ?, ?, datetime('now', 'localtime'), 0)""",
                (title, message, notification_type, user_id, related_table, related_id)
            )
            self.conn.commit()
            return self.cursor.lastrowid
        except Exception as e:
            print(f"Erreur lors de l'ajout de la notification : {str(e)}")
            return None

    def get_notifications(self, user_id=None, unread_only=False, limit=50):
        """Récupère les notifications"""
        try:
            query = "SELECT * FROM notifications WHERE 1=1"
            params = []

            if user_id:
                query += " AND (user_id = ? OR user_id IS NULL)"
                params.append(user_id)

            if unread_only:
                query += " AND is_read = 0"

            query += " ORDER BY created_at DESC LIMIT ?"
            params.append(limit)

            self.cursor.execute(query, params)
            return self.cursor.fetchall()
        except Exception as e:
            print(f"Erreur lors de la récupération des notifications : {str(e)}")
            return []

    def mark_notification_read(self, notification_id):
        """Marque une notification comme lue"""
        try:
            self.cursor.execute(
                "UPDATE notifications SET is_read = 1, read_at = datetime('now', 'localtime') WHERE id = ?",
                (notification_id,)
            )
            self.conn.commit()
        except Exception as e:
            print(f"Erreur lors du marquage de la notification : {str(e)}")

    def get_unread_notification_count(self, user_id=None):
        """Récupère le nombre de notifications non lues"""
        try:
            if user_id:
                self.cursor.execute(
                    "SELECT COUNT(*) FROM notifications WHERE is_read = 0 AND (user_id = ? OR user_id IS NULL)",
                    (user_id,)
                )
            else:
                self.cursor.execute("SELECT COUNT(*) FROM notifications WHERE is_read = 0")

            return self.cursor.fetchone()[0]
        except Exception as e:
            print(f"Erreur lors du comptage des notifications : {str(e)}")
            return 0

    def check_and_create_alerts(self):
        """Vérifie et crée automatiquement des alertes"""
        try:
            alerts_created = 0

            # Alertes pour les tâches en retard
            self.cursor.execute("""
                SELECT id, title, due_date FROM tasks
                WHERE due_date < date('now') AND status != 'Terminée'
                AND id NOT IN (
                    SELECT related_id FROM notifications
                    WHERE related_table = 'tasks' AND type = 'warning'
                    AND title LIKE 'Tâche en retard%'
                    AND created_at > date('now', '-1 day')
                )
            """)

            overdue_tasks = self.cursor.fetchall()
            for task in overdue_tasks:
                self.add_notification(
                    f"Tâche en retard : {task[1]}",
                    f"La tâche '{task[1]}' était due le {task[2]}",
                    "warning",
                    related_table="tasks",
                    related_id=task[0]
                )
                alerts_created += 1

            # Alertes pour les pièces de rechange sous le seuil
            self.cursor.execute("""
                SELECT id, code, name, quantity, min_threshold FROM spare_parts
                WHERE quantity < min_threshold AND min_threshold > 0
                AND id NOT IN (
                    SELECT related_id FROM notifications
                    WHERE related_table = 'spare_parts' AND type = 'warning'
                    AND title LIKE 'Stock faible%'
                    AND created_at > date('now', '-1 day')
                )
            """)

            low_stock_parts = self.cursor.fetchall()
            for part in low_stock_parts:
                self.add_notification(
                    f"Stock faible : {part[1]} - {part[2]}",
                    f"Quantité actuelle: {part[3]}, Seuil minimum: {part[4]}",
                    "warning",
                    related_table="spare_parts",
                    related_id=part[0]
                )
                alerts_created += 1

            # Alertes pour les maintenances à venir
            self.cursor.execute("""
                SELECT id, name, next_maintenance_date FROM equipment
                WHERE next_maintenance_date <= date('now', '+7 days')
                AND next_maintenance_date >= date('now')
                AND id NOT IN (
                    SELECT related_id FROM notifications
                    WHERE related_table = 'equipment' AND type = 'info'
                    AND title LIKE 'Maintenance programmée%'
                    AND created_at > date('now', '-1 day')
                )
            """)

            upcoming_maintenance = self.cursor.fetchall()
            for equipment in upcoming_maintenance:
                self.add_notification(
                    f"Maintenance programmée : {equipment[1]}",
                    f"Maintenance prévue le {equipment[2]}",
                    "info",
                    related_table="equipment",
                    related_id=equipment[0]
                )
                alerts_created += 1

            return alerts_created

        except Exception as e:
            print(f"Erreur lors de la création des alertes : {str(e)}")
            return 0

    # Méthodes pour les logs et l'audit
    def add_audit_log(self, action, table_name, record_id, user_id=None, details=None):
        """Ajoute une entrée dans le log d'audit"""
        try:
            self.cursor.execute(
                """INSERT INTO audit_logs
                   (action, table_name, record_id, user_id, details, timestamp)
                   VALUES (?, ?, ?, ?, ?, datetime('now', 'localtime'))""",
                (action, table_name, record_id, user_id, details)
            )
            self.conn.commit()
        except Exception as e:
            print(f"Erreur lors de l'ajout du log d'audit : {str(e)}")

    def get_audit_logs(self, table_name=None, record_id=None, limit=100):
        """Récupère les logs d'audit"""
        try:
            query = "SELECT * FROM audit_logs WHERE 1=1"
            params = []

            if table_name:
                query += " AND table_name = ?"
                params.append(table_name)

            if record_id:
                query += " AND record_id = ?"
                params.append(record_id)

            query += " ORDER BY timestamp DESC LIMIT ?"
            params.append(limit)

            self.cursor.execute(query, params)
            return self.cursor.fetchall()
        except Exception as e:
            print(f"Erreur lors de la récupération des logs : {str(e)}")
            return []

    # Méthodes pour la maintenance de la base de données
    def cleanup_old_data(self, days_to_keep=365):
        """Nettoie les anciennes données"""
        try:
            cleaned_count = 0

            # Nettoyer les anciennes notifications lues
            self.cursor.execute(
                "DELETE FROM notifications WHERE is_read = 1 AND read_at < date('now', '-30 days')"
            )
            cleaned_count += self.cursor.rowcount

            # Nettoyer les anciens logs d'audit
            self.cursor.execute(
                f"DELETE FROM audit_logs WHERE timestamp < date('now', '-{days_to_keep} days')"
            )
            cleaned_count += self.cursor.rowcount

            # Nettoyer les anciens mouvements de stock
            self.cursor.execute(
                f"DELETE FROM stock_movements WHERE movement_date < date('now', '-{days_to_keep} days')"
            )
            cleaned_count += self.cursor.rowcount

            self.conn.commit()
            return cleaned_count

        except Exception as e:
            print(f"Erreur lors du nettoyage : {str(e)}")
            self.conn.rollback()
            return 0

    def optimize_database_performance(self):
        """Optimise les performances de la base de données"""
        try:
            operations = []

            # Analyser les tables
            self.cursor.execute("ANALYZE")
            operations.append("Tables analysées")

            # Reconstruire les index
            self.cursor.execute("REINDEX")
            operations.append("Index reconstruits")

            # Optimiser
            self.cursor.execute("PRAGMA optimize")
            operations.append("Base de données optimisée")

            # Compacter si nécessaire
            self.cursor.execute("PRAGMA page_count")
            page_count = self.cursor.fetchone()[0]

            self.cursor.execute("PRAGMA freelist_count")
            free_pages = self.cursor.fetchone()[0]

            # Si plus de 10% de pages libres, compacter
            if free_pages > page_count * 0.1:
                self.cursor.execute("VACUUM")
                operations.append("Base de données compactée")

            self.conn.commit()
            return operations

        except Exception as e:
            print(f"Erreur lors de l'optimisation : {str(e)}")
            return []

    # Méthodes utilitaires avancées
    def get_database_info(self):
        """Récupère des informations détaillées sur la base de données"""
        try:
            info = {}

            # Taille de la base de données
            self.cursor.execute("PRAGMA page_count")
            page_count = self.cursor.fetchone()[0]
            self.cursor.execute("PRAGMA page_size")
            page_size = self.cursor.fetchone()[0]
            info['size_mb'] = (page_count * page_size) / (1024 * 1024)

            # Version SQLite
            self.cursor.execute("SELECT sqlite_version()")
            info['sqlite_version'] = self.cursor.fetchone()[0]

            # Nombre d'enregistrements par table
            tables = ['tasks', 'categories', 'persons', 'equipment', 'spare_parts', 'attendance']
            info['record_counts'] = {}

            for table in tables:
                try:
                    self.cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    info['record_counts'][table] = self.cursor.fetchone()[0]
                except:
                    info['record_counts'][table] = 0

            # Informations sur les index
            self.cursor.execute("SELECT name FROM sqlite_master WHERE type='index'")
            info['indexes'] = [row[0] for row in self.cursor.fetchall()]

            # Statistiques du cache
            if hasattr(self, '_cache'):
                info['cache_entries'] = len(self._cache)
                info['cache_hit_ratio'] = getattr(self, '_cache_hits', 0) / max(getattr(self, '_cache_requests', 1), 1)

            return info

        except Exception as e:
            print(f"Erreur lors de la récupération des informations : {str(e)}")
            return {}

    def validate_data_integrity(self):
        """Valide l'intégrité des données"""
        try:
            issues = []

            # Vérifier l'intégrité de la base de données
            self.cursor.execute("PRAGMA integrity_check")
            integrity_result = self.cursor.fetchone()[0]
            if integrity_result != "ok":
                issues.append(f"Intégrité de la base : {integrity_result}")

            # Vérifier les clés étrangères
            self.cursor.execute("PRAGMA foreign_key_check")
            fk_errors = self.cursor.fetchall()
            if fk_errors:
                issues.extend([f"Erreur clé étrangère : {error}" for error in fk_errors])

            # Vérifier les données orphelines
            orphaned_queries = [
                ("Tâches sans catégorie", "SELECT COUNT(*) FROM tasks WHERE category_id NOT IN (SELECT id FROM categories)"),
                ("Assignations sans tâche", "SELECT COUNT(*) FROM task_assignments WHERE task_id NOT IN (SELECT id FROM tasks)"),
                ("Assignations sans personne", "SELECT COUNT(*) FROM task_assignments WHERE person_id NOT IN (SELECT id FROM persons)"),
                ("Pointages sans personne", "SELECT COUNT(*) FROM attendance WHERE person_id NOT IN (SELECT id FROM persons)")
            ]

            for description, query in orphaned_queries:
                try:
                    self.cursor.execute(query)
                    count = self.cursor.fetchone()[0]
                    if count > 0:
                        issues.append(f"{description} : {count} enregistrements")
                except:
                    pass

            return issues

        except Exception as e:
            print(f"Erreur lors de la validation : {str(e)}")
            return [f"Erreur de validation : {str(e)}"]

    # Méthodes pour la gestion des paramètres
    def get_setting(self, key, default_value=None):
        """Récupère un paramètre de l'application"""
        try:
            self.cursor.execute("SELECT setting_value, setting_type FROM app_settings WHERE setting_key = ?", (key,))
            result = self.cursor.fetchone()

            if not result:
                return default_value

            value, setting_type = result

            # Convertir selon le type
            if setting_type == 'boolean':
                return value.lower() in ('true', '1', 'yes', 'on')
            elif setting_type == 'integer':
                return int(value)
            elif setting_type == 'float':
                return float(value)
            else:
                return value

        except Exception as e:
            print(f"Erreur lors de la récupération du paramètre {key} : {str(e)}")
            return default_value

    def set_setting(self, key, value, setting_type='string', description=None):
        """Définit un paramètre de l'application"""
        try:
            # Convertir la valeur en string pour le stockage
            if isinstance(value, bool):
                str_value = 'true' if value else 'false'
                setting_type = 'boolean'
            else:
                str_value = str(value)

            # Insérer ou mettre à jour
            self.cursor.execute(
                """INSERT OR REPLACE INTO app_settings
                   (setting_key, setting_value, setting_type, description, updated_at)
                   VALUES (?, ?, ?, ?, datetime('now', 'localtime'))""",
                (key, str_value, setting_type, description)
            )
            self.conn.commit()

            # Invalider le cache pour ce paramètre
            cache_key = f"setting_{key}"
            if hasattr(self, '_cache') and cache_key in self._cache:
                del self._cache[cache_key]

        except Exception as e:
            print(f"Erreur lors de la définition du paramètre {key} : {str(e)}")
            self.conn.rollback()

    def get_all_settings(self):
        """Récupère tous les paramètres"""
        try:
            self.cursor.execute("SELECT setting_key, setting_value, setting_type, description FROM app_settings ORDER BY setting_key")
            return self.cursor.fetchall()
        except Exception as e:
            print(f"Erreur lors de la récupération des paramètres : {str(e)}")
            return []

    def reset_settings_to_default(self):
        """Remet les paramètres aux valeurs par défaut"""
        try:
            # Supprimer tous les paramètres
            self.cursor.execute("DELETE FROM app_settings")

            # Réinsérer les paramètres par défaut
            default_settings = [
                ('app_version', '2.1', 'string', 'Version de l\'application'),
                ('auto_backup', 'true', 'boolean', 'Sauvegarde automatique activée'),
                ('backup_frequency', '7', 'integer', 'Fréquence de sauvegarde en jours'),
                ('notification_enabled', 'true', 'boolean', 'Notifications activées'),
                ('theme', 'default', 'string', 'Thème de l\'interface'),
                ('language', 'fr', 'string', 'Langue de l\'interface'),
                ('max_recent_items', '10', 'integer', 'Nombre maximum d\'éléments récents'),
                ('session_timeout', '480', 'integer', 'Timeout de session en minutes'),
                ('debug_mode', 'false', 'boolean', 'Mode debug activé')
            ]

            for key, value, type_val, desc in default_settings:
                self.cursor.execute(
                    "INSERT INTO app_settings (setting_key, setting_value, setting_type, description) VALUES (?, ?, ?, ?)",
                    (key, value, type_val, desc)
                )

            self.conn.commit()

            # Vider le cache des paramètres
            if hasattr(self, '_cache'):
                keys_to_remove = [k for k in self._cache.keys() if k.startswith('setting_')]
                for key in keys_to_remove:
                    del self._cache[key]

        except Exception as e:
            print(f"Erreur lors de la remise à zéro des paramètres : {str(e)}")
            self.conn.rollback()

    # Méthodes pour la gestion des favoris
    def add_favorite(self, user_id, item_type, item_id, item_name):
        """Ajoute un élément aux favoris"""
        try:
            self.cursor.execute(
                """INSERT OR IGNORE INTO favorites (user_id, item_type, item_id, item_name)
                   VALUES (?, ?, ?, ?)""",
                (user_id, item_type, item_id, item_name)
            )
            self.conn.commit()
            return self.cursor.rowcount > 0
        except Exception as e:
            print(f"Erreur lors de l'ajout aux favoris : {str(e)}")
            return False

    def remove_favorite(self, user_id, item_type, item_id):
        """Supprime un élément des favoris"""
        try:
            self.cursor.execute(
                "DELETE FROM favorites WHERE user_id = ? AND item_type = ? AND item_id = ?",
                (user_id, item_type, item_id)
            )
            self.conn.commit()
            return self.cursor.rowcount > 0
        except Exception as e:
            print(f"Erreur lors de la suppression des favoris : {str(e)}")
            return False

    def get_favorites(self, user_id, item_type=None):
        """Récupère les favoris d'un utilisateur"""
        try:
            if item_type:
                self.cursor.execute(
                    "SELECT * FROM favorites WHERE user_id = ? AND item_type = ? ORDER BY created_at DESC",
                    (user_id, item_type)
                )
            else:
                self.cursor.execute(
                    "SELECT * FROM favorites WHERE user_id = ? ORDER BY item_type, created_at DESC",
                    (user_id,)
                )
            return self.cursor.fetchall()
        except Exception as e:
            print(f"Erreur lors de la récupération des favoris : {str(e)}")
            return []

    def is_favorite(self, user_id, item_type, item_id):
        """Vérifie si un élément est dans les favoris"""
        try:
            self.cursor.execute(
                "SELECT COUNT(*) FROM favorites WHERE user_id = ? AND item_type = ? AND item_id = ?",
                (user_id, item_type, item_id)
            )
            return self.cursor.fetchone()[0] > 0
        except Exception as e:
            print(f"Erreur lors de la vérification des favoris : {str(e)}")
            return False

    # Méthodes pour les recherches avancées
    def advanced_search(self, search_params):
        """Effectue une recherche avancée multi-tables"""
        try:
            results = {}
            query = search_params.get('query', '').strip()

            if not query:
                return results

            # Recherche dans les tâches
            if search_params.get('search_tasks', True):
                self.cursor.execute(
                    """SELECT 'task' as type, id, title as name,
                       COALESCE(description, '') as description, status, priority
                       FROM tasks
                       WHERE title LIKE ? OR COALESCE(description, '') LIKE ?
                       ORDER BY title LIMIT 20""",
                    (f"%{query}%", f"%{query}%")
                )
                results['tasks'] = self.cursor.fetchall()

            # Recherche dans les équipements
            if search_params.get('search_equipment', True):
                self.cursor.execute(
                    """SELECT 'equipment' as type, id, name,
                       COALESCE(description, '') as description, status, location
                       FROM equipment
                       WHERE name LIKE ? OR COALESCE(description, '') LIKE ? OR location LIKE ?
                       ORDER BY name LIMIT 20""",
                    (f"%{query}%", f"%{query}%", f"%{query}%")
                )
                results['equipment'] = self.cursor.fetchall()

            # Recherche dans le personnel
            if search_params.get('search_personnel', True):
                self.cursor.execute(
                    """SELECT 'person' as type, id, name, role, '', ''
                       FROM persons
                       WHERE name LIKE ? OR role LIKE ?
                       ORDER BY name LIMIT 20""",
                    (f"%{query}%", f"%{query}%")
                )
                results['personnel'] = self.cursor.fetchall()

            # Recherche dans les pièces de rechange
            if search_params.get('search_spare_parts', True):
                self.cursor.execute(
                    """SELECT 'spare_part' as type, id, name, code, part_type, location
                       FROM spare_parts
                       WHERE name LIKE ? OR code LIKE ? OR part_type LIKE ?
                       ORDER BY name LIMIT 20""",
                    (f"%{query}%", f"%{query}%", f"%{query}%")
                )
                results['spare_parts'] = self.cursor.fetchall()

            return results

        except Exception as e:
            print(f"Erreur lors de la recherche avancée : {str(e)}")
            return {}

    def get_recent_items(self, user_id=None, limit=10):
        """Récupère les éléments récemment consultés/modifiés"""
        try:
            recent_items = []

            # Tâches récentes
            try:
                self.cursor.execute(
                    """SELECT 'task' as type, id, title as name,
                       COALESCE(created_at, datetime('now')) as date
                       FROM tasks
                       ORDER BY id DESC LIMIT ?""",
                    (limit // 4,)
                )
                recent_items.extend(self.cursor.fetchall())
            except:
                pass

            # Équipements récents
            try:
                self.cursor.execute(
                    """SELECT 'equipment' as type, id, name,
                       COALESCE(created_at, datetime('now')) as date
                       FROM equipment
                       ORDER BY id DESC LIMIT ?""",
                    (limit // 4,)
                )
                recent_items.extend(self.cursor.fetchall())
            except:
                pass

            # Pièces récentes
            try:
                self.cursor.execute(
                    """SELECT 'spare_part' as type, id, name,
                       COALESCE(last_updated, datetime('now')) as date
                       FROM spare_parts
                       ORDER BY id DESC LIMIT ?""",
                    (limit // 4,)
                )
                recent_items.extend(self.cursor.fetchall())
            except:
                pass

            # Personnel récent
            try:
                self.cursor.execute(
                    """SELECT 'person' as type, id, name,
                       COALESCE(created_at, datetime('now')) as date
                       FROM persons
                       ORDER BY id DESC LIMIT ?""",
                    (limit // 4,)
                )
                recent_items.extend(self.cursor.fetchall())
            except:
                pass

            # Trier par date et limiter
            recent_items.sort(key=lambda x: x[3], reverse=True)
            return recent_items[:limit]

        except Exception as e:
            print(f"Erreur lors de la récupération des éléments récents : {str(e)}")
            return []