#!/usr/bin/env python3
"""
Application principale avec menu de maintenance intégré
Version simplifiée qui évite les problèmes d'import
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QTabWidget, QLabel, QFrame, QPushButton,
                             QMessageBox, QStatusBar)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QIcon

from database import Database
from gui.maintenance_menu_simple import MaintenanceMenu

class MainApplication(QMainWindow):
    """Application principale avec menu de maintenance intégré"""
    
    def __init__(self):
        super().__init__()
        self.db = Database("data/test_maintenance.db")  # Base de données de test
        self.setup_ui()
        self.setup_status_bar()
        
        # Timer pour actualisation
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_application)
        self.refresh_timer.start(60000)  # 1 minute
    
    def setup_ui(self):
        """Configure l'interface utilisateur principale"""
        self.setWindowTitle("🔧 Application de Maintenance - SOTRAMINE PHOSPHATE")
        self.setGeometry(100, 100, 1400, 900)
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principal
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # En-tête de l'application
        header = self.create_header()
        main_layout.addWidget(header)
        
        # Onglets principaux
        self.main_tabs = QTabWidget()
        self.main_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dcdde1;
                background-color: white;
                border-radius: 8px;
            }
            QTabBar::tab {
                background-color: #f8f9fa;
                border: 1px solid #dcdde1;
                padding: 12px 20px;
                margin-right: 2px;
                font-weight: bold;
                min-width: 150px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
                border-bottom: 2px solid #2980b9;
            }
            QTabBar::tab:hover {
                background-color: #e3f2fd;
                color: #1976d2;
            }
        """)
        
        # Onglet Accueil
        home_tab = self.create_home_tab()
        self.main_tabs.addTab(home_tab, "🏠 Accueil")
        
        # Onglet Maintenance (intégré)
        maintenance_menu = MaintenanceMenu(self.db, self)
        self.main_tabs.addTab(maintenance_menu, "🔧 Maintenance")
        
        # Onglet Gestion des Tâches (simplifié)
        tasks_tab = self.create_tasks_tab()
        self.main_tabs.addTab(tasks_tab, "📋 Gestion des Tâches")
        
        # Onglet Équipements (simplifié)
        equipment_tab = self.create_equipment_tab()
        self.main_tabs.addTab(equipment_tab, "⚙️ Équipements")
        
        # Onglet Personnel (simplifié)
        personnel_tab = self.create_personnel_tab()
        self.main_tabs.addTab(personnel_tab, "👥 Personnel")
        
        main_layout.addWidget(self.main_tabs)
        
        # Connecter les signaux du menu de maintenance
        maintenance_menu.maintenance_updated.connect(self.on_maintenance_updated)
        maintenance_menu.intervention_created.connect(self.on_intervention_created)
        maintenance_menu.work_order_created.connect(self.on_work_order_created)
    
    def create_header(self):
        """Crée l'en-tête de l'application"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2c3e50, stop:1 #34495e);
                border-radius: 15px;
                padding: 25px;
            }
        """)
        
        header_layout = QVBoxLayout(header_frame)
        
        # Titre principal
        title = QLabel("🔧 SOTRAMINE PHOSPHATE")
        title.setStyleSheet("""
            font-size: 32px; font-weight: bold; color: white; 
            margin-bottom: 10px; text-align: center;
        """)
        title.setAlignment(Qt.AlignCenter)
        
        # Sous-titre
        subtitle = QLabel("Application de Gestion de Maintenance Intégrée")
        subtitle.setStyleSheet("""
            font-size: 18px; color: #ecf0f1; text-align: center;
        """)
        subtitle.setAlignment(Qt.AlignCenter)
        
        # Description
        description = QLabel("Menu de maintenance complet avec interventions, planification et indicateurs")
        description.setStyleSheet("""
            font-size: 14px; color: #bdc3c7; text-align: center;
        """)
        description.setAlignment(Qt.AlignCenter)
        
        header_layout.addWidget(title)
        header_layout.addWidget(subtitle)
        header_layout.addWidget(description)
        
        return header_frame
    
    def create_home_tab(self):
        """Crée l'onglet d'accueil"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Titre
        title = QLabel("🏠 Bienvenue dans l'Application de Maintenance")
        title.setStyleSheet("font-size: 24px; font-weight: bold; color: #2c3e50; margin: 20px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Message d'information
        info = QLabel("✅ Le menu de maintenance est maintenant intégré et fonctionnel !")
        info.setStyleSheet("font-size: 18px; color: #27ae60; margin: 20px;")
        info.setAlignment(Qt.AlignCenter)
        layout.addWidget(info)
        
        # Actions rapides
        actions_frame = QFrame()
        actions_frame.setStyleSheet("QFrame { background-color: #ecf0f1; border-radius: 10px; padding: 20px; }")
        actions_layout = QVBoxLayout(actions_frame)
        
        actions_title = QLabel("⚡ Accès Rapide")
        actions_title.setStyleSheet("font-size: 20px; font-weight: bold; color: #34495e; margin-bottom: 15px;")
        actions_layout.addWidget(actions_title)
        
        # Boutons d'action
        btn_maintenance = QPushButton("🔧 Accéder au Menu Maintenance")
        btn_maintenance.setStyleSheet("""
            QPushButton { 
                background-color: #3498db; color: white; border: none; 
                padding: 15px 30px; border-radius: 8px; font-weight: bold; font-size: 16px; 
            } 
            QPushButton:hover { background-color: #2980b9; }
        """)
        btn_maintenance.clicked.connect(lambda: self.main_tabs.setCurrentIndex(1))
        
        btn_tasks = QPushButton("📋 Gérer les Tâches")
        btn_tasks.setStyleSheet("""
            QPushButton { 
                background-color: #e74c3c; color: white; border: none; 
                padding: 15px 30px; border-radius: 8px; font-weight: bold; font-size: 16px; 
            } 
            QPushButton:hover { background-color: #c0392b; }
        """)
        btn_tasks.clicked.connect(lambda: self.main_tabs.setCurrentIndex(2))
        
        actions_layout.addWidget(btn_maintenance)
        actions_layout.addWidget(btn_tasks)
        
        layout.addWidget(actions_frame)
        layout.addStretch()
        
        return tab
    
    def create_tasks_tab(self):
        """Crée l'onglet de gestion des tâches simplifié"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        header = QLabel("📋 Gestion des Tâches")
        header.setStyleSheet("font-size: 20px; font-weight: bold; color: #2c3e50; margin: 20px;")
        header.setAlignment(Qt.AlignCenter)
        layout.addWidget(header)
        
        info = QLabel("✅ Module de gestion des tâches - Prêt pour l'intégration complète")
        info.setStyleSheet("font-size: 16px; color: #27ae60; margin: 20px;")
        info.setAlignment(Qt.AlignCenter)
        layout.addWidget(info)
        
        layout.addStretch()
        return tab
    
    def create_equipment_tab(self):
        """Crée l'onglet d'équipements simplifié"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        header = QLabel("⚙️ Gestion des Équipements")
        header.setStyleSheet("font-size: 20px; font-weight: bold; color: #2c3e50; margin: 20px;")
        header.setAlignment(Qt.AlignCenter)
        layout.addWidget(header)
        
        info = QLabel("✅ Module de gestion des équipements - Prêt pour l'intégration complète")
        info.setStyleSheet("font-size: 16px; color: #27ae60; margin: 20px;")
        info.setAlignment(Qt.AlignCenter)
        layout.addWidget(info)
        
        layout.addStretch()
        return tab
    
    def create_personnel_tab(self):
        """Crée l'onglet de personnel simplifié"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        header = QLabel("👥 Gestion du Personnel")
        header.setStyleSheet("font-size: 20px; font-weight: bold; color: #2c3e50; margin: 20px;")
        header.setAlignment(Qt.AlignCenter)
        layout.addWidget(header)
        
        info = QLabel("✅ Module de gestion du personnel - Prêt pour l'intégration complète")
        info.setStyleSheet("font-size: 16px; color: #27ae60; margin: 20px;")
        info.setAlignment(Qt.AlignCenter)
        layout.addWidget(info)
        
        layout.addStretch()
        return tab
    
    def setup_status_bar(self):
        """Configure la barre de statut"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("✅ Application de maintenance prête - Menu intégré avec succès")
    
    def refresh_application(self):
        """Actualise l'application"""
        print("🔄 Actualisation de l'application...")
        self.status_bar.showMessage("🔄 Application actualisée")
    
    def on_maintenance_updated(self):
        """Appelé quand la maintenance est mise à jour"""
        print("✅ Maintenance mise à jour")
        self.status_bar.showMessage("✅ Maintenance mise à jour")
    
    def on_intervention_created(self, intervention_id):
        """Appelé quand une intervention est créée"""
        print(f"✅ Intervention créée: {intervention_id}")
        self.status_bar.showMessage(f"✅ Intervention {intervention_id} créée")
    
    def on_work_order_created(self, work_order_id):
        """Appelé quand un bon de travail est créé"""
        print(f"✅ Bon de travail créé: {work_order_id}")
        self.status_bar.showMessage(f"✅ Bon de travail {work_order_id} créé")

def main():
    """Fonction principale"""
    try:
        app = QApplication(sys.argv)
        app.setApplicationName("Application de Maintenance - SOTRAMINE PHOSPHATE")
        
        # Créer et afficher la fenêtre principale
        main_window = MainApplication()
        main_window.show()
        
        print("🎉 Application de maintenance lancée avec succès !")
        print("✅ Menu de maintenance intégré et fonctionnel")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ Erreur lors du lancement: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
