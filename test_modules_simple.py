#!/usr/bin/env python3
"""
Test simple des modules SOTRAMINE
"""

import sys
import traceback

def test_module_import(module_name, class_name):
    """Teste l'import d'un module spécifique"""
    try:
        print(f"🔍 Test de {module_name}...")
        module = __import__(module_name, fromlist=[class_name])
        print(f"✅ {module_name} importé avec succès")
        
        # Test de la classe
        if hasattr(module, class_name):
            print(f"✅ Classe {class_name} trouvée")
            return True
        else:
            print(f"❌ Classe {class_name} non trouvée dans {module_name}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur import {module_name}: {str(e)}")
        traceback.print_exc()
        return False

def test_database():
    """Teste la base de données"""
    try:
        print("🗄️ Test de la base de données...")
        from database import Database
        db = Database()
        print("✅ Base de données créée avec succès")
        return db
    except Exception as e:
        print(f"❌ Erreur base de données: {str(e)}")
        traceback.print_exc()
        return None

def test_module_creation(module_name, class_name, db):
    """Teste la création d'une instance de module"""
    try:
        print(f"🏗️ Test création {class_name}...")
        module = __import__(module_name, fromlist=[class_name])
        class_obj = getattr(module, class_name)
        
        # Créer une instance (sans parent pour éviter les erreurs Qt)
        instance = class_obj(db, None)
        print(f"✅ Instance {class_name} créée avec succès")
        return instance
        
    except Exception as e:
        print(f"❌ Erreur création {class_name}: {str(e)}")
        traceback.print_exc()
        return None

def main():
    """Test principal"""
    print("🚀 TEST DES MODULES SOTRAMINE")
    print("="*50)
    
    # Test de la base de données
    db = test_database()
    if not db:
        print("❌ Impossible de continuer sans base de données")
        return
    
    # Modules à tester
    modules_to_test = [
        ("gui.task_manager_complete", "TaskManagerComplete"),
        ("gui.equipment_manager_complete", "EquipmentManagerComplete"),
        ("gui.personnel_manager_complete", "PersonnelManagerComplete"),
        ("gui.attendance_manager_complete", "AttendanceManagerComplete"),
        ("gui.reports_manager_complete", "ReportsManagerComplete"),
        ("gui.spare_parts_manager", "SparePartsManager"),
        ("gui.maintenance_menu", "MaintenanceMenu")
    ]
    
    print("\n📋 RÉSULTATS DES TESTS:")
    print("="*50)
    
    success_count = 0
    total_count = len(modules_to_test)
    
    for module_name, class_name in modules_to_test:
        print(f"\n--- Test de {class_name} ---")
        
        # Test d'import
        if test_module_import(module_name, class_name):
            # Test de création
            if test_module_creation(module_name, class_name, db):
                success_count += 1
                print(f"✅ {class_name} - COMPLET")
            else:
                print(f"❌ {class_name} - ÉCHEC CRÉATION")
        else:
            print(f"❌ {class_name} - ÉCHEC IMPORT")
    
    print("\n" + "="*50)
    print(f"📊 RÉSULTAT FINAL: {success_count}/{total_count} modules fonctionnels")
    
    if success_count == total_count:
        print("🎉 TOUS LES MODULES FONCTIONNENT PARFAITEMENT !")
    elif success_count > total_count // 2:
        print("⚠️ La plupart des modules fonctionnent, quelques erreurs mineures")
    else:
        print("❌ Nombreux problèmes détectés, vérification nécessaire")

if __name__ == "__main__":
    main()
