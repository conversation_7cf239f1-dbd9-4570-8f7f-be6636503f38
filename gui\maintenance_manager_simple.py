"""
Gestionnaire de maintenance simplifié pour éviter les erreurs
Version fonctionnelle du système de maintenance
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, 
                             QLabel, QPushButton, QFrame, QMessageBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont
from utils.data_refresh_manager import RefreshableWidget, notify_data_changed

class MaintenanceManagerSimple(QWidget, RefreshableWidget):
    """Gestionnaire de maintenance simplifié"""
    
    section_requested = pyqtSignal(str)
    
    def __init__(self, db, parent=None):
        super().__init__(parent)
        RefreshableWidget.__init__(self)
        self.db = db
        
        self.setup_ui()
        self.enable_auto_refresh(['equipment', 'maintenance'])
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # En-tête de la section maintenance
        header = self.create_maintenance_header()
        layout.addWidget(header)
        
        # Tableau de bord maintenance
        dashboard = self.create_maintenance_dashboard()
        layout.addWidget(dashboard)
        
        # Onglets principaux de maintenance
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dcdde1;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f8f9fa;
                border: 1px solid #dcdde1;
                padding: 12px 20px;
                margin-right: 2px;
                font-weight: bold;
                min-width: 120px;
            }
            QTabBar::tab:selected {
                background-color: #f39c12;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #e67e22;
                color: white;
            }
        """)
        
        # Onglets simplifiés
        self.tab_widget.addTab(self.create_equipment_tab(), "🔌 Équipements")
        self.tab_widget.addTab(self.create_interventions_tab(), "🔧 Interventions")
        self.tab_widget.addTab(self.create_preventive_tab(), "📅 Planification")
        self.tab_widget.addTab(self.create_spare_parts_tab(), "🔧 Pièces")
        self.tab_widget.addTab(self.create_technicians_tab(), "👷 Techniciens")
        self.tab_widget.addTab(self.create_history_tab(), "📊 Historique & KPI")
        
        layout.addWidget(self.tab_widget)
    
    def create_maintenance_header(self):
        """Crée l'en-tête de la section maintenance"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f39c12, stop:1 #e67e22);
                border-radius: 0;
                padding: 20px;
                margin: 0;
            }
        """)
        header_frame.setFixedHeight(80)
        
        layout = QHBoxLayout(header_frame)
        layout.setContentsMargins(20, 15, 20, 15)
        
        # Titre et icône
        title_layout = QHBoxLayout()
        
        icon_label = QLabel("🔧")
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 32px;
                color: white;
                margin-right: 15px;
            }
        """)
        title_layout.addWidget(icon_label)
        
        title_text_layout = QVBoxLayout()
        title_text_layout.setSpacing(2)
        
        title_label = QLabel("MAINTENANCE INDUSTRIELLE")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: white;
                margin: 0;
            }
        """)
        title_text_layout.addWidget(title_label)
        
        subtitle_label = QLabel("Système complet de gestion de maintenance")
        subtitle_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: rgba(255, 255, 255, 0.9);
                margin: 0;
            }
        """)
        title_text_layout.addWidget(subtitle_label)
        
        title_layout.addLayout(title_text_layout)
        layout.addLayout(title_layout)
        
        layout.addStretch()
        
        # Boutons d'actions rapides
        actions_layout = QHBoxLayout()
        actions_layout.setSpacing(10)
        
        quick_actions = [
            ("🚨 Intervention Urgente", self.create_urgent_intervention, "#e74c3c"),
            ("📋 Nouveau Bon de Travail", self.create_work_order, "#3498db"),
            ("📅 Planifier Maintenance", self.schedule_maintenance, "#27ae60")
        ]
        
        for text, action, color in quick_actions:
            btn = QPushButton(text)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 10px 16px;
                    font-weight: bold;
                    font-size: 12px;
                    min-width: 140px;
                }}
                QPushButton:hover {{
                    background-color: {color}dd;
                }}
                QPushButton:pressed {{
                    background-color: {color}bb;
                }}
            """)
            btn.clicked.connect(action)
            actions_layout.addWidget(btn)
        
        layout.addLayout(actions_layout)
        
        return header_frame
    
    def create_maintenance_dashboard(self):
        """Crée le tableau de bord de maintenance"""
        dashboard_frame = QFrame()
        dashboard_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-bottom: 1px solid #dee2e6;
                padding: 15px;
            }
        """)
        dashboard_frame.setFixedHeight(120)
        
        layout = QHBoxLayout(dashboard_frame)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(30)
        
        # Indicateurs clés simplifiés
        kpis = [
            ("🔌", "Équipements", "0", "#3498db"),
            ("⏳", "En Attente", "0", "#f39c12"),
            ("🚨", "Urgentes", "0", "#e74c3c"),
            ("📅", "Préventives", "0", "#9b59b6"),
            ("👷", "Techniciens", "0", "#27ae60"),
            ("⚠️", "Stock Faible", "0", "#e67e22")
        ]
        
        for icon, label, value, color in kpis:
            kpi_widget = self.create_kpi_widget(icon, label, value, color)
            layout.addWidget(kpi_widget)
        
        layout.addStretch()
        
        return dashboard_frame
    
    def create_kpi_widget(self, icon, label, value, color):
        """Crée un widget d'indicateur KPI"""
        widget = QFrame()
        widget.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 2px solid {color};
                border-radius: 8px;
                padding: 10px;
                min-width: 100px;
            }}
            QFrame:hover {{
                background-color: {color}10;
            }}
        """)
        
        layout = QVBoxLayout(widget)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(5)
        
        # Icône et valeur
        icon_value = QLabel(f"{icon} {value}")
        icon_value.setStyleSheet(f"""
            QLabel {{
                font-size: 20px;
                font-weight: bold;
                color: {color};
            }}
        """)
        icon_value.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_value)
        
        # Label
        label_widget = QLabel(label)
        label_widget.setStyleSheet("""
            QLabel {
                font-size: 11px;
                color: #6c757d;
                font-weight: bold;
            }
        """)
        label_widget.setAlignment(Qt.AlignCenter)
        layout.addWidget(label_widget)
        
        return widget
    
    def create_equipment_tab(self):
        """Crée l'onglet de gestion des équipements"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Message informatif
        info_label = QLabel("🔌 Gestion Avancée des Équipements")
        info_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #495057;
                padding: 20px;
                background-color: #e3f2fd;
                border-radius: 8px;
                margin-bottom: 20px;
            }
        """)
        info_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(info_label)
        
        # Boutons d'actions
        actions_layout = QHBoxLayout()
        
        btn_manage = QPushButton("🔌 Gérer les Équipements")
        btn_manage.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 15px 25px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        btn_manage.clicked.connect(lambda: self.section_requested.emit('equipment'))
        actions_layout.addWidget(btn_manage)
        
        btn_add = QPushButton("➕ Nouvel Équipement")
        btn_add.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 15px 25px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        btn_add.clicked.connect(self.add_equipment)
        actions_layout.addWidget(btn_add)
        
        layout.addLayout(actions_layout)
        layout.addStretch()
        
        return tab
    
    def create_interventions_tab(self):
        """Crée l'onglet des interventions"""
        return self.create_placeholder_tab("🔧 Interventions et Bons de Travail", "#e74c3c")
    
    def create_preventive_tab(self):
        """Crée l'onglet de planification préventive"""
        return self.create_placeholder_tab("📅 Planification Préventive", "#9b59b6")
    
    def create_spare_parts_tab(self):
        """Crée l'onglet des pièces de rechange"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Message informatif
        info_label = QLabel("🔧 Gestion des Pièces de Rechange")
        info_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #495057;
                padding: 20px;
                background-color: #fff3e0;
                border-radius: 8px;
                margin-bottom: 20px;
            }
        """)
        info_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(info_label)
        
        # Bouton d'action
        btn_manage = QPushButton("🔧 Gérer les Pièces de Rechange")
        btn_manage.setStyleSheet("""
            QPushButton {
                background-color: #ff9800;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 15px 25px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #f57c00;
            }
        """)
        btn_manage.clicked.connect(lambda: self.section_requested.emit('spare_parts'))
        layout.addWidget(btn_manage)
        
        layout.addStretch()
        
        return tab
    
    def create_technicians_tab(self):
        """Crée l'onglet des techniciens"""
        return self.create_placeholder_tab("👷 Gestion des Techniciens", "#27ae60")
    
    def create_history_tab(self):
        """Crée l'onglet d'historique"""
        return self.create_placeholder_tab("📊 Historique et Indicateurs", "#17a2b8")
    
    def create_placeholder_tab(self, title, color):
        """Crée un onglet placeholder"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Message informatif
        info_label = QLabel(title)
        info_label.setStyleSheet(f"""
            QLabel {{
                font-size: 18px;
                font-weight: bold;
                color: #495057;
                padding: 20px;
                background-color: {color}20;
                border-radius: 8px;
                margin-bottom: 20px;
            }}
        """)
        info_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(info_label)
        
        # Message de développement
        dev_label = QLabel("🚧 Module en cours de développement\n\nCette fonctionnalité sera disponible dans une prochaine version.")
        dev_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #6c757d;
                padding: 20px;
                background-color: #f8f9fa;
                border-radius: 8px;
                text-align: center;
            }
        """)
        dev_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(dev_label)
        
        layout.addStretch()
        
        return tab
    
    # Méthodes d'actions
    def create_urgent_intervention(self):
        """Crée une intervention urgente"""
        QMessageBox.information(self, "Intervention Urgente", 
                               "🚨 Fonction d'intervention urgente\n\nCette fonctionnalité sera implémentée dans une prochaine version.")
    
    def create_work_order(self):
        """Crée un nouveau bon de travail"""
        QMessageBox.information(self, "Bon de Travail", 
                               "📋 Création de bon de travail\n\nCette fonctionnalité sera implémentée dans une prochaine version.")
    
    def schedule_maintenance(self):
        """Planifie une maintenance préventive"""
        QMessageBox.information(self, "Planification", 
                               "📅 Planification de maintenance\n\nCette fonctionnalité sera implémentée dans une prochaine version.")
    
    def add_equipment(self):
        """Ajoute un nouvel équipement"""
        try:
            from gui.equipment_manager import EquipmentDialog
            from PyQt5.QtWidgets import QDialog
            dialog = EquipmentDialog(self, self.db)
            if dialog.exec_() == QDialog.Accepted:
                notify_data_changed('equipment')
                QMessageBox.information(self, "Succès", "Équipement ajouté avec succès !")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajout : {str(e)}")
    
    def refresh_data(self):
        """Actualise les données (pour le système d'actualisation automatique)"""
        # Actualisation simplifiée
        pass
