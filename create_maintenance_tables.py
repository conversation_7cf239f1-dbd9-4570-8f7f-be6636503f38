#!/usr/bin/env python3
"""
Script pour créer les tables de maintenance nécessaires
"""

import sys
import os

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_maintenance_tables():
    """Crée toutes les tables nécessaires pour le système de maintenance"""
    print("🔧 CRÉATION DES TABLES DE MAINTENANCE")
    print("=" * 50)
    
    try:
        from database import Database
        
        db = Database()
        cursor = db.cursor
        print("✓ Base de données ouverte")
        
        # Table des interventions de maintenance
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS maintenance_interventions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                equipment_id INTEGER,
                intervention_type TEXT NOT NULL,
                priority TEXT DEFAULT 'Normale',
                status TEXT DEFAULT 'En attente',
                scheduled_date DATETIME,
                actual_start_date DATETIME,
                actual_end_date DATETIME,
                estimated_duration REAL DEFAULT 0,
                actual_duration REAL DEFAULT 0,
                description TEXT,
                notes TEXT,
                progress INTEGER DEFAULT 0,
                technician_id INTEGER,
                created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (equipment_id) REFERENCES equipment (id),
                FOREIGN KEY (technician_id) REFERENCES technicians (id)
            )
        """)
        print("✓ Table maintenance_interventions créée")
        
        # Table des bons de travail
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS work_orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                work_order_number TEXT UNIQUE NOT NULL,
                equipment_id INTEGER,
                work_type TEXT NOT NULL,
                priority TEXT DEFAULT 'Normale',
                status TEXT DEFAULT 'Créé',
                requester_name TEXT,
                assigned_technician_id INTEGER,
                description TEXT,
                creation_date DATE DEFAULT CURRENT_DATE,
                due_date DATE,
                completion_date DATE,
                estimated_cost REAL DEFAULT 0,
                actual_cost REAL DEFAULT 0,
                notes TEXT,
                FOREIGN KEY (equipment_id) REFERENCES equipment (id),
                FOREIGN KEY (assigned_technician_id) REFERENCES technicians (id)
            )
        """)
        print("✓ Table work_orders créée")
        
        # Table des planifications de maintenance préventive
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS maintenance_schedules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                equipment_id INTEGER NOT NULL,
                maintenance_type TEXT NOT NULL,
                frequency_value INTEGER NOT NULL,
                frequency_unit TEXT NOT NULL,
                last_maintenance_date DATE,
                next_maintenance_date DATE,
                assigned_technician_id INTEGER,
                status TEXT DEFAULT 'Actif',
                notes TEXT,
                created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (equipment_id) REFERENCES equipment (id),
                FOREIGN KEY (assigned_technician_id) REFERENCES technicians (id)
            )
        """)
        print("✓ Table maintenance_schedules créée")
        
        # Table des modèles de maintenance
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS maintenance_templates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                maintenance_type TEXT NOT NULL,
                frequency_value INTEGER NOT NULL,
                frequency_unit TEXT NOT NULL,
                estimated_duration REAL DEFAULT 0,
                description TEXT,
                checklist TEXT,
                required_parts TEXT,
                usage_count INTEGER DEFAULT 0,
                created_date DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("✓ Table maintenance_templates créée")
        
        # Table des pièces utilisées dans les interventions
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS intervention_parts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                intervention_id INTEGER NOT NULL,
                part_id INTEGER NOT NULL,
                quantity INTEGER NOT NULL,
                unit_cost REAL DEFAULT 0,
                total_cost REAL DEFAULT 0,
                FOREIGN KEY (intervention_id) REFERENCES maintenance_interventions (id),
                FOREIGN KEY (part_id) REFERENCES spare_parts (id)
            )
        """)
        print("✓ Table intervention_parts créée")
        
        # Table de l'historique des interventions
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS intervention_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                intervention_id INTEGER NOT NULL,
                event_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                event_type TEXT NOT NULL,
                description TEXT,
                user_name TEXT,
                FOREIGN KEY (intervention_id) REFERENCES maintenance_interventions (id)
            )
        """)
        print("✓ Table intervention_history créée")
        
        # Table des techniciens
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS technicians (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                specialization TEXT,
                phone TEXT,
                email TEXT,
                status TEXT DEFAULT 'Actif',
                hire_date DATE,
                hourly_rate REAL DEFAULT 0,
                notes TEXT,
                created_date DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("✓ Table technicians créée")
        
        # Table des documents d'équipements
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS equipment_documents (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                equipment_id INTEGER NOT NULL,
                name TEXT NOT NULL,
                document_type TEXT,
                file_path TEXT,
                upload_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                file_size INTEGER DEFAULT 0,
                notes TEXT,
                FOREIGN KEY (equipment_id) REFERENCES equipment (id)
            )
        """)
        print("✓ Table equipment_documents créée")
        
        # Ajouter des colonnes manquantes à la table equipment si nécessaire
        try:
            cursor.execute("ALTER TABLE equipment ADD COLUMN last_maintenance_date DATE")
            print("✓ Colonne last_maintenance_date ajoutée à equipment")
        except:
            pass  # Colonne existe déjà
        
        try:
            cursor.execute("ALTER TABLE equipment ADD COLUMN next_maintenance_date DATE")
            print("✓ Colonne next_maintenance_date ajoutée à equipment")
        except:
            pass  # Colonne existe déjà
        
        try:
            cursor.execute("ALTER TABLE equipment ADD COLUMN maintenance_frequency INTEGER DEFAULT 0")
            print("✓ Colonne maintenance_frequency ajoutée à equipment")
        except:
            pass  # Colonne existe déjà
        
        try:
            cursor.execute("ALTER TABLE equipment ADD COLUMN maintenance_type TEXT")
            print("✓ Colonne maintenance_type ajoutée à equipment")
        except:
            pass  # Colonne existe déjà
        
        # Insérer quelques techniciens de test
        cursor.execute("""
            INSERT OR IGNORE INTO technicians (name, specialization, status)
            VALUES 
                ('Jean Dupont', 'Mécanique générale', 'Actif'),
                ('Marie Martin', 'Électricité', 'Actif'),
                ('Pierre Durand', 'Hydraulique', 'Actif'),
                ('Sophie Bernard', 'Automatisme', 'Actif'),
                ('Luc Moreau', 'Maintenance préventive', 'Actif')
        """)
        print("✓ Techniciens de test ajoutés")
        
        # Insérer quelques modèles de maintenance
        cursor.execute("""
            INSERT OR IGNORE INTO maintenance_templates (name, maintenance_type, frequency_value, frequency_unit, estimated_duration, description)
            VALUES 
                ('Maintenance Préventive Standard', 'Préventive', 3, 'mois', 2.0, 'Maintenance préventive standard trimestrielle'),
                ('Révision Annuelle', 'Révision', 12, 'mois', 8.0, 'Révision complète annuelle'),
                ('Contrôle Mensuel', 'Contrôle', 1, 'mois', 0.5, 'Contrôle mensuel de routine'),
                ('Maintenance Corrective', 'Corrective', 0, 'intervention', 4.0, 'Intervention corrective suite à panne'),
                ('Nettoyage Hebdomadaire', 'Nettoyage', 1, 'semaine', 1.0, 'Nettoyage et vérification hebdomadaire')
        """)
        print("✓ Modèles de maintenance ajoutés")
        
        db.conn.commit()
        print("\n✅ Toutes les tables de maintenance ont été créées avec succès !")
        
        # Vérifier les tables créées
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name LIKE '%maintenance%' OR name LIKE '%work_order%' OR name = 'technicians'
            ORDER BY name
        """)
        
        tables = cursor.fetchall()
        print(f"\n📊 {len(tables)} tables de maintenance créées :")
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table[0]}")
            count = cursor.fetchone()[0]
            print(f"   • {table[0]} : {count} enregistrement(s)")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la création des tables : {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Point d'entrée principal"""
    print("🔧 SOTRAMINE PHOSPHATE - CRÉATION TABLES MAINTENANCE")
    print("Version 2.1 - Système de Maintenance Professionnel")
    print("=" * 60)
    
    success = create_maintenance_tables()
    
    if success:
        print("\n🎉 CRÉATION DES TABLES RÉUSSIE !")
        print("✅ Le système de maintenance est prêt à être utilisé")
        print("\n📝 TABLES CRÉÉES :")
        print("   • maintenance_interventions : Interventions de maintenance")
        print("   • work_orders : Bons de travail")
        print("   • maintenance_schedules : Planifications préventives")
        print("   • maintenance_templates : Modèles de maintenance")
        print("   • intervention_parts : Pièces utilisées")
        print("   • intervention_history : Historique des interventions")
        print("   • technicians : Techniciens de maintenance")
        print("   • equipment_documents : Documents d'équipements")
        
        print("\n🚀 PROCHAINES ÉTAPES :")
        print("   1. Lancer l'application : python main.py")
        print("   2. Aller dans Menu Maintenance")
        print("   3. Commencer à utiliser le système professionnel")
    else:
        print("\n❌ ÉCHEC DE LA CRÉATION DES TABLES")
        print("🔧 Vérifiez les erreurs ci-dessus")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        print(f"\n👋 Création des tables terminée - {'Succès' if success else 'Échec'}")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 Création interrompue par l'utilisateur")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Erreur critique : {str(e)}")
        sys.exit(1)
