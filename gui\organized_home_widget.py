"""
Widget d'accueil organisé par sections : Production, Maintenance, Personnel
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QPushButton, QFrame, QGridLayout, QScrollArea)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap, QPalette

class OrganizedHomeWidget(QWidget):
    """Widget d'accueil organisé en trois grandes sections"""
    
    section_requested = pyqtSignal(str)
    
    def __init__(self, db, parent=None):
        super().__init__(parent)
        self.db = db
        self.setup_ui()
        self.update_statistics()
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # Titre principal
        title = QLabel("SOTRAMINE PHOSPHATE")
        title.setStyleSheet("""
            QLabel {
                font-size: 28px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border-radius: 12px;
                margin-bottom: 10px;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Sous-titre
        subtitle = QLabel("Système de Gestion Intégré - Tableau de Bord")
        subtitle.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #7f8c8d;
                padding: 10px;
                text-align: center;
            }
        """)
        subtitle.setAlignment(Qt.AlignCenter)
        layout.addWidget(subtitle)
        
        # Zone de contenu avec scroll
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(30)
        
        # Section PRODUCTION
        production_section = self.create_section(
            "📊 PRODUCTION",
            "#e74c3c",
            [
                ("🏠 Tableau de Bord", "Vue d'ensemble temps réel", "home", "Accédez aux indicateurs clés"),
                ("📋 Tâches", "Gestion des tâches", "tasks", "Planifiez et suivez les tâches"),
                ("📄 Rapports", "Analyses et rapports", "reports", "Générez des rapports détaillés")
            ]
        )
        content_layout.addWidget(production_section)
        
        # Section MAINTENANCE
        maintenance_section = self.create_section(
            "🔧 MAINTENANCE",
            "#f39c12",
            [
                ("🔌 Équipements", "Parc d'équipements", "equipment", "Gérez vos équipements"),
                ("🔧 Pièces de Rechange", "Inventaire des pièces", "spare_parts", "Suivez votre stock")
            ]
        )
        content_layout.addWidget(maintenance_section)
        
        # Section PERSONNEL
        personnel_section = self.create_section(
            "👥 PERSONNEL",
            "#27ae60",
            [
                ("👤 Gestion Personnel", "Employés et équipes", "personnel", "Gérez votre personnel"),
                ("📊 Pointage", "Présences et horaires", "attendance", "Suivez les présences")
            ]
        )
        content_layout.addWidget(personnel_section)
        
        scroll_area.setWidget(content_widget)
        layout.addWidget(scroll_area)
        
        # Statistiques rapides en bas
        self.stats_widget = self.create_quick_stats()
        layout.addWidget(self.stats_widget)
    
    def create_section(self, title, color, items):
        """Crée une section avec ses éléments"""
        section_frame = QFrame()
        section_frame.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 2px solid {color};
                border-radius: 12px;
                margin: 5px;
            }}
        """)
        
        layout = QVBoxLayout(section_frame)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Titre de section
        section_title = QLabel(title)
        section_title.setStyleSheet(f"""
            QLabel {{
                font-size: 20px;
                font-weight: bold;
                color: {color};
                padding: 10px;
                background-color: {color}20;
                border-radius: 8px;
                margin-bottom: 10px;
            }}
        """)
        section_title.setAlignment(Qt.AlignCenter)
        layout.addWidget(section_title)
        
        # Grille des éléments
        items_layout = QGridLayout()
        items_layout.setSpacing(15)
        
        for i, (icon_title, description, section_id, tooltip) in enumerate(items):
            item_btn = self.create_section_item(icon_title, description, section_id, tooltip, color)
            row = i // 2
            col = i % 2
            items_layout.addWidget(item_btn, row, col)
        
        layout.addLayout(items_layout)
        return section_frame
    
    def create_section_item(self, title, description, section_id, tooltip, color):
        """Crée un élément de section cliquable"""
        item_frame = QFrame()
        item_frame.setStyleSheet(f"""
            QFrame {{
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
                min-height: 100px;
            }}
            QFrame:hover {{
                background-color: {color}10;
                border-color: {color};
                cursor: pointer;
            }}
        """)
        
        layout = QVBoxLayout(item_frame)
        layout.setSpacing(8)
        
        # Titre avec icône
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 16px;
                font-weight: bold;
                color: {color};
            }}
        """)
        layout.addWidget(title_label)
        
        # Description
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            QLabel {
                font-size: 13px;
                color: #6c757d;
                margin-bottom: 8px;
            }
        """)
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)
        
        # Bouton d'action
        action_btn = QPushButton("Accéder →")
        action_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: {color}dd;
            }}
            QPushButton:pressed {{
                background-color: {color}bb;
            }}
        """)
        action_btn.clicked.connect(lambda: self.section_requested.emit(section_id))
        layout.addWidget(action_btn)
        
        item_frame.setToolTip(tooltip)
        
        # Rendre le frame cliquable
        def mousePressEvent(event):
            if event.button() == Qt.LeftButton:
                self.section_requested.emit(section_id)
        
        item_frame.mousePressEvent = mousePressEvent
        
        return item_frame
    
    def create_quick_stats(self):
        """Crée le widget de statistiques rapides"""
        stats_frame = QFrame()
        stats_frame.setStyleSheet("""
            QFrame {
                background-color: #34495e;
                border-radius: 12px;
                padding: 15px;
                margin: 10px 0;
            }
        """)
        
        layout = QHBoxLayout(stats_frame)
        layout.setSpacing(30)
        
        # Statistiques à afficher
        self.stats_labels = {}
        stats_info = [
            ("tasks_count", "📋", "Tâches", "#3498db"),
            ("equipment_count", "🔌", "Équipements", "#f39c12"),
            ("personnel_count", "👥", "Personnel", "#27ae60"),
            ("parts_count", "🔧", "Pièces", "#e67e22")
        ]
        
        for stat_id, icon, label, color in stats_info:
            stat_widget, value_label = self.create_stat_widget(icon, label, "0", color)
            self.stats_labels[stat_id] = value_label
            layout.addWidget(stat_widget)
        
        layout.addStretch()
        
        return stats_frame
    
    def create_stat_widget(self, icon, label, value, color):
        """Crée un widget de statistique"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(5)
        
        # Icône et valeur
        icon_value = QLabel(f"{icon} {value}")
        icon_value.setStyleSheet(f"""
            QLabel {{
                font-size: 24px;
                font-weight: bold;
                color: {color};
            }}
        """)
        icon_value.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_value)
        
        # Label
        label_widget = QLabel(label)
        label_widget.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #bdc3c7;
                font-weight: bold;
            }
        """)
        label_widget.setAlignment(Qt.AlignCenter)
        layout.addWidget(label_widget)
        
        return widget, icon_value  # Retourner le widget et le label pour mise à jour
    
    def update_statistics(self):
        """Met à jour les statistiques"""
        try:
            # Compter les éléments
            tasks_count = len(self.db.get_all_tasks())
            equipment_count = len(self.db.get_all_equipment())
            personnel_count = len(self.db.get_all_persons())
            
            try:
                parts_count = len(self.db.get_all_spare_parts())
            except:
                parts_count = 0
            
            # Mettre à jour les labels
            self.stats_labels["tasks_count"].setText(f"📋 {tasks_count}")
            self.stats_labels["equipment_count"].setText(f"🔌 {equipment_count}")
            self.stats_labels["personnel_count"].setText(f"👥 {personnel_count}")
            self.stats_labels["parts_count"].setText(f"🔧 {parts_count}")
            
        except Exception as e:
            print(f"Erreur mise à jour statistiques : {str(e)}")
    
    def refresh_data(self):
        """Actualise les données (pour le système d'actualisation automatique)"""
        self.update_statistics()
