"""
Module complet de gestion des rapports pour SOTRAMINE PHOSPHATE
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                             QTableWidgetItem, QPushButton, QLabel, QFrame, 
                             QComboBox, QLineEdit, QTextEdit, QDateEdit, 
                             QDialog, QFormLayout, QMessageBox, QHeaderView,
                             QSplitter, QTabWidget, QGroupBox, QGridLayout,
                             QSpinBox, QCheckBox, QProgressBar, QListWidget,
                             QScrollArea, QFileDialog)
from PyQt5.QtCore import Qt, QDate, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QColor, QPixmap, QPainter
from utils.data_refresh_manager import RefreshableWidget, notify_data_changed
import os
from datetime import datetime, timedelta

class ReportsManagerComplete(QWidget, RefreshableWidget):
    """Gestionnaire complet des rapports"""
    
    def __init__(self, db, parent=None):
        super().__init__(parent)
        RefreshableWidget.__init__(self)
        self.db = db
        self.setup_ui()
        self.load_reports_data()
        self.enable_auto_refresh(['reports'])
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # En-tête
        header = self.create_header()
        layout.addWidget(header)
        
        # Contenu principal avec onglets
        self.main_tabs = QTabWidget()
        self.main_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #ddd;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f8f9fa;
                padding: 12px 20px;
                margin-right: 2px;
                font-weight: bold;
                min-width: 120px;
            }
            QTabBar::tab:selected {
                background-color: #9b59b6;
                color: white;
            }
        """)
        
        # Onglets
        self.dashboard_tab = self.create_dashboard_tab()
        self.main_tabs.addTab(self.dashboard_tab, "📊 Tableau de Bord")
        
        self.production_tab = self.create_production_tab()
        self.main_tabs.addTab(self.production_tab, "🏭 Production")
        
        self.maintenance_tab = self.create_maintenance_tab()
        self.main_tabs.addTab(self.maintenance_tab, "🔧 Maintenance")
        
        self.personnel_tab = self.create_personnel_tab()
        self.main_tabs.addTab(self.personnel_tab, "👥 Personnel")
        
        self.custom_tab = self.create_custom_tab()
        self.main_tabs.addTab(self.custom_tab, "📋 Personnalisés")
        
        layout.addWidget(self.main_tabs)
    
    def create_header(self):
        """Crée l'en-tête du module"""
        header = QFrame()
        header.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #9b59b6, stop:1 #8e44ad);
                padding: 20px;
                border-radius: 0;
            }
        """)
        header.setFixedHeight(80)
        
        layout = QHBoxLayout(header)
        
        # Titre
        title = QLabel("📄 RAPPORTS & ANALYSES")
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24px;
                font-weight: bold;
            }
        """)
        layout.addWidget(title)
        
        layout.addStretch()
        
        # Statistiques rapides
        stats_layout = QHBoxLayout()
        
        # Rapports générés ce mois
        reports_frame = self.create_stat_frame("📊", "Rapports", "23", "#3498db")
        stats_layout.addWidget(reports_frame)
        
        # Analyses en cours
        analysis_frame = self.create_stat_frame("🔍", "Analyses", "5", "#e67e22")
        stats_layout.addWidget(analysis_frame)
        
        # Alertes
        alerts_frame = self.create_stat_frame("⚠️", "Alertes", "2", "#e74c3c")
        stats_layout.addWidget(alerts_frame)
        
        layout.addLayout(stats_layout)
        
        # Boutons d'actions
        actions_layout = QVBoxLayout()
        
        btn_generate = QPushButton("📊 Nouveau Rapport")
        btn_generate.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 14px;
                margin-bottom: 5px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        btn_generate.clicked.connect(self.create_new_report)
        actions_layout.addWidget(btn_generate)
        
        btn_schedule = QPushButton("⏰ Planifier")
        btn_schedule.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        btn_schedule.clicked.connect(self.schedule_report)
        actions_layout.addWidget(btn_schedule)
        
        layout.addLayout(actions_layout)
        
        return header
    
    def create_stat_frame(self, icon, label, value, color):
        """Crée un cadre de statistique"""
        frame = QFrame()
        frame.setStyleSheet(f"""
            QFrame {{
                background-color: rgba(255, 255, 255, 0.15);
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                padding: 10px;
                margin: 5px;
                min-width: 80px;
            }}
        """)
        
        layout = QVBoxLayout(frame)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(5)
        
        # Icône et valeur
        icon_value = QLabel(f"{icon} {value}")
        icon_value.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 16px;
                font-weight: bold;
            }
        """)
        icon_value.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_value)
        
        # Label
        label_widget = QLabel(label)
        label_widget.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 11px;
                font-weight: bold;
            }
        """)
        label_widget.setAlignment(Qt.AlignCenter)
        layout.addWidget(label_widget)
        
        return frame
    
    def create_dashboard_tab(self):
        """Crée l'onglet tableau de bord"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # KPIs principaux
        kpis_frame = self.create_kpis_section()
        layout.addWidget(kpis_frame)
        
        # Graphiques et analyses
        charts_frame = self.create_charts_section()
        layout.addWidget(charts_frame)
        
        # Rapports récents
        recent_frame = self.create_recent_reports_section()
        layout.addWidget(recent_frame)
        
        return tab
    
    def create_kpis_section(self):
        """Crée la section des KPIs"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 20px;
                margin-bottom: 20px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        # Titre
        title = QLabel("📊 Indicateurs Clés de Performance")
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # Grille de KPIs
        kpis_grid = QGridLayout()
        
        kpis_data = [
            ("🏭", "Production", "95.2%", "Taux de production", "#27ae60"),
            ("🔧", "Maintenance", "87.5%", "Disponibilité équipements", "#f39c12"),
            ("👥", "Personnel", "96.8%", "Taux de présence", "#3498db"),
            ("💰", "Coûts", "€125,450", "Coûts mensuels", "#e74c3c"),
            ("⚡", "Efficacité", "92.1%", "Efficacité globale", "#9b59b6"),
            ("📈", "Qualité", "98.7%", "Taux de conformité", "#17a2b8")
        ]
        
        for i, (icon, title_text, value, description, color) in enumerate(kpis_data):
            row = i // 3
            col = i % 3
            
            kpi_widget = self.create_kpi_widget(icon, title_text, value, description, color)
            kpis_grid.addWidget(kpi_widget, row, col)
        
        layout.addLayout(kpis_grid)
        
        return frame
    
    def create_kpi_widget(self, icon, title, value, description, color):
        """Crée un widget KPI"""
        widget = QFrame()
        widget.setStyleSheet(f"""
            QFrame {{
                background-color: {color}15;
                border: 2px solid {color};
                border-radius: 10px;
                padding: 15px;
                margin: 5px;
                min-height: 100px;
            }}
            QFrame:hover {{
                background-color: {color}25;
            }}
        """)
        
        layout = QVBoxLayout(widget)
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(8)
        
        # Icône et titre
        header_layout = QHBoxLayout()
        
        icon_label = QLabel(icon)
        icon_label.setStyleSheet(f"""
            QLabel {{
                font-size: 24px;
                color: {color};
            }}
        """)
        header_layout.addWidget(icon_label)
        
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 14px;
                font-weight: bold;
                color: {color};
            }}
        """)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
        # Valeur
        value_label = QLabel(value)
        value_label.setStyleSheet(f"""
            QLabel {{
                font-size: 20px;
                font-weight: bold;
                color: {color};
            }}
        """)
        value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(value_label)
        
        # Description
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            QLabel {
                font-size: 11px;
                color: #6c757d;
                font-weight: bold;
            }
        """)
        desc_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(desc_label)
        
        return widget
    
    def create_charts_section(self):
        """Crée la section des graphiques"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 20px;
                margin-bottom: 20px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        # Titre
        title = QLabel("📈 Analyses Graphiques")
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # Placeholder pour graphiques
        charts_layout = QHBoxLayout()
        
        # Graphique 1 - Production
        chart1 = self.create_chart_placeholder("📊 Production Mensuelle", "#3498db")
        charts_layout.addWidget(chart1)
        
        # Graphique 2 - Maintenance
        chart2 = self.create_chart_placeholder("🔧 Interventions Maintenance", "#f39c12")
        charts_layout.addWidget(chart2)
        
        # Graphique 3 - Personnel
        chart3 = self.create_chart_placeholder("👥 Présences Personnel", "#27ae60")
        charts_layout.addWidget(chart3)
        
        layout.addLayout(charts_layout)
        
        return frame
    
    def create_chart_placeholder(self, title, color):
        """Crée un placeholder pour graphique"""
        widget = QFrame()
        widget.setStyleSheet(f"""
            QFrame {{
                background-color: #f8f9fa;
                border: 1px solid {color};
                border-radius: 8px;
                padding: 15px;
                margin: 5px;
                min-height: 200px;
            }}
        """)
        
        layout = QVBoxLayout(widget)
        layout.setAlignment(Qt.AlignCenter)
        
        # Titre
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 14px;
                font-weight: bold;
                color: {color};
                margin-bottom: 10px;
            }}
        """)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # Placeholder graphique
        chart_label = QLabel("📊\n\nGraphique\ninteractif")
        chart_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #6c757d;
                text-align: center;
            }
        """)
        chart_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(chart_label)
        
        # Bouton pour voir détails
        btn_details = QPushButton("Voir détails")
        btn_details.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {color}dd;
            }}
        """)
        btn_details.clicked.connect(lambda: self.show_chart_details(title))
        layout.addWidget(btn_details)
        
        return widget
    
    def create_recent_reports_section(self):
        """Crée la section des rapports récents"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 20px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        # Titre
        title = QLabel("📋 Rapports Récents")
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # Table des rapports récents
        self.recent_reports_table = QTableWidget()
        self.recent_reports_table.setColumnCount(6)
        self.recent_reports_table.setHorizontalHeaderLabels([
            "Nom", "Type", "Date", "Statut", "Taille", "Actions"
        ])
        
        self.recent_reports_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #ddd;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eee;
            }
            QTableWidget::item:selected {
                background-color: #9b59b6;
                color: white;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)
        
        self.recent_reports_table.setAlternatingRowColors(True)
        self.recent_reports_table.horizontalHeader().setStretchLastSection(True)
        self.recent_reports_table.setMaximumHeight(200)
        
        # Données d'exemple
        reports_data = [
            ("Rapport Production Août", "Production", "2025-08-09", "Généré", "2.3 MB"),
            ("Analyse Maintenance Q3", "Maintenance", "2025-08-08", "En cours", "1.8 MB"),
            ("Présences Personnel", "Personnel", "2025-08-07", "Généré", "856 KB"),
            ("KPIs Mensuels", "Dashboard", "2025-08-06", "Généré", "1.2 MB")
        ]
        
        self.recent_reports_table.setRowCount(len(reports_data))
        for row, data in enumerate(reports_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(value)
                if col == 3:  # Colonne statut
                    if value == "Généré":
                        item.setBackground(QColor("#d4edda"))
                    elif value == "En cours":
                        item.setBackground(QColor("#fff3cd"))
                self.recent_reports_table.setItem(row, col, item)
        
        layout.addWidget(self.recent_reports_table)
        
        return frame
    
    def create_production_tab(self):
        """Crée l'onglet rapports de production"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Titre
        title = QLabel("🏭 Rapports de Production")
        title.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
        # Types de rapports de production
        reports_grid = QGridLayout()
        
        production_reports = [
            ("📊 Production Quotidienne", "Rapport détaillé de la production du jour", "#3498db", self.daily_production_report),
            ("📈 Tendances Production", "Analyse des tendances sur plusieurs périodes", "#27ae60", self.production_trends),
            ("⚡ Efficacité Machines", "Performance et efficacité des équipements", "#f39c12", self.machine_efficiency),
            ("📋 Qualité Production", "Contrôle qualité et taux de conformité", "#e74c3c", self.quality_report),
            ("💰 Coûts de Production", "Analyse des coûts par produit/ligne", "#9b59b6", self.production_costs),
            ("🎯 Objectifs vs Réalisé", "Comparaison objectifs et réalisations", "#17a2b8", self.objectives_report)
        ]
        
        for i, (title_text, description, color, action) in enumerate(production_reports):
            row = i // 2
            col = i % 2
            
            report_card = self.create_report_card(title_text, description, color, action)
            reports_grid.addWidget(report_card, row, col)
        
        layout.addLayout(reports_grid)
        layout.addStretch()
        
        return tab
    
    def create_maintenance_tab(self):
        """Crée l'onglet rapports de maintenance"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Titre
        title = QLabel("🔧 Rapports de Maintenance")
        title.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
        # Types de rapports de maintenance
        reports_grid = QGridLayout()
        
        maintenance_reports = [
            ("🔧 Interventions Mensuelles", "Détail de toutes les interventions", "#e67e22", self.monthly_interventions),
            ("📅 Maintenance Préventive", "Suivi de la maintenance préventive", "#3498db", self.preventive_maintenance),
            ("⚠️ Pannes et Incidents", "Analyse des pannes et temps d'arrêt", "#e74c3c", self.breakdowns_report),
            ("💰 Coûts Maintenance", "Analyse des coûts de maintenance", "#27ae60", self.maintenance_costs),
            ("📊 Performance Équipements", "Disponibilité et performance", "#9b59b6", self.equipment_performance),
            ("🔧 Stock Pièces", "État du stock de pièces de rechange", "#17a2b8", self.spare_parts_stock)
        ]
        
        for i, (title_text, description, color, action) in enumerate(maintenance_reports):
            row = i // 2
            col = i % 2
            
            report_card = self.create_report_card(title_text, description, color, action)
            reports_grid.addWidget(report_card, row, col)
        
        layout.addLayout(reports_grid)
        layout.addStretch()
        
        return tab
    
    def create_personnel_tab(self):
        """Crée l'onglet rapports de personnel"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Titre
        title = QLabel("👥 Rapports de Personnel")
        title.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
        # Types de rapports de personnel
        reports_grid = QGridLayout()
        
        personnel_reports = [
            ("📊 Présences Mensuelles", "Rapport détaillé des présences", "#27ae60", self.monthly_attendance),
            ("⏰ Heures Travaillées", "Analyse des heures par employé", "#3498db", self.worked_hours),
            ("❌ Absences et Congés", "Suivi des absences et congés", "#e74c3c", self.absences_report),
            ("📈 Performance Équipes", "Évaluation des performances", "#9b59b6", self.team_performance),
            ("🎓 Formations", "Suivi des formations du personnel", "#f39c12", self.training_report),
            ("💰 Masse Salariale", "Analyse des coûts de personnel", "#17a2b8", self.payroll_report)
        ]
        
        for i, (title_text, description, color, action) in enumerate(personnel_reports):
            row = i // 2
            col = i % 2
            
            report_card = self.create_report_card(title_text, description, color, action)
            reports_grid.addWidget(report_card, row, col)
        
        layout.addLayout(reports_grid)
        layout.addStretch()
        
        return tab
    
    def create_custom_tab(self):
        """Crée l'onglet rapports personnalisés"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Titre
        title = QLabel("📋 Rapports Personnalisés")
        title.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title)
        
        # Splitter pour créateur et liste
        splitter = QSplitter(Qt.Horizontal)
        
        # Panel de création
        creator_panel = self.create_report_creator()
        splitter.addWidget(creator_panel)
        
        # Panel des rapports sauvegardés
        saved_panel = self.create_saved_reports_panel()
        splitter.addWidget(saved_panel)
        
        splitter.setSizes([600, 400])
        layout.addWidget(splitter)
        
        return tab
    
    def create_report_creator(self):
        """Crée le créateur de rapports"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Titre
        title = QLabel("🛠️ Créateur de Rapport")
        title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # Formulaire de création
        form_layout = QFormLayout()
        
        self.report_name = QLineEdit()
        self.report_name.setPlaceholderText("Nom du rapport...")
        form_layout.addRow("Nom:", self.report_name)
        
        self.report_type = QComboBox()
        self.report_type.addItems(["Production", "Maintenance", "Personnel", "Financier", "Qualité"])
        form_layout.addRow("Type:", self.report_type)
        
        self.date_range = QComboBox()
        self.date_range.addItems(["Aujourd'hui", "Cette semaine", "Ce mois", "Ce trimestre", "Cette année", "Personnalisé"])
        form_layout.addRow("Période:", self.date_range)
        
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.setCalendarPopup(True)
        form_layout.addRow("Date début:", self.start_date)
        
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setCalendarPopup(True)
        form_layout.addRow("Date fin:", self.end_date)
        
        self.output_format = QComboBox()
        self.output_format.addItems(["Excel (.xlsx)", "PDF", "CSV", "HTML"])
        form_layout.addRow("Format:", self.output_format)
        
        layout.addLayout(form_layout)
        
        # Options avancées
        options_group = QGroupBox("Options Avancées")
        options_layout = QVBoxLayout(options_group)
        
        self.include_charts = QCheckBox("Inclure des graphiques")
        self.include_charts.setChecked(True)
        options_layout.addWidget(self.include_charts)
        
        self.include_summary = QCheckBox("Inclure un résumé exécutif")
        self.include_summary.setChecked(True)
        options_layout.addWidget(self.include_summary)
        
        self.auto_schedule = QCheckBox("Planifier automatiquement")
        options_layout.addWidget(self.auto_schedule)
        
        layout.addWidget(options_group)
        
        # Boutons d'actions
        buttons_layout = QHBoxLayout()
        
        btn_preview = QPushButton("👁️ Aperçu")
        btn_preview.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        btn_preview.clicked.connect(self.preview_report)
        buttons_layout.addWidget(btn_preview)
        
        btn_generate = QPushButton("📊 Générer")
        btn_generate.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        btn_generate.clicked.connect(self.generate_custom_report)
        buttons_layout.addWidget(btn_generate)
        
        btn_save_template = QPushButton("💾 Sauver Modèle")
        btn_save_template.setStyleSheet("""
            QPushButton {
                background-color: #6f42c1;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #5a32a3;
            }
        """)
        btn_save_template.clicked.connect(self.save_template)
        buttons_layout.addWidget(btn_save_template)
        
        layout.addLayout(buttons_layout)
        layout.addStretch()
        
        return panel
    
    def create_saved_reports_panel(self):
        """Crée le panel des rapports sauvegardés"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Titre
        title = QLabel("📁 Rapports Sauvegardés")
        title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # Liste des rapports
        self.saved_reports_list = QListWidget()
        self.saved_reports_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #ddd;
                background-color: white;
                padding: 5px;
            }
            QListWidget::item {
                padding: 10px;
                border-bottom: 1px solid #eee;
            }
            QListWidget::item:selected {
                background-color: #9b59b6;
                color: white;
            }
        """)
        
        # Ajouter quelques rapports d'exemple
        saved_reports = [
            "📊 Rapport Production Mensuel",
            "🔧 Analyse Maintenance Q3",
            "👥 Présences Personnel Août",
            "💰 Coûts Opérationnels",
            "📈 KPIs Trimestriels"
        ]
        
        for report in saved_reports:
            self.saved_reports_list.addItem(report)
        
        layout.addWidget(self.saved_reports_list)
        
        # Boutons d'actions pour rapports sauvegardés
        saved_buttons_layout = QHBoxLayout()
        
        btn_open = QPushButton("📂 Ouvrir")
        btn_open.clicked.connect(self.open_saved_report)
        saved_buttons_layout.addWidget(btn_open)
        
        btn_duplicate = QPushButton("📋 Dupliquer")
        btn_duplicate.clicked.connect(self.duplicate_report)
        saved_buttons_layout.addWidget(btn_duplicate)
        
        btn_delete = QPushButton("🗑️ Supprimer")
        btn_delete.clicked.connect(self.delete_saved_report)
        saved_buttons_layout.addWidget(btn_delete)
        
        layout.addLayout(saved_buttons_layout)
        
        return panel
    
    def create_report_card(self, title, description, color, action):
        """Crée une carte de rapport"""
        frame = QFrame()
        frame.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 1px solid #dee2e6;
                border-left: 4px solid {color};
                border-radius: 8px;
                padding: 20px;
                margin: 10px;
            }}
            QFrame:hover {{
                background-color: #f8f9fa;
                border-left: 6px solid {color};
            }}
        """)
        
        layout = QVBoxLayout(frame)
        
        # Titre
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 16px;
                font-weight: bold;
                color: {color};
                margin-bottom: 8px;
            }}
        """)
        layout.addWidget(title_label)
        
        # Description
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #6c757d;
                margin-bottom: 15px;
            }
        """)
        desc_label.setWordWrap(True)
        layout.addWidget(desc_label)
        
        # Boutons
        buttons_layout = QHBoxLayout()
        
        btn_generate = QPushButton("📊 Générer")
        btn_generate.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 12px;
                font-weight: bold;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: {color}dd;
            }}
        """)
        btn_generate.clicked.connect(action)
        buttons_layout.addWidget(btn_generate)
        
        btn_schedule = QPushButton("⏰")
        btn_schedule.setStyleSheet(f"""
            QPushButton {{
                background-color: {color}aa;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px;
                font-weight: bold;
                max-width: 30px;
            }}
            QPushButton:hover {{
                background-color: {color};
            }}
        """)
        btn_schedule.clicked.connect(lambda: self.schedule_specific_report(title))
        buttons_layout.addWidget(btn_schedule)
        
        layout.addLayout(buttons_layout)
        
        return frame
    
    def load_reports_data(self):
        """Charge les données des rapports"""
        # Simuler le chargement des données
        pass
    
    # Méthodes d'actions générales
    def create_new_report(self):
        """Crée un nouveau rapport"""
        dialog = ReportWizard(self)
        dialog.exec_()
    
    def schedule_report(self):
        """Planifie un rapport"""
        QMessageBox.information(self, "Planification", 
                               "⏰ Planification de rapport\n\n"
                               "Cette fonctionnalité permettra de :\n"
                               "• Définir une récurrence\n"
                               "• Choisir les destinataires\n"
                               "• Configurer l'envoi automatique\n"
                               "• Gérer les notifications")
    
    def show_chart_details(self, chart_title):
        """Affiche les détails d'un graphique"""
        QMessageBox.information(self, "Détails Graphique", 
                               f"📊 {chart_title}\n\n"
                               "Cette vue détaillée inclurait :\n"
                               "• Graphique interactif\n"
                               "• Données sous-jacentes\n"
                               "• Options de filtrage\n"
                               "• Export des données")
    
    # Méthodes de rapports de production
    def daily_production_report(self):
        """Rapport de production quotidienne"""
        self.generate_report("Production Quotidienne", "production")
    
    def production_trends(self):
        """Analyse des tendances de production"""
        self.generate_report("Tendances Production", "production")
    
    def machine_efficiency(self):
        """Rapport d'efficacité des machines"""
        self.generate_report("Efficacité Machines", "production")
    
    def quality_report(self):
        """Rapport de qualité"""
        self.generate_report("Qualité Production", "production")
    
    def production_costs(self):
        """Rapport des coûts de production"""
        self.generate_report("Coûts Production", "production")
    
    def objectives_report(self):
        """Rapport objectifs vs réalisé"""
        self.generate_report("Objectifs vs Réalisé", "production")
    
    # Méthodes de rapports de maintenance
    def monthly_interventions(self):
        """Rapport des interventions mensuelles"""
        self.generate_report("Interventions Mensuelles", "maintenance")
    
    def preventive_maintenance(self):
        """Rapport de maintenance préventive"""
        self.generate_report("Maintenance Préventive", "maintenance")
    
    def breakdowns_report(self):
        """Rapport des pannes"""
        self.generate_report("Pannes et Incidents", "maintenance")
    
    def maintenance_costs(self):
        """Rapport des coûts de maintenance"""
        self.generate_report("Coûts Maintenance", "maintenance")
    
    def equipment_performance(self):
        """Rapport de performance des équipements"""
        self.generate_report("Performance Équipements", "maintenance")
    
    def spare_parts_stock(self):
        """Rapport du stock de pièces"""
        self.generate_report("Stock Pièces", "maintenance")
    
    # Méthodes de rapports de personnel
    def monthly_attendance(self):
        """Rapport des présences mensuelles"""
        self.generate_report("Présences Mensuelles", "personnel")
    
    def worked_hours(self):
        """Rapport des heures travaillées"""
        self.generate_report("Heures Travaillées", "personnel")
    
    def absences_report(self):
        """Rapport des absences"""
        self.generate_report("Absences et Congés", "personnel")
    
    def team_performance(self):
        """Rapport de performance des équipes"""
        self.generate_report("Performance Équipes", "personnel")
    
    def training_report(self):
        """Rapport des formations"""
        self.generate_report("Formations", "personnel")
    
    def payroll_report(self):
        """Rapport de masse salariale"""
        self.generate_report("Masse Salariale", "personnel")
    
    # Méthodes de rapports personnalisés
    def preview_report(self):
        """Aperçu du rapport"""
        if not self.report_name.text().strip():
            QMessageBox.warning(self, "Erreur", "Le nom du rapport est obligatoire !")
            return
        
        QMessageBox.information(self, "Aperçu", 
                               f"👁️ Aperçu du rapport\n\n"
                               f"Nom : {self.report_name.text()}\n"
                               f"Type : {self.report_type.currentText()}\n"
                               f"Période : {self.date_range.currentText()}\n"
                               f"Format : {self.output_format.currentText()}")
    
    def generate_custom_report(self):
        """Génère un rapport personnalisé"""
        if not self.report_name.text().strip():
            QMessageBox.warning(self, "Erreur", "Le nom du rapport est obligatoire !")
            return
        
        self.generate_report(self.report_name.text(), self.report_type.currentText().lower())
    
    def save_template(self):
        """Sauvegarde un modèle de rapport"""
        if not self.report_name.text().strip():
            QMessageBox.warning(self, "Erreur", "Le nom du rapport est obligatoire !")
            return
        
        template_name = f"Modèle - {self.report_name.text()}"
        self.saved_reports_list.addItem(template_name)
        QMessageBox.information(self, "Succès", f"Modèle '{template_name}' sauvegardé !")
    
    def open_saved_report(self):
        """Ouvre un rapport sauvegardé"""
        current_item = self.saved_reports_list.currentItem()
        if current_item:
            QMessageBox.information(self, "Ouvrir Rapport", 
                                   f"📂 Ouverture du rapport\n\n{current_item.text()}")
    
    def duplicate_report(self):
        """Duplique un rapport"""
        current_item = self.saved_reports_list.currentItem()
        if current_item:
            new_name = f"Copie - {current_item.text()}"
            self.saved_reports_list.addItem(new_name)
            QMessageBox.information(self, "Succès", f"Rapport dupliqué : {new_name}")
    
    def delete_saved_report(self):
        """Supprime un rapport sauvegardé"""
        current_row = self.saved_reports_list.currentRow()
        if current_row >= 0:
            item = self.saved_reports_list.takeItem(current_row)
            QMessageBox.information(self, "Succès", f"Rapport '{item.text()}' supprimé !")
    
    def schedule_specific_report(self, report_name):
        """Planifie un rapport spécifique"""
        QMessageBox.information(self, "Planification", 
                               f"⏰ Planification du rapport\n\n{report_name}\n\n"
                               "Configuration de la planification automatique...")
    
    def generate_report(self, report_name, report_type):
        """Génère un rapport"""
        try:
            # Simuler la génération
            QMessageBox.information(self, "Génération en cours", 
                                   f"📊 Génération du rapport\n\n"
                                   f"Nom : {report_name}\n"
                                   f"Type : {report_type}\n\n"
                                   "Le rapport sera disponible dans quelques instants...")
            
            # Ajouter à la liste des rapports récents
            current_date = datetime.now().strftime("%Y-%m-%d")
            new_row = self.recent_reports_table.rowCount()
            self.recent_reports_table.insertRow(new_row)
            
            self.recent_reports_table.setItem(new_row, 0, QTableWidgetItem(report_name))
            self.recent_reports_table.setItem(new_row, 1, QTableWidgetItem(report_type.title()))
            self.recent_reports_table.setItem(new_row, 2, QTableWidgetItem(current_date))
            self.recent_reports_table.setItem(new_row, 3, QTableWidgetItem("Généré"))
            self.recent_reports_table.setItem(new_row, 4, QTableWidgetItem("1.5 MB"))
            
            notify_data_changed('reports')
            
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la génération : {str(e)}")
    
    def refresh_data(self):
        """Actualise les données"""
        self.load_reports_data()


class ReportWizard(QDialog):
    """Assistant de création de rapport"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """Configure l'interface de l'assistant"""
        self.setWindowTitle("Assistant de Création de Rapport")
        self.setFixedSize(600, 500)
        
        layout = QVBoxLayout(self)
        
        # Titre
        title = QLabel("🧙‍♂️ Assistant de Création de Rapport")
        title.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 20px;
                text-align: center;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Étapes
        steps_layout = QVBoxLayout()
        
        steps = [
            "1️⃣ Sélectionnez le type de rapport",
            "2️⃣ Choisissez la période d'analyse",
            "3️⃣ Définissez les paramètres",
            "4️⃣ Configurez le format de sortie",
            "5️⃣ Générez votre rapport"
        ]
        
        for step in steps:
            step_label = QLabel(step)
            step_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    color: #495057;
                    padding: 10px;
                    background-color: #f8f9fa;
                    border-left: 4px solid #9b59b6;
                    margin: 5px 0;
                }
            """)
            steps_layout.addWidget(step_label)
        
        layout.addLayout(steps_layout)
        
        # Message
        message = QLabel("Cet assistant vous guidera pas à pas dans la création de votre rapport personnalisé.")
        message.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #6c757d;
                text-align: center;
                margin: 20px 0;
            }
        """)
        message.setAlignment(Qt.AlignCenter)
        message.setWordWrap(True)
        layout.addWidget(message)
        
        # Boutons
        buttons_layout = QHBoxLayout()
        
        btn_start = QPushButton("🚀 Commencer")
        btn_start.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        btn_start.clicked.connect(self.start_wizard)
        buttons_layout.addWidget(btn_start)
        
        btn_cancel = QPushButton("❌ Annuler")
        btn_cancel.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        btn_cancel.clicked.connect(self.reject)
        buttons_layout.addWidget(btn_cancel)
        
        layout.addLayout(buttons_layout)
    
    def start_wizard(self):
        """Démarre l'assistant"""
        QMessageBox.information(self, "Assistant", 
                               "🧙‍♂️ Lancement de l'assistant\n\n"
                               "L'assistant vous guidera maintenant à travers "
                               "chaque étape de création de votre rapport.")
        self.accept()
