"""
Gestionnaire de cache intelligent pour l'application SOTRAMINE PHOSPHATE
"""

import time
import threading
import pickle
import hashlib
from collections import OrderedDict
from datetime import datetime, timedelta
from pathlib import Path
from config import APP_CONFIG, DATA_DIR
from utils.logger import get_logger

logger = get_logger("CACHE")

class LRUCache:
    """Cache LRU (Least Recently Used) thread-safe"""
    
    def __init__(self, max_size=1000, ttl=300):
        self.max_size = max_size
        self.ttl = ttl  # Time to live en secondes
        self.cache = OrderedDict()
        self.timestamps = {}
        self.lock = threading.RLock()
        self.hits = 0
        self.misses = 0
    
    def get(self, key):
        """Récupère une valeur du cache"""
        with self.lock:
            if key not in self.cache:
                self.misses += 1
                return None
            
            # Vérifier l'expiration
            if self._is_expired(key):
                self._remove(key)
                self.misses += 1
                return None
            
            # Déplacer vers la fin (plus récemment utilisé)
            value = self.cache.pop(key)
            self.cache[key] = value
            self.hits += 1
            return value
    
    def set(self, key, value):
        """Met une valeur en cache"""
        with self.lock:
            # Supprimer si déjà présent
            if key in self.cache:
                self.cache.pop(key)
            
            # Ajouter la nouvelle valeur
            self.cache[key] = value
            self.timestamps[key] = time.time()
            
            # Supprimer les anciens éléments si nécessaire
            while len(self.cache) > self.max_size:
                oldest_key = next(iter(self.cache))
                self._remove(oldest_key)
    
    def _remove(self, key):
        """Supprime une clé du cache"""
        if key in self.cache:
            del self.cache[key]
        if key in self.timestamps:
            del self.timestamps[key]
    
    def _is_expired(self, key):
        """Vérifie si une clé a expiré"""
        if key not in self.timestamps:
            return True
        return time.time() - self.timestamps[key] > self.ttl
    
    def clear(self):
        """Vide le cache"""
        with self.lock:
            self.cache.clear()
            self.timestamps.clear()
            self.hits = 0
            self.misses = 0
    
    def cleanup_expired(self):
        """Nettoie les entrées expirées"""
        with self.lock:
            expired_keys = [
                key for key in self.timestamps
                if self._is_expired(key)
            ]
            for key in expired_keys:
                self._remove(key)
            
            if expired_keys:
                logger.debug(f"Nettoyage du cache: {len(expired_keys)} entrées expirées supprimées")
    
    def get_stats(self):
        """Retourne les statistiques du cache"""
        with self.lock:
            total_requests = self.hits + self.misses
            hit_rate = (self.hits / total_requests * 100) if total_requests > 0 else 0
            
            return {
                'size': len(self.cache),
                'max_size': self.max_size,
                'hits': self.hits,
                'misses': self.misses,
                'hit_rate': hit_rate,
                'ttl': self.ttl
            }

class PersistentCache:
    """Cache persistant sur disque"""
    
    def __init__(self, cache_dir=None, max_file_size=10*1024*1024):  # 10MB par défaut
        self.cache_dir = Path(cache_dir) if cache_dir else DATA_DIR / "cache"
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.max_file_size = max_file_size
        self.lock = threading.RLock()
    
    def _get_cache_path(self, key):
        """Génère le chemin de fichier pour une clé"""
        # Hasher la clé pour éviter les problèmes de noms de fichiers
        key_hash = hashlib.md5(str(key).encode()).hexdigest()
        return self.cache_dir / f"{key_hash}.cache"
    
    def get(self, key):
        """Récupère une valeur du cache persistant"""
        cache_path = self._get_cache_path(key)
        
        with self.lock:
            if not cache_path.exists():
                return None
            
            try:
                # Vérifier la taille du fichier
                if cache_path.stat().st_size > self.max_file_size:
                    logger.warning(f"Fichier cache trop volumineux supprimé: {cache_path}")
                    cache_path.unlink()
                    return None
                
                with open(cache_path, 'rb') as f:
                    data = pickle.load(f)
                
                # Vérifier l'expiration
                if 'expires_at' in data and datetime.now() > data['expires_at']:
                    cache_path.unlink()
                    return None
                
                return data['value']
                
            except Exception as e:
                logger.error(f"Erreur lors de la lecture du cache {key}: {e}")
                # Supprimer le fichier corrompu
                try:
                    cache_path.unlink()
                except:
                    pass
                return None
    
    def set(self, key, value, ttl=3600):
        """Met une valeur en cache persistant"""
        cache_path = self._get_cache_path(key)
        
        with self.lock:
            try:
                data = {
                    'value': value,
                    'created_at': datetime.now(),
                    'expires_at': datetime.now() + timedelta(seconds=ttl)
                }
                
                with open(cache_path, 'wb') as f:
                    pickle.dump(data, f)
                
                logger.debug(f"Valeur mise en cache persistant: {key}")
                
            except Exception as e:
                logger.error(f"Erreur lors de l'écriture du cache {key}: {e}")
    
    def clear(self):
        """Vide le cache persistant"""
        with self.lock:
            try:
                for cache_file in self.cache_dir.glob("*.cache"):
                    cache_file.unlink()
                logger.info("Cache persistant vidé")
            except Exception as e:
                logger.error(f"Erreur lors du vidage du cache: {e}")
    
    def cleanup_expired(self):
        """Nettoie les fichiers de cache expirés"""
        with self.lock:
            cleaned_count = 0
            for cache_file in self.cache_dir.glob("*.cache"):
                try:
                    with open(cache_file, 'rb') as f:
                        data = pickle.load(f)
                    
                    if 'expires_at' in data and datetime.now() > data['expires_at']:
                        cache_file.unlink()
                        cleaned_count += 1
                        
                except Exception:
                    # Fichier corrompu, le supprimer
                    cache_file.unlink()
                    cleaned_count += 1
            
            if cleaned_count > 0:
                logger.info(f"Nettoyage du cache persistant: {cleaned_count} fichiers supprimés")

class SmartCacheManager:
    """Gestionnaire de cache intelligent combinant mémoire et disque"""
    
    def __init__(self):
        self.memory_cache = LRUCache(
            max_size=APP_CONFIG.get('cache_size', 1000),
            ttl=APP_CONFIG.get('cache_ttl', 300)
        )
        self.persistent_cache = PersistentCache()
        self.cleanup_thread = None
        self.running = True
        
        # Démarrer le thread de nettoyage
        self._start_cleanup_thread()
    
    def get(self, key, use_persistent=True):
        """Récupère une valeur du cache (mémoire puis disque)"""
        # Essayer d'abord le cache mémoire
        value = self.memory_cache.get(key)
        if value is not None:
            return value
        
        # Essayer le cache persistant si activé
        if use_persistent:
            value = self.persistent_cache.get(key)
            if value is not None:
                # Remettre en cache mémoire
                self.memory_cache.set(key, value)
                return value
        
        return None
    
    def set(self, key, value, ttl=None, persist=False):
        """Met une valeur en cache"""
        # Cache mémoire
        if ttl is not None:
            # Créer un cache temporaire avec TTL spécifique
            temp_cache = LRUCache(ttl=ttl)
            temp_cache.set(key, value)
        else:
            self.memory_cache.set(key, value)
        
        # Cache persistant si demandé
        if persist:
            cache_ttl = ttl or APP_CONFIG.get('cache_ttl', 300)
            self.persistent_cache.set(key, value, cache_ttl)
    
    def invalidate(self, key_pattern=None):
        """Invalide le cache selon un pattern"""
        if key_pattern is None:
            self.memory_cache.clear()
            self.persistent_cache.clear()
        else:
            # Invalider les clés correspondant au pattern
            with self.memory_cache.lock:
                keys_to_remove = [
                    key for key in self.memory_cache.cache.keys()
                    if key_pattern in str(key)
                ]
                for key in keys_to_remove:
                    self.memory_cache._remove(key)
    
    def get_stats(self):
        """Retourne les statistiques complètes du cache"""
        memory_stats = self.memory_cache.get_stats()
        
        # Statistiques du cache persistant
        persistent_files = len(list(self.persistent_cache.cache_dir.glob("*.cache")))
        
        return {
            'memory': memory_stats,
            'persistent_files': persistent_files,
            'cache_dir': str(self.persistent_cache.cache_dir)
        }
    
    def _start_cleanup_thread(self):
        """Démarre le thread de nettoyage automatique"""
        def cleanup_worker():
            while self.running:
                try:
                    time.sleep(300)  # Nettoyer toutes les 5 minutes
                    if self.running:
                        self.memory_cache.cleanup_expired()
                        self.persistent_cache.cleanup_expired()
                except Exception as e:
                    logger.error(f"Erreur dans le thread de nettoyage: {e}")
        
        self.cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        self.cleanup_thread.start()
        logger.info("Thread de nettoyage du cache démarré")
    
    def shutdown(self):
        """Arrête le gestionnaire de cache"""
        self.running = False
        if self.cleanup_thread and self.cleanup_thread.is_alive():
            self.cleanup_thread.join(timeout=1)
        logger.info("Gestionnaire de cache arrêté")

# Instance globale du gestionnaire de cache
cache_manager = SmartCacheManager()

def cached(ttl=300, persist=False, key_func=None):
    """
    Décorateur pour mettre en cache le résultat d'une fonction
    
    Args:
        ttl: Durée de vie du cache en secondes
        persist: Si True, utilise le cache persistant
        key_func: Fonction pour générer la clé de cache
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Générer la clé de cache
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__module__}.{func.__name__}_{hash((args, tuple(sorted(kwargs.items()))))}"
            
            # Essayer de récupérer du cache
            result = cache_manager.get(cache_key, use_persistent=persist)
            if result is not None:
                return result
            
            # Exécuter la fonction et mettre en cache
            result = func(*args, **kwargs)
            cache_manager.set(cache_key, result, ttl=ttl, persist=persist)
            
            return result
        
        return wrapper
    return decorator

# Fonction utilitaire pour invalider le cache
def invalidate_cache(pattern=None):
    """Invalide le cache selon un pattern"""
    cache_manager.invalidate(pattern)

# Fonction pour obtenir les statistiques du cache
def get_cache_stats():
    """Retourne les statistiques du cache"""
    return cache_manager.get_stats()