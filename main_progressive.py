#!/usr/bin/env python3
"""
Application Todo List Progressive
Ajoute le menu de maintenance étape par étape
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                             QLabel, QPushButton, QTabWidget, QFrame, QHBoxLayout)
from PyQt5.QtCore import Qt

def main():
    """Fonction principale progressive"""
    try:
        # Créer l'application PyQt5
        app = QApplication(sys.argv)
        app.setApplicationName("Todo List - Menu de Maintenance Progressif")
        
        # Créer la fenêtre principale
        main_window = QMainWindow()
        main_window.setWindowTitle("Todo List - Menu de Maintenance Progressif")
        main_window.setGeometry(100, 100, 1200, 800)
        
        # Widget central
        central_widget = QWidget()
        main_window.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # En-tête
        header = QLabel("🔧 Menu de Maintenance - Version Progressive")
        header.setStyleSheet("font-size: 24px; font-weight: bold; margin: 20px; color: #2c3e50;")
        header.setAlignment(Qt.AlignCenter)
        layout.addWidget(header)
        
        # Tableau de bord simple
        dashboard_frame = QFrame()
        dashboard_frame.setStyleSheet("QFrame { background-color: #ecf0f1; border-radius: 10px; padding: 20px; }")
        dashboard_layout = QVBoxLayout(dashboard_frame)
        
        dashboard_title = QLabel("📊 Tableau de Bord")
        dashboard_title.setStyleSheet("font-size: 18px; font-weight: bold; color: #34495e; margin-bottom: 15px;")
        dashboard_layout.addWidget(dashboard_title)
        
        # KPIs simples
        kpi_frame = QFrame()
        kpi_frame.setStyleSheet("QFrame { background-color: white; border-radius: 8px; padding: 15px; }")
        kpi_layout = QVBoxLayout(kpi_frame)
        
        kpi_title = QLabel("📈 Indicateurs Clés")
        kpi_title.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; margin-bottom: 10px;")
        kpi_layout.addWidget(kpi_title)
        
        kpi_info = QLabel("🔴 Maintenances urgentes: 3 | ✅ Terminées: 12 | 💰 Coût mensuel: 2,450 €")
        kpi_info.setStyleSheet("font-size: 14px; color: #7f8c8d;")
        kpi_layout.addWidget(kpi_info)
        
        dashboard_layout.addWidget(kpi_frame)
        
        # Actions rapides
        actions_frame = QFrame()
        actions_frame.setStyleSheet("QFrame { background-color: white; border-radius: 8px; padding: 15px; margin-top: 15px; }")
        actions_layout = QVBoxLayout(actions_frame)
        
        actions_title = QLabel("⚡ Actions Rapides")
        actions_title.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; margin-bottom: 10px;")
        actions_layout.addWidget(actions_title)
        
        actions_buttons = QFrame()
        actions_buttons_layout = QHBoxLayout(actions_buttons)
        
        btn_new_intervention = QPushButton("➕ Nouvelle Intervention")
        btn_new_intervention.setStyleSheet("QPushButton { background-color: #3498db; color: white; border: none; padding: 10px 15px; border-radius: 5px; font-weight: bold; } QPushButton:hover { background-color: #2980b9; }")
        btn_new_intervention.clicked.connect(lambda: print("✅ Nouvelle intervention créée"))
        
        btn_new_work_order = QPushButton("📋 Nouveau Bon de Travail")
        btn_new_work_order.setStyleSheet("QPushButton { background-color: #e74c3c; color: white; border: none; padding: 10px 15px; border-radius: 5px; font-weight: bold; } QPushButton:hover { background-color: #c0392b; }")
        btn_new_work_order.clicked.connect(lambda: print("✅ Nouveau bon de travail créé"))
        
        btn_schedule = QPushButton("📅 Planifier Maintenance")
        btn_schedule.setStyleSheet("QPushButton { background-color: #27ae60; color: white; border: none; padding: 10px 15px; border-radius: 5px; font-weight: bold; } QPushButton:hover { background-color: #229954; }")
        btn_schedule.clicked.connect(lambda: print("✅ Maintenance planifiée"))
        
        actions_buttons_layout.addWidget(btn_new_intervention)
        actions_buttons_layout.addWidget(btn_new_work_order)
        actions_buttons_layout.addWidget(btn_schedule)
        
        actions_layout.addWidget(actions_buttons)
        dashboard_layout.addWidget(actions_frame)
        
        layout.addWidget(dashboard_frame)
        
        # Onglets de maintenance
        tabs_frame = QFrame()
        tabs_frame.setStyleSheet("QFrame { background-color: white; border-radius: 10px; margin-top: 20px; }")
        tabs_layout = QVBoxLayout(tabs_frame)
        
        tabs_title = QLabel("📑 Modules de Maintenance")
        tabs_title.setStyleSheet("font-size: 18px; font-weight: bold; color: #34495e; margin: 20px;")
        tabs_layout.addWidget(tabs_title)
        
        # Onglets
        maintenance_tabs = QTabWidget()
        maintenance_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dcdde1;
                background-color: white;
                border-radius: 8px;
            }
            QTabBar::tab {
                background-color: #f8f9fa;
                border: 1px solid #dcdde1;
                padding: 12px 20px;
                margin-right: 2px;
                font-weight: bold;
                min-width: 150px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
                border-bottom: 2px solid #2980b9;
            }
            QTabBar::tab:hover {
                background-color: #e3f2fd;
                color: #1976d2;
            }
        """)
        
        # Onglet 1: Interventions
        interventions_tab = QWidget()
        interventions_layout = QVBoxLayout(interventions_tab)
        interventions_title = QLabel("🔧 Gestion des Interventions")
        interventions_title.setStyleSheet("font-size: 20px; font-weight: bold; color: #2c3e50; margin: 20px;")
        interventions_layout.addWidget(interventions_title)
        interventions_info = QLabel("✅ Module d'interventions prêt à être implémenté")
        interventions_info.setStyleSheet("font-size: 16px; color: #7f8c8d; margin: 20px;")
        interventions_layout.addWidget(interventions_info)
        maintenance_tabs.addTab(interventions_tab, "🔧 Interventions")
        
        # Onglet 2: Planification
        planning_tab = QWidget()
        planning_layout = QVBoxLayout(planning_tab)
        planning_title = QLabel("📅 Planification Préventive")
        planning_title.setStyleSheet("font-size: 20px; font-weight: bold; color: #2c3e50; margin: 20px;")
        planning_layout.addWidget(planning_title)
        planning_info = QLabel("✅ Module de planification prêt à être implémenté")
        planning_info.setStyleSheet("font-size: 16px; color: #7f8c8d; margin: 20px;")
        planning_layout.addWidget(planning_info)
        maintenance_tabs.addTab(planning_tab, "📅 Planification")
        
        # Onglet 3: Bons de Travail
        work_orders_tab = QWidget()
        work_orders_layout = QVBoxLayout(work_orders_tab)
        work_orders_title = QLabel("📋 Gestion des Bons de Travail")
        work_orders_title.setStyleSheet("font-size: 20px; font-weight: bold; color: #2c3e50; margin: 20px;")
        work_orders_layout.addWidget(work_orders_title)
        work_orders_info = QLabel("✅ Module des bons de travail prêt à être implémenté")
        work_orders_info.setStyleSheet("font-size: 16px; color: #7f8c8d; margin: 20px;")
        work_orders_layout.addWidget(work_orders_info)
        maintenance_tabs.addTab(work_orders_tab, "📋 Bons de Travail")
        
        # Onglet 4: Indicateurs
        indicators_tab = QWidget()
        indicators_layout = QVBoxLayout(indicators_tab)
        indicators_title = QLabel("📊 Historique et Indicateurs")
        indicators_title.setStyleSheet("font-size: 20px; font-weight: bold; color: #2c3e50; margin: 20px;")
        indicators_layout.addWidget(indicators_title)
        indicators_info = QLabel("✅ Module d'indicateurs prêt à être implémenté")
        indicators_info.setStyleSheet("font-size: 16px; color: #7f8c8d; margin: 20px;")
        indicators_layout.addWidget(indicators_info)
        maintenance_tabs.addTab(indicators_tab, "📊 Indicateurs")
        
        tabs_layout.addWidget(maintenance_tabs)
        layout.addWidget(tabs_frame)
        
        # Message de statut
        status_frame = QFrame()
        status_frame.setStyleSheet("QFrame { background-color: #d5f4e6; border-radius: 8px; padding: 15px; margin-top: 20px; }")
        status_layout = QVBoxLayout(status_frame)
        
        status_title = QLabel("🎯 Statut de l'Implémentation")
        status_title.setStyleSheet("font-size: 16px; font-weight: bold; color: #27ae60; margin-bottom: 10px;")
        status_layout.addWidget(status_title)
        
        status_info = QLabel("✅ Interface de base fonctionnelle | 🔧 Modules de maintenance prêts | 🚀 Prêt pour l'intégration complète")
        status_info.setStyleSheet("font-size: 14px; color: #27ae60;")
        status_layout.addWidget(status_info)
        
        layout.addWidget(status_frame)
        
        # Afficher la fenêtre
        main_window.show()
        print("✅ Application progressive lancée avec succès !")
        print("🎯 Interface de maintenance visible avec tous les onglets")
        
        # Lancer l'application
        return app.exec_()
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    print("🚀 Test de l'interface de maintenance progressive")
    print("=" * 60)
    
    result = main()
    sys.exit(result)
