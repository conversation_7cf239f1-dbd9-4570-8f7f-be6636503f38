"""
Module d'export Excel pour SOTRAMINE PHOSPHATE
"""

import os
import csv
from datetime import datetime

# Vérifier la disponibilité d'openpyxl
try:
    import openpyxl
    from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
    from openpyxl.utils import get_column_letter
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False

class ExcelExporter:
    """Classe pour l'export des données vers Excel"""
    
    def __init__(self, db):
        self.db = db
        self.export_dir = "export"
        
        # Créer le dossier d'export s'il n'existe pas
        if not os.path.exists(self.export_dir):
            os.makedirs(self.export_dir)
    
    def _create_styled_workbook(self, title="Export SOTRAMINE PHOSPHATE"):
        """Crée un classeur Excel avec style professionnel"""
        if not EXCEL_AVAILABLE:
            raise ImportError("Module openpyxl non disponible")
        
        wb = openpyxl.Workbook()
        ws = wb.active
        
        # Supprimer la feuille par défaut et en créer une nouvelle
        wb.remove(ws)
        ws = wb.create_sheet(title="Données")
        
        return wb
    
    def _style_worksheet(self, ws, headers, data_rows):
        """Applique un style professionnel à la feuille"""
        if not EXCEL_AVAILABLE:
            return
        
        # Style pour les headers
        header_font = Font(name='Calibri', size=12, bold=True, color='FFFFFF')
        header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
        header_alignment = Alignment(horizontal='center', vertical='center')
        
        # Style pour les données
        data_font = Font(name='Calibri', size=11)
        data_alignment = Alignment(horizontal='left', vertical='center')
        
        # Bordures
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # Appliquer les headers
        for col_num, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col_num, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
            cell.border = thin_border
        
        # Appliquer les données
        for row_num, row_data in enumerate(data_rows, 2):
            for col_num, value in enumerate(row_data, 1):
                cell = ws.cell(row=row_num, column=col_num, value=value)
                cell.font = data_font
                cell.alignment = data_alignment
                cell.border = thin_border
                
                # Alternance de couleurs pour les lignes
                if row_num % 2 == 0:
                    cell.fill = PatternFill(start_color='F8F9FA', end_color='F8F9FA', fill_type='solid')
        
        # Figer la première ligne
        ws.freeze_panes = 'A2'
        
        # Ajuster automatiquement les largeurs de colonnes
        for column in ws.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width
    
    def export_tasks_to_excel(self, parent=None):
        """Exporte les tâches vers Excel"""
        try:
            # Récupérer les données
            tasks = self.db.get_all_tasks()
            
            if not tasks:
                if parent:
                    from PyQt5.QtWidgets import QMessageBox
                    QMessageBox.information(parent, "Export", "Aucune tâche à exporter")
                return False
            
            # Générer le nom de fichier
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"taches_{timestamp}.xlsx"
            filepath = os.path.join(self.export_dir, filename)
            
            if EXCEL_AVAILABLE:
                # Export Excel avec formatage
                wb = self._create_styled_workbook("Export Tâches SOTRAMINE PHOSPHATE")
                ws = wb.active
                ws.title = "Tâches"
                
                # Headers
                headers = ['ID', 'Titre', 'Description', 'Date échéance', 'Priorité', 'Statut', 'Créé le']
                
                # Données
                data_rows = []
                for task in tasks:
                    data_rows.append([
                        task[0],  # ID
                        task[2],  # Titre
                        task[3],  # Description
                        task[4],  # Date échéance
                        task[5],  # Priorité
                        task[6],  # Statut
                        task[9]   # Créé le
                    ])
                
                # Appliquer le style
                self._style_worksheet(ws, headers, data_rows)
                
                # Sauvegarder
                wb.save(filepath)
                
            else:
                # Fallback CSV
                filename = f"taches_{timestamp}.csv"
                filepath = os.path.join(self.export_dir, filename)
                
                with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                    fieldnames = ['ID', 'Titre', 'Description', 'Date échéance', 'Priorité', 'Statut', 'Créé le']
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    
                    writer.writeheader()
                    for task in tasks:
                        writer.writerow({
                            'ID': task[0],
                            'Titre': task[2],
                            'Description': task[3],
                            'Date échéance': task[4],
                            'Priorité': task[5],
                            'Statut': task[6],
                            'Créé le': task[9]
                        })
            
            if parent:
                from PyQt5.QtWidgets import QMessageBox
                file_type = "Excel (.xlsx)" if EXCEL_AVAILABLE else "CSV"
                QMessageBox.information(parent, "Export Réussi", 
                                      f"Tâches exportées vers {file_type} :\n{filepath}")
            
            return True
            
        except Exception as e:
            if parent:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.critical(parent, "Erreur d'Export", 
                                   f"Erreur lors de l'export des tâches :\n{str(e)}")
            return False
    
    def export_equipment_to_excel(self, parent=None):
        """Exporte les équipements vers Excel"""
        try:
            # Récupérer les données
            equipment = self.db.get_all_equipment()
            
            if not equipment:
                if parent:
                    from PyQt5.QtWidgets import QMessageBox
                    QMessageBox.information(parent, "Export", "Aucun équipement à exporter")
                return False
            
            # Générer le nom de fichier
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"equipements_{timestamp}.xlsx"
            filepath = os.path.join(self.export_dir, filename)
            
            if EXCEL_AVAILABLE:
                # Export Excel avec formatage
                wb = self._create_styled_workbook("Export Équipements SOTRAMINE PHOSPHATE")
                ws = wb.active
                ws.title = "Équipements"
                
                # Headers
                headers = ['ID', 'Code', 'Nom', 'Modèle', 'Numéro série', 'Fabricant',
                          'Date achat', 'Date installation', 'Localisation', 'Statut',
                          'Dernière maintenance', 'Prochaine maintenance', 'Notes']
                
                # Données
                data_rows = []
                for eq in equipment:
                    data_rows.append([
                        eq[0],   # ID
                        eq[1] or '',   # Code
                        eq[2],   # Nom
                        eq[3] or '',   # Modèle
                        eq[4] or '',   # Numéro série
                        eq[5] or '',   # Fabricant
                        eq[6] or '',   # Date achat
                        eq[7] or '',   # Date installation
                        eq[8] or '',   # Localisation
                        eq[9],   # Statut
                        eq[10] or '',  # Dernière maintenance
                        eq[11] or '',  # Prochaine maintenance
                        eq[12] or ''   # Notes
                    ])
                
                # Appliquer le style
                self._style_worksheet(ws, headers, data_rows)
                
                # Sauvegarder
                wb.save(filepath)
                
            else:
                # Fallback CSV
                filename = f"equipements_{timestamp}.csv"
                filepath = os.path.join(self.export_dir, filename)
                
                with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                    fieldnames = ['ID', 'Code', 'Nom', 'Modèle', 'Numéro série', 'Fabricant',
                                 'Date achat', 'Date installation', 'Localisation', 'Statut',
                                 'Dernière maintenance', 'Prochaine maintenance', 'Notes']
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    
                    writer.writeheader()
                    for eq in equipment:
                        writer.writerow({
                            'ID': eq[0],
                            'Code': eq[1] or '',
                            'Nom': eq[2],
                            'Modèle': eq[3] or '',
                            'Numéro série': eq[4] or '',
                            'Fabricant': eq[5] or '',
                            'Date achat': eq[6] or '',
                            'Date installation': eq[7] or '',
                            'Localisation': eq[8] or '',
                            'Statut': eq[9],
                            'Dernière maintenance': eq[10] or '',
                            'Prochaine maintenance': eq[11] or '',
                            'Notes': eq[12] or ''
                        })
            
            if parent:
                from PyQt5.QtWidgets import QMessageBox
                file_type = "Excel (.xlsx)" if EXCEL_AVAILABLE else "CSV"
                QMessageBox.information(parent, "Export Réussi", 
                                      f"Équipements exportés vers {file_type} :\n{filepath}")
            
            return True
            
        except Exception as e:
            if parent:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.critical(parent, "Erreur d'Export", 
                                   f"Erreur lors de l'export des équipements :\n{str(e)}")
            return False
    
    def export_personnel_to_excel(self, parent=None):
        """Exporte le personnel vers Excel"""
        try:
            # Récupérer les données
            personnel = self.db.get_all_persons()
            
            if not personnel:
                if parent:
                    from PyQt5.QtWidgets import QMessageBox
                    QMessageBox.information(parent, "Export", "Aucun personnel à exporter")
                return False
            
            # Générer le nom de fichier
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"personnel_{timestamp}.xlsx"
            filepath = os.path.join(self.export_dir, filename)
            
            if EXCEL_AVAILABLE:
                # Export Excel avec formatage
                wb = self._create_styled_workbook("Export Personnel SOTRAMINE PHOSPHATE")
                ws = wb.active
                ws.title = "Personnel"
                
                # Headers
                headers = ['ID', 'Prénom', 'Nom', 'Poste', 'Email', 'Téléphone', 'Date embauche']
                
                # Données
                data_rows = []
                for person in personnel:
                    data_rows.append([
                        person[0],  # ID
                        person[1],  # Prénom
                        person[2],  # Nom
                        person[3] or '',  # Poste
                        person[4] or '',  # Email
                        person[5] or '',  # Téléphone
                        person[6] or ''   # Date embauche
                    ])
                
                # Appliquer le style
                self._style_worksheet(ws, headers, data_rows)
                
                # Sauvegarder
                wb.save(filepath)
                
            else:
                # Fallback CSV
                filename = f"personnel_{timestamp}.csv"
                filepath = os.path.join(self.export_dir, filename)
                
                with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                    fieldnames = ['ID', 'Prénom', 'Nom', 'Poste', 'Email', 'Téléphone', 'Date embauche']
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    
                    writer.writeheader()
                    for person in personnel:
                        writer.writerow({
                            'ID': person[0],
                            'Prénom': person[1],
                            'Nom': person[2],
                            'Poste': person[3] or '',
                            'Email': person[4] or '',
                            'Téléphone': person[5] or '',
                            'Date embauche': person[6] or ''
                        })
            
            if parent:
                from PyQt5.QtWidgets import QMessageBox
                file_type = "Excel (.xlsx)" if EXCEL_AVAILABLE else "CSV"
                QMessageBox.information(parent, "Export Réussi", 
                                      f"Personnel exporté vers {file_type} :\n{filepath}")
            
            return True
            
        except Exception as e:
            if parent:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.critical(parent, "Erreur d'Export",
                                   f"Erreur lors de l'export du personnel :\n{str(e)}")
            return False

    def export_spare_parts_to_excel(self, parent=None):
        """Exporte les pièces de rechange vers Excel"""
        try:
            # Récupérer les données
            spare_parts = self.db.get_all_spare_parts()

            if not spare_parts:
                if parent:
                    from PyQt5.QtWidgets import QMessageBox
                    QMessageBox.information(parent, "Export", "Aucune pièce de rechange à exporter")
                return False

            # Générer le nom de fichier
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"pieces_rechange_{timestamp}.xlsx"
            filepath = os.path.join(self.export_dir, filename)

            if EXCEL_AVAILABLE:
                # Export Excel avec formatage
                wb = self._create_styled_workbook("Export Pièces de Rechange SOTRAMINE PHOSPHATE")
                ws = wb.active
                ws.title = "Pièces de Rechange"

                # Headers
                headers = ['ID', 'Code', 'Nom', 'Quantité', 'Localisation', 'Type', 'Seuil minimum', 'Notes']

                # Données
                data_rows = []
                for part in spare_parts:
                    data_rows.append([
                        part[0],  # ID
                        part[1] or '',  # Code
                        part[2],  # Nom
                        part[3] or 0,   # Quantité
                        part[4] or '',  # Localisation
                        part[5] or '',  # Type
                        part[6] or 0,   # Seuil minimum
                        part[7] or ''   # Notes
                    ])

                # Appliquer le style
                self._style_worksheet(ws, headers, data_rows)

                # Sauvegarder
                wb.save(filepath)

            else:
                # Fallback CSV
                filename = f"pieces_rechange_{timestamp}.csv"
                filepath = os.path.join(self.export_dir, filename)

                with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                    fieldnames = ['ID', 'Code', 'Nom', 'Quantité', 'Localisation', 'Type', 'Seuil minimum', 'Notes']
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                    writer.writeheader()
                    for part in spare_parts:
                        writer.writerow({
                            'ID': part[0],
                            'Code': part[1] or '',
                            'Nom': part[2],
                            'Quantité': part[3] or 0,
                            'Localisation': part[4] or '',
                            'Type': part[5] or '',
                            'Seuil minimum': part[6] or 0,
                            'Notes': part[7] or ''
                        })

            if parent:
                from PyQt5.QtWidgets import QMessageBox
                file_type = "Excel (.xlsx)" if EXCEL_AVAILABLE else "CSV"
                QMessageBox.information(parent, "Export Réussi",
                                      f"Pièces de rechange exportées vers {file_type} :\n{filepath}")

            return True

        except Exception as e:
            if parent:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.critical(parent, "Erreur d'Export",
                                   f"Erreur lors de l'export des pièces de rechange :\n{str(e)}")
            return False

    def export_attendance_to_excel(self, parent=None):
        """Exporte les présences vers Excel"""
        try:
            # Récupérer les données
            try:
                attendance = self.db.get_attendance_records()
            except:
                attendance = []

            if not attendance:
                if parent:
                    from PyQt5.QtWidgets import QMessageBox
                    QMessageBox.information(parent, "Export", "Aucune donnée de présence à exporter")
                return False

            # Générer le nom de fichier
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"presences_{timestamp}.xlsx"
            filepath = os.path.join(self.export_dir, filename)

            if EXCEL_AVAILABLE:
                # Export Excel avec formatage
                wb = self._create_styled_workbook("Export Présences SOTRAMINE PHOSPHATE")
                ws = wb.active
                ws.title = "Présences"

                # Headers
                headers = ['ID', 'Personnel', 'Date', 'Statut', 'Heures travaillées', 'Notes']

                # Données
                data_rows = []
                for record in attendance:
                    data_rows.append([
                        record[0],  # ID
                        record[1] or '',  # Personnel
                        record[2] or '',  # Date
                        record[3] or '',  # Statut
                        record[4] or 0,   # Heures travaillées
                        record[5] or ''   # Notes
                    ])

                # Appliquer le style
                self._style_worksheet(ws, headers, data_rows)

                # Sauvegarder
                wb.save(filepath)

            else:
                # Fallback CSV
                filename = f"presences_{timestamp}.csv"
                filepath = os.path.join(self.export_dir, filename)

                with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                    fieldnames = ['ID', 'Personnel', 'Date', 'Statut', 'Heures travaillées', 'Notes']
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                    writer.writeheader()
                    for record in attendance:
                        writer.writerow({
                            'ID': record[0],
                            'Personnel': record[1] or '',
                            'Date': record[2] or '',
                            'Statut': record[3] or '',
                            'Heures travaillées': record[4] or 0,
                            'Notes': record[5] or ''
                        })

            if parent:
                from PyQt5.QtWidgets import QMessageBox
                file_type = "Excel (.xlsx)" if EXCEL_AVAILABLE else "CSV"
                QMessageBox.information(parent, "Export Réussi",
                                      f"Présences exportées vers {file_type} :\n{filepath}")

            return True

        except Exception as e:
            if parent:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.critical(parent, "Erreur d'Export",
                                   f"Erreur lors de l'export des présences :\n{str(e)}")
            return False
