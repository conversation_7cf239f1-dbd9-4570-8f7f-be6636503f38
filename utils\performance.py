"""
Utilitaires de monitoring et d'optimisation des performances
"""

import time
import functools
import threading
from datetime import datetime
from collections import defaultdict, deque
from utils.logger import get_performance_logger

class PerformanceMonitor:
    """Moniteur de performances pour l'application"""
    
    def __init__(self):
        self.perf_logger = get_performance_logger()
        self.operation_stats = defaultdict(list)
        self.slow_operations = deque(maxlen=100)  # Garder les 100 dernières opérations lentes
        self.lock = threading.RLock()
    
    def record_operation(self, operation_name, duration_ms, details=None):
        """Enregistre une opération et ses performances"""
        with self.lock:
            self.operation_stats[operation_name].append({
                'duration': duration_ms,
                'timestamp': datetime.now(),
                'details': details
            })
            
            # Garder seulement les 1000 dernières mesures par opération
            if len(self.operation_stats[operation_name]) > 1000:
                self.operation_stats[operation_name] = self.operation_stats[operation_name][-1000:]
            
            # Enregistrer les opérations lentes
            if duration_ms > 1000:  # Plus d'1 seconde
                self.slow_operations.append({
                    'operation': operation_name,
                    'duration': duration_ms,
                    'timestamp': datetime.now(),
                    'details': details
                })
    
    def get_operation_stats(self, operation_name):
        """Récupère les statistiques d'une opération"""
        with self.lock:
            if operation_name not in self.operation_stats:
                return None
            
            durations = [op['duration'] for op in self.operation_stats[operation_name]]
            if not durations:
                return None
            
            return {
                'count': len(durations),
                'avg': sum(durations) / len(durations),
                'min': min(durations),
                'max': max(durations),
                'recent_avg': sum(durations[-10:]) / min(len(durations), 10)  # Moyenne des 10 dernières
            }
    
    def get_slow_operations(self, limit=10):
        """Récupère les opérations les plus lentes récentes"""
        with self.lock:
            return list(self.slow_operations)[-limit:]
    
    def generate_report(self):
        """Génère un rapport de performance"""
        report = []
        report.append("=== RAPPORT DE PERFORMANCE ===")
        report.append(f"Généré le: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # Statistiques par opération
        report.append("Statistiques par opération:")
        for operation, stats in self.operation_stats.items():
            op_stats = self.get_operation_stats(operation)
            if op_stats:
                report.append(f"  {operation}:")
                report.append(f"    Nombre d'exécutions: {op_stats['count']}")
                report.append(f"    Durée moyenne: {op_stats['avg']:.2f}ms")
                report.append(f"    Durée min/max: {op_stats['min']:.2f}ms / {op_stats['max']:.2f}ms")
                report.append(f"    Moyenne récente: {op_stats['recent_avg']:.2f}ms")
                report.append("")
        
        # Opérations lentes
        slow_ops = self.get_slow_operations(5)
        if slow_ops:
            report.append("Opérations lentes récentes:")
            for op in slow_ops:
                report.append(f"  {op['operation']}: {op['duration']:.2f}ms à {op['timestamp'].strftime('%H:%M:%S')}")
        
        return "\n".join(report)

# Instance globale du moniteur
performance_monitor = PerformanceMonitor()

def monitor_performance(operation_name=None, log_slow=True, threshold_ms=1000):
    """
    Décorateur pour monitorer les performances d'une fonction
    
    Args:
        operation_name: Nom de l'opération (par défaut: nom de la fonction)
        log_slow: Si True, log les opérations lentes
        threshold_ms: Seuil en millisecondes pour considérer une opération comme lente
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            op_name = operation_name or f"{func.__module__}.{func.__name__}"
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                duration_ms = (time.time() - start_time) * 1000
                
                # Enregistrer l'opération
                performance_monitor.record_operation(op_name, duration_ms)
                
                # Logger si l'opération est lente
                if log_slow and duration_ms > threshold_ms:
                    performance_monitor.perf_logger.warning(
                        f"Opération lente détectée: {op_name} - {duration_ms:.2f}ms"
                    )
        
        return wrapper
    return decorator

def time_operation(operation_name):
    """
    Context manager pour mesurer le temps d'une opération
    
    Usage:
        with time_operation("ma_operation"):
            # code à mesurer
    """
    class TimerContext:
        def __init__(self, name):
            self.name = name
            self.start_time = None
        
        def __enter__(self):
            self.start_time = time.time()
            return self
        
        def __exit__(self, exc_type, exc_val, exc_tb):
            if self.start_time:
                duration_ms = (time.time() - self.start_time) * 1000
                performance_monitor.record_operation(self.name, duration_ms)
    
    return TimerContext(operation_name)

class DatabasePerformanceMonitor:
    """Moniteur spécialisé pour les performances de base de données"""
    
    def __init__(self, db_connection):
        self.conn = db_connection
        self.query_stats = defaultdict(list)
        self.lock = threading.RLock()
    
    def monitor_query(self, query, params=None):
        """Monitore l'exécution d'une requête"""
        def decorator(func):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    duration_ms = (time.time() - start_time) * 1000
                    
                    # Normaliser la requête pour les statistiques
                    normalized_query = self._normalize_query(query)
                    
                    with self.lock:
                        self.query_stats[normalized_query].append({
                            'duration': duration_ms,
                            'timestamp': datetime.now(),
                            'params': str(params) if params else None
                        })
                    
                    # Logger les requêtes lentes
                    if duration_ms > 500:  # Plus de 500ms
                        performance_monitor.perf_logger.warning(
                            f"Requête lente: {normalized_query[:100]}... - {duration_ms:.2f}ms"
                        )
            
            return wrapper
        return decorator
    
    def _normalize_query(self, query):
        """Normalise une requête SQL pour les statistiques"""
        # Remplacer les paramètres par des placeholders
        import re
        normalized = re.sub(r'\b\d+\b', '?', query)
        normalized = re.sub(r"'[^']*'", '?', normalized)
        normalized = ' '.join(normalized.split())  # Normaliser les espaces
        return normalized
    
    def get_slow_queries(self, limit=10):
        """Récupère les requêtes les plus lentes"""
        all_queries = []
        with self.lock:
            for query, executions in self.query_stats.items():
                for execution in executions:
                    all_queries.append({
                        'query': query,
                        'duration': execution['duration'],
                        'timestamp': execution['timestamp']
                    })
        
        # Trier par durée décroissante
        all_queries.sort(key=lambda x: x['duration'], reverse=True)
        return all_queries[:limit]

def optimize_database_connection(conn):
    """Applique des optimisations à une connexion de base de données"""
    cursor = conn.cursor()
    
    # Optimisations SQLite
    optimizations = [
        "PRAGMA journal_mode=WAL",
        "PRAGMA synchronous=NORMAL", 
        "PRAGMA cache_size=10000",
        "PRAGMA temp_store=MEMORY",
        "PRAGMA mmap_size=268435456",
        "PRAGMA foreign_keys=ON"
    ]
    
    for pragma in optimizations:
        try:
            cursor.execute(pragma)
        except Exception as e:
            performance_monitor.perf_logger.warning(f"Erreur lors de l'optimisation {pragma}: {e}")
    
    conn.commit()
    performance_monitor.perf_logger.info("Optimisations de base de données appliquées")

# Fonction utilitaire pour générer un rapport de performance
def generate_performance_report():
    """Génère et sauvegarde un rapport de performance"""
    report = performance_monitor.generate_report()
    
    # Sauvegarder le rapport
    from config import DATA_DIR
    report_path = DATA_DIR / f"performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    
    try:
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)
        performance_monitor.perf_logger.info(f"Rapport de performance sauvegardé: {report_path}")
        return str(report_path)
    except Exception as e:
        performance_monitor.perf_logger.error(f"Erreur lors de la sauvegarde du rapport: {e}")
        return None