#!/usr/bin/env python3
print("Test ultra simple")
print("Début du test")

try:
    print("Import de PyQt5...")
    from PyQt5.QtWidgets import QApplication
    print("PyQt5 importé")
    
    print("Import de database...")
    from database import Database
    print("Database importé")
    
    print("Test réussi !")
    
except Exception as e:
    print(f"Erreur: {e}")
    import traceback
    traceback.print_exc()

print("Fin du test")
