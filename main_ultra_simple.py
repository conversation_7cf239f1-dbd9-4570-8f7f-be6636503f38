#!/usr/bin/env python3
"""
Application Todo List Ultra Simple
Test de l'interface de base
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
from PyQt5.QtCore import Qt

def main():
    """Fonction principale ultra simple"""
    try:
        # Créer l'application PyQt5
        app = QApplication(sys.argv)
        app.setApplicationName("Todo List - Test Simple")
        
        # Créer la fenêtre principale
        main_window = QMainWindow()
        main_window.setWindowTitle("Todo List - Test Simple")
        main_window.setGeometry(100, 100, 800, 600)
        
        # Widget central
        central_widget = QWidget()
        main_window.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Titre
        title = QLabel("🧪 Test de l'Interface")
        title.setStyleSheet("font-size: 24px; font-weight: bold; margin: 20px;")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Message
        message = QLabel("✅ Interface PyQt5 fonctionnelle !")
        message.setStyleSheet("font-size: 16px; margin: 20px;")
        message.setAlignment(Qt.AlignCenter)
        layout.addWidget(message)
        
        # Bouton de test
        test_button = QPushButton("🎯 Test du Bouton")
        test_button.setStyleSheet("font-size: 14px; padding: 10px; margin: 20px;")
        test_button.clicked.connect(lambda: print("✅ Bouton cliqué avec succès !"))
        layout.addWidget(test_button)
        
        # Afficher la fenêtre
        main_window.show()
        print("✅ Application simple lancée avec succès !")
        
        # Lancer l'application
        return app.exec_()
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    print("🚀 Test de l'interface PyQt5")
    print("=" * 40)
    
    result = main()
    sys.exit(result)
