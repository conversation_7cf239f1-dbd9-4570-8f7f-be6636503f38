#!/usr/bin/env python3
"""
Diagnostic complet du problème de réinitialisation
"""

import os
import sqlite3
import sys
from datetime import datetime

def check_all_database_files():
    """Vérifie tous les fichiers de base de données possibles"""
    print("🔍 RECHERCHE DE TOUS LES FICHIERS DE BASE DE DONNÉES")
    print("=" * 60)
    
    # Fichiers possibles
    possible_files = [
        'sotramine.db',
        'sotramine.sqlite',
        'sotramine.sqlite3',
        'database.db',
        'app.db',
        'data/sotramine.db',
        'data/tasks.db',
        'backup_sotramine.db'
    ]
    
    found_files = []
    
    for file_path in possible_files:
        if os.path.exists(file_path):
            try:
                size = os.path.getsize(file_path)
                modified = datetime.fromtimestamp(os.path.getmtime(file_path))
                found_files.append((file_path, size, modified))
                print(f"📁 TROUVÉ : {file_path}")
                print(f"   Taille : {size} bytes")
                print(f"   Modifié : {modified}")
                
                # Analyser le contenu
                try:
                    conn = sqlite3.connect(file_path)
                    cursor = conn.cursor()
                    
                    # Lister les tables
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = [table[0] for table in cursor.fetchall()]
                    print(f"   Tables : {len(tables)} - {', '.join(tables[:5])}")
                    
                    # Compter les données dans les tables principales
                    for table in ['tasks', 'equipment', 'persons']:
                        try:
                            cursor.execute(f"SELECT COUNT(*) FROM {table}")
                            count = cursor.fetchone()[0]
                            if count > 0:
                                print(f"   {table} : {count} enregistrements")
                        except:
                            pass
                    
                    conn.close()
                    
                except Exception as e:
                    print(f"   ❌ Erreur lecture : {str(e)}")
                
                print()
                
            except Exception as e:
                print(f"❌ Erreur accès {file_path} : {str(e)}")
    
    return found_files

def check_database_class():
    """Vérifie la classe Database et son comportement"""
    print("🔧 ANALYSE DE LA CLASSE DATABASE")
    print("=" * 60)
    
    try:
        # Importer sans créer d'instance
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        # Vérifier le chemin de la base dans database.py
        with open('database.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
            # Chercher les définitions de chemin de base
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if 'DB_PATH' in line or 'database' in line.lower() and ('=' in line or 'connect' in line):
                    print(f"Ligne {i+1}: {line.strip()}")
        
        print("\n🔍 TENTATIVE D'IMPORT DE LA CLASSE DATABASE...")
        
        from database import Database
        
        # Vérifier les attributs de classe
        if hasattr(Database, 'DB_PATH'):
            print(f"✓ DB_PATH défini : {Database.DB_PATH}")
        else:
            print("⚠️ DB_PATH non défini comme attribut de classe")
        
        # Créer une instance temporaire pour voir ce qui se passe
        print("\n🧪 CRÉATION D'UNE INSTANCE TEMPORAIRE...")
        db = Database()
        
        # Vérifier le chemin utilisé
        if hasattr(db, 'db_path'):
            print(f"✓ Chemin utilisé par l'instance : {db.db_path}")
        
        # Vérifier la connexion
        if hasattr(db, 'conn') and db.conn:
            print("✓ Connexion active")
            
            # Vérifier les tables créées automatiquement
            cursor = db.conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [table[0] for table in cursor.fetchall()]
            print(f"✓ Tables créées automatiquement : {len(tables)}")
            for table in tables:
                print(f"   • {table}")
        
        db.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur analyse classe Database : {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def check_data_persistence():
    """Vérifie pourquoi les données persistent"""
    print("\n🔄 ANALYSE DE LA PERSISTANCE DES DONNÉES")
    print("=" * 60)
    
    # Vérifier s'il y a des données dans data/
    data_dir = 'data'
    if os.path.exists(data_dir):
        print(f"📁 Dossier 'data/' trouvé")
        for file in os.listdir(data_dir):
            file_path = os.path.join(data_dir, file)
            if file.endswith('.db'):
                size = os.path.getsize(file_path)
                print(f"   • {file} ({size} bytes)")
                
                # Analyser ce fichier
                try:
                    conn = sqlite3.connect(file_path)
                    cursor = conn.cursor()
                    
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = [table[0] for table in cursor.fetchall()]
                    
                    for table in ['tasks', 'equipment', 'persons']:
                        try:
                            cursor.execute(f"SELECT COUNT(*) FROM {table}")
                            count = cursor.fetchone()[0]
                            if count > 0:
                                print(f"     {table} : {count} enregistrements")
                        except:
                            pass
                    
                    conn.close()
                    
                except Exception as e:
                    print(f"     ❌ Erreur : {str(e)}")
    
    # Vérifier les sauvegardes automatiques
    backup_files = [f for f in os.listdir('.') if f.startswith('backup_') and f.endswith('.db')]
    if backup_files:
        print(f"\n💾 Fichiers de sauvegarde trouvés : {len(backup_files)}")
        for backup in backup_files:
            size = os.path.getsize(backup)
            print(f"   • {backup} ({size} bytes)")

def test_manual_deletion():
    """Test de suppression manuelle"""
    print("\n🗑️ TEST DE SUPPRESSION MANUELLE")
    print("=" * 60)
    
    # Lister tous les fichiers .db
    db_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.db'):
                db_files.append(os.path.join(root, file))
    
    print(f"📋 Fichiers .db trouvés : {len(db_files)}")
    for db_file in db_files:
        size = os.path.getsize(db_file)
        print(f"   • {db_file} ({size} bytes)")
    
    # Demander confirmation pour suppression
    response = input(f"\nVoulez-vous supprimer TOUS ces fichiers .db ? (tapez 'OUI' pour confirmer) : ")
    
    if response == 'OUI':
        deleted_count = 0
        for db_file in db_files:
            try:
                os.remove(db_file)
                print(f"✓ Supprimé : {db_file}")
                deleted_count += 1
            except Exception as e:
                print(f"❌ Erreur suppression {db_file} : {str(e)}")
        
        print(f"\n✅ {deleted_count} fichier(s) supprimé(s)")
        
        # Vérifier qu'ils sont bien supprimés
        remaining = []
        for db_file in db_files:
            if os.path.exists(db_file):
                remaining.append(db_file)
        
        if remaining:
            print(f"⚠️ {len(remaining)} fichier(s) persistent :")
            for file in remaining:
                print(f"   • {file}")
        else:
            print("✅ Tous les fichiers .db ont été supprimés")
        
        return deleted_count > 0
    else:
        print("❌ Suppression annulée")
        return False

def main():
    """Point d'entrée principal"""
    print("🔍 SOTRAMINE PHOSPHATE - DIAGNOSTIC COMPLET")
    print("Version 2.1 - Analyse Approfondie du Problème")
    print("=" * 70)
    
    # Étape 1: Chercher tous les fichiers de base
    found_files = check_all_database_files()
    
    # Étape 2: Analyser la classe Database
    database_ok = check_database_class()
    
    # Étape 3: Analyser la persistance
    check_data_persistence()
    
    # Étape 4: Proposer suppression manuelle
    if found_files:
        print(f"\n" + "=" * 70)
        print("💡 SOLUTION PROPOSÉE")
        print("=" * 70)
        
        print("Le problème semble venir du fait que plusieurs fichiers de base existent.")
        print("L'application recrée ou utilise un autre fichier après la réinitialisation.")
        print()
        
        test_manual_deletion()
    
    print(f"\n" + "=" * 70)
    print("📋 RÉSUMÉ DU DIAGNOSTIC")
    print("=" * 70)
    
    print(f"📁 Fichiers de base trouvés : {len(found_files)}")
    print(f"🔧 Classe Database analysée : {'✅' if database_ok else '❌'}")
    
    if len(found_files) > 1:
        print("\n⚠️ PROBLÈME IDENTIFIÉ :")
        print("   • Plusieurs fichiers de base de données existent")
        print("   • L'application peut utiliser un fichier différent")
        print("   • La réinitialisation ne supprime pas le bon fichier")
        
        print("\n💡 SOLUTIONS :")
        print("   1. Supprimer TOUS les fichiers .db")
        print("   2. Vérifier le chemin utilisé par Database()")
        print("   3. Modifier la classe Database si nécessaire")
    
    print(f"\n🔄 PROCHAINES ÉTAPES :")
    print("   1. Supprimer tous les fichiers .db trouvés")
    print("   2. Relancer l'application")
    print("   3. Vérifier que la base est vraiment vide")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Diagnostic interrompu par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur critique : {str(e)}")
        import traceback
        traceback.print_exc()
