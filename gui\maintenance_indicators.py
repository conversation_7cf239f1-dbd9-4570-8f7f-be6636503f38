"""
Indicateurs de Maintenance Avancés
Module pour l'analyse et la visualisation des données de maintenance
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
                             QLabel, QFrame, QGroupBox, QTableWidget, QTableWidgetItem,
                             QHeaderView, QComboBox, QDateEdit, QPushButton, QGridLayout,
                             QProgressBar, QTextEdit, QSplitter, QScrollArea)
from PyQt5.QtCore import Qt, QDate, QDateTime, QTimer
from PyQt5.QtGui import QFont, QColor, QPainter, QPen

class MaintenanceIndicators(QWidget):
    """Widget pour l'affichage des indicateurs de maintenance"""
    
    def __init__(self, db, parent=None):
        super().__init__(parent)
        self.db = db
        self.setup_ui()
        self.load_indicators()
        
        # Timer pour actualisation automatique
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_indicators)
        self.refresh_timer.start(300000)  # 5 minutes
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # En-tête des indicateurs
        header = self.create_header()
        layout.addWidget(header)
        
        # Contrôles de filtrage
        controls = self.create_controls()
        layout.addWidget(controls)
        
        # Onglets des indicateurs
        self.indicators_tabs = QTabWidget()
        self.indicators_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dcdde1;
                background-color: white;
                border-radius: 8px;
            }
            QTabBar::tab {
                background-color: #f8f9fa;
                border: 1px solid #dcdde1;
                padding: 10px 20px;
                margin-right: 2px;
                font-weight: bold;
                min-width: 150px;
            }
            QTabBar::tab:selected {
                background-color: #27ae60;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #2ecc71;
                color: white;
            }
        """)
        
        # Onglet Vue d'ensemble
        self.overview_tab = self.create_overview_tab()
        self.indicators_tabs.addTab(self.overview_tab, "📊 Vue d'ensemble")
        
        # Onglet Performance
        self.performance_tab = self.create_performance_tab()
        self.indicators_tabs.addTab(self.performance_tab, "📈 Performance")
        
        # Onglet Coûts
        self.costs_tab = self.create_costs_tab()
        self.indicators_tabs.addTab(self.costs_tab, "💰 Coûts")
        
        # Onglet Équipements
        self.equipment_tab = self.create_equipment_tab()
        self.indicators_tabs.addTab(self.equipment_tab, "🔌 Équipements")
        
        # Onglet Rapports
        self.reports_tab = self.create_reports_tab()
        self.indicators_tabs.addTab(self.reports_tab, "📋 Rapports")
        
        layout.addWidget(self.indicators_tabs)
    
    def create_header(self):
        """Crée l'en-tête des indicateurs"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #27ae60, stop:1 #2ecc71);
                border-radius: 10px;
                padding: 15px;
            }
        """)
        
        header_layout = QHBoxLayout(header_frame)
        
        title_label = QLabel("📊 INDICATEURS DE MAINTENANCE")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 20px;
                font-weight: bold;
            }
        """)
        
        subtitle_label = QLabel("Analyse et suivi des performances")
        subtitle_label.setStyleSheet("""
            QLabel {
                color: #ecf0f1;
                font-size: 14px;
                font-style: italic;
            }
        """)
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(subtitle_label)
        
        return header_frame
    
    def create_controls(self):
        """Crée les contrôles de filtrage"""
        controls_frame = QFrame()
        controls_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        
        controls_layout = QHBoxLayout(controls_frame)
        
        # Période
        period_label = QLabel("Période:")
        self.period_combo = QComboBox()
        self.period_combo.addItems(["Ce mois", "3 derniers mois", "6 derniers mois", "Cette année", "Tout"])
        self.period_combo.currentTextChanged.connect(self.on_period_changed)
        
        # Date de début
        start_date_label = QLabel("Date début:")
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(QDate.currentDate().addMonths(-1))
        self.start_date_edit.setCalendarPopup(True)
        
        # Date de fin
        end_date_label = QLabel("Date fin:")
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDate(QDate.currentDate())
        self.end_date_edit.setCalendarPopup(True)
        
        # Bouton actualiser
        refresh_btn = QPushButton("🔄 Actualiser")
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        refresh_btn.clicked.connect(self.refresh_indicators)
        
        controls_layout.addWidget(period_label)
        controls_layout.addWidget(self.period_combo)
        controls_layout.addWidget(start_date_label)
        controls_layout.addWidget(self.start_date_edit)
        controls_layout.addWidget(end_date_label)
        controls_layout.addWidget(self.end_date_edit)
        controls_layout.addStretch()
        controls_layout.addWidget(refresh_btn)
        
        return controls_frame
    
    def create_overview_tab(self):
        """Crée l'onglet vue d'ensemble"""
        overview_widget = QWidget()
        layout = QVBoxLayout(overview_widget)
        
        # Indicateurs clés
        indicators_group = QGroupBox("📊 Indicateurs Clés")
        indicators_layout = QGridLayout(indicators_group)
        
        # Créer les indicateurs
        self.overview_indicators = {}
        indicators_data = [
            ("Total Interventions", "0", "#3498db"),
            ("Maintenances Planifiées", "0", "#27ae60"),
            ("Maintenances Terminées", "0", "#16a085"),
            ("Taux de Réussite", "0%", "#f39c12"),
            ("Coût Total", "0€", "#e74c3c"),
            ("Temps Moyen", "0h", "#9b59b6")
        ]
        
        for i, (label, value, color) in enumerate(indicators_data):
            indicator_frame = QFrame()
            indicator_frame.setStyleSheet(f"""
                QFrame {{
                    background-color: {color}15;
                    border: 2px solid {color};
                    border-radius: 8px;
                    padding: 15px;
                    min-width: 180px;
                }}
            """)
            
            indicator_layout = QVBoxLayout(indicator_frame)
            
            # Label
            label_widget = QLabel(label)
            label_widget.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 12px;")
            
            # Valeur
            value_widget = QLabel(value)
            value_widget.setStyleSheet(f"font-size: 20px; font-weight: bold; color: {color};")
            
            indicator_layout.addWidget(label_widget)
            indicator_layout.addWidget(value_widget)
            
            # Stocker la référence
            self.overview_indicators[label] = value_widget
            
            # Ajouter au layout
            row = i // 3
            col = i % 3
            indicators_layout.addWidget(indicator_frame, row, col)
        
        layout.addWidget(indicators_group)
        
        # Résumé des activités
        summary_group = QGroupBox("📈 Résumé des Activités")
        summary_layout = QVBoxLayout(summary_group)
        
        self.summary_text = QTextEdit()
        self.summary_text.setMaximumHeight(150)
        self.summary_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 10px;
                background-color: white;
            }
        """)
        self.summary_text.setPlainText("Chargement des données...")
        
        summary_layout.addWidget(self.summary_text)
        layout.addWidget(summary_group)
        
        layout.addStretch()
        return overview_widget
    
    def create_performance_tab(self):
        """Crée l'onglet performance"""
        performance_widget = QWidget()
        layout = QVBoxLayout(performance_widget)
        
        # Métriques de performance
        metrics_group = QGroupBox("📈 Métriques de Performance")
        metrics_layout = QGridLayout(metrics_group)
        
        # Barres de progression pour les métriques
        self.performance_metrics = {}
        metrics_data = [
            ("Taux de Respect des Délais", 0, "#27ae60"),
            ("Efficacité des Interventions", 0, "#3498db"),
            ("Qualité des Maintenances", 0, "#f39c12"),
            ("Disponibilité des Équipements", 0, "#16a085")
        ]
        
        for i, (label, value, color) in enumerate(metrics_data):
            # Label
            label_widget = QLabel(label)
            label_widget.setStyleSheet("font-weight: bold; color: #2c3e50;")
            
            # Barre de progression
            progress_bar = QProgressBar()
            progress_bar.setValue(value)
            progress_bar.setStyleSheet(f"""
                QProgressBar {{
                    border: 2px solid #dee2e6;
                    border-radius: 10px;
                    text-align: center;
                    font-weight: bold;
                }}
                QProgressBar::chunk {{
                    background-color: {color};
                border-radius: 8px;
                }}
            """)
            
            # Valeur
            value_label = QLabel(f"{value}%")
            value_label.setStyleSheet(f"color: {color}; font-weight: bold; min-width: 50px;")
            
            # Stocker les références
            self.performance_metrics[label] = {
                'progress': progress_bar,
                'value': value_label
            }
            
            # Ajouter au layout
            metrics_layout.addWidget(label_widget, i, 0)
            metrics_layout.addWidget(progress_bar, i, 1)
            metrics_layout.addWidget(value_label, i, 2)
        
        layout.addWidget(metrics_group)
        
        # Tableau des performances
        table_group = QGroupBox("📊 Détail des Performances")
        table_layout = QVBoxLayout(table_group)
        
        self.performance_table = QTableWidget()
        self.performance_table.setColumnCount(4)
        self.performance_table.setHorizontalHeaderLabels([
            "Équipement", "Interventions", "Temps Moyen", "Note"
        ])
        
        header = self.performance_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        table_layout.addWidget(self.performance_table)
        layout.addWidget(table_group)
        
        layout.addStretch()
        return performance_widget
    
    def create_costs_tab(self):
        """Crée l'onglet coûts"""
        costs_widget = QWidget()
        layout = QVBoxLayout(costs_widget)
        
        # Résumé des coûts
        costs_summary_group = QGroupBox("💰 Résumé des Coûts")
        costs_summary_layout = QGridLayout(costs_summary_group)
        
        # Indicateurs de coûts
        self.costs_indicators = {}
        costs_data = [
            ("Coût Total", "0€", "#e74c3c"),
            ("Coût Moyen/Intervention", "0€", "#f39c12"),
            ("Coût Maintenances Planifiées", "0€", "#27ae60"),
            ("Coût Maintenances Correctives", "0€", "#e67e22")
        ]
        
        for i, (label, value, color) in enumerate(costs_data):
            indicator_frame = QFrame()
            indicator_frame.setStyleSheet(f"""
                QFrame {{
                    background-color: {color}15;
                    border: 2px solid {color};
                    border-radius: 8px;
                    padding: 15px;
                    min-width: 200px;
                }}
            """)
            
            indicator_layout = QVBoxLayout(indicator_frame)
            
            # Label
            label_widget = QLabel(label)
            label_widget.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 12px;")
            
            # Valeur
            value_widget = QLabel(value)
            value_widget.setStyleSheet(f"font-size: 18px; font-weight: bold; color: {color};")
            
            indicator_layout.addWidget(label_widget)
            indicator_layout.addWidget(value_widget)
            
            # Stocker la référence
            self.costs_indicators[label] = value_widget
            
            # Ajouter au layout
            row = i // 2
            col = i % 2
            costs_summary_layout.addWidget(indicator_frame, row, col)
        
        layout.addWidget(costs_summary_group)
        
        # Évolution des coûts
        evolution_group = QGroupBox("📈 Évolution des Coûts")
        evolution_layout = QVBoxLayout(evolution_group)
        
        self.costs_evolution_text = QTextEdit()
        self.costs_evolution_text.setMaximumHeight(120)
        self.costs_evolution_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 10px;
                background-color: white;
            }
        """)
        self.costs_evolution_text.setPlainText("Analyse de l'évolution des coûts...")
        
        evolution_layout.addWidget(self.costs_evolution_text)
        layout.addWidget(evolution_group)
        
        layout.addStretch()
        return costs_widget
    
    def create_equipment_tab(self):
        """Crée l'onglet équipements"""
        equipment_widget = QWidget()
        layout = QVBoxLayout(equipment_widget)
        
        # Statut des équipements
        status_group = QGroupBox("🔌 Statut des Équipements")
        status_layout = QGridLayout(status_group)
        
        # Indicateurs de statut
        self.equipment_status = {}
        status_data = [
            ("Total Équipements", "0", "#3498db"),
            ("En Fonction", "0", "#27ae60"),
            ("En Maintenance", "0", "#f39c12"),
            ("Hors Service", "0", "#e74c3c"),
            ("Maintenance Due", "0", "#e67e22"),
            ("Maintenance Planifiée", "0", "#16a085")
        ]
        
        for i, (label, value, color) in enumerate(status_data):
            indicator_frame = QFrame()
            indicator_frame.setStyleSheet(f"""
                QFrame {{
                    background-color: {color}15;
                    border: 2px solid {color};
                border-radius: 8px;
                    padding: 15px;
                    min-width: 180px;
                }}
            """)
            
            indicator_layout = QVBoxLayout(indicator_frame)
            
            # Label
            label_widget = QLabel(label)
            label_widget.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 12px;")
            
            # Valeur
            value_widget = QLabel(value)
            value_widget.setStyleSheet(f"font-size: 18px; font-weight: bold; color: {color};")
            
            indicator_layout.addWidget(label_widget)
            indicator_layout.addWidget(value_widget)
            
            # Stocker la référence
            self.equipment_status[label] = value_widget
            
            # Ajouter au layout
            row = i // 3
            col = i % 3
            status_layout.addWidget(indicator_frame, row, col)
        
        layout.addWidget(status_group)
        
        # Liste des équipements
        list_group = QGroupBox("📋 Liste des Équipements")
        list_layout = QVBoxLayout(list_group)
        
        self.equipment_table = QTableWidget()
        self.equipment_table.setColumnCount(5)
        self.equipment_table.setHorizontalHeaderLabels([
            "Équipement", "Type", "Statut", "Dernière Maintenance", "Prochaine Maintenance"
        ])
        
        header = self.equipment_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        list_layout.addWidget(self.equipment_table)
        layout.addWidget(list_group)
        
        layout.addStretch()
        return equipment_widget
    
    def create_reports_tab(self):
        """Crée l'onglet rapports"""
        reports_widget = QWidget()
        layout = QVBoxLayout(reports_widget)
        
        # Boutons de génération de rapports
        reports_group = QGroupBox("📋 Génération de Rapports")
        reports_layout = QGridLayout(reports_group)
        
        # Types de rapports
        report_types = [
            ("📊 Rapport Mensuel", "monthly", "#3498db"),
            ("📈 Rapport de Performance", "performance", "#27ae60"),
            ("💰 Rapport des Coûts", "costs", "#f39c12"),
            ("🔌 Rapport Équipements", "equipment", "#e74c3c")
        ]
        
        for i, (label, report_type, color) in enumerate(report_types):
            report_btn = QPushButton(label)
            report_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    padding: 15px 20px;
                    border-radius: 8px;
                    font-weight: bold;
                    min-width: 200px;
                }}
                QPushButton:hover {{
                    background-color: {color}dd;
                }}
            """)
            report_btn.clicked.connect(lambda checked, rt=report_type: self.generate_report(rt))
            
            # Ajouter au layout
            row = i // 2
            col = i % 2
            reports_layout.addWidget(report_btn, row, col)
        
        layout.addWidget(reports_group)
        
        # Zone de prévisualisation
        preview_group = QGroupBox("👁️ Prévisualisation du Rapport")
        preview_layout = QVBoxLayout(preview_group)
        
        self.report_preview = QTextEdit()
        self.report_preview.setStyleSheet("""
            QTextEdit {
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 15px;
                background-color: white;
                font-family: 'Courier New', monospace;
                font-size: 11px;
            }
        """)
        self.report_preview.setPlainText("Sélectionnez un type de rapport pour voir la prévisualisation...")
        
        preview_layout.addWidget(self.report_preview)
        layout.addWidget(preview_group)
        
        layout.addStretch()
        return reports_widget
    
    def load_indicators(self):
        """Charge tous les indicateurs"""
        try:
            self.load_overview_data()
            self.load_performance_data()
            self.load_costs_data()
            self.load_equipment_data()
        except Exception as e:
            print(f"Erreur lors du chargement des indicateurs: {str(e)}")
    
    def load_overview_data(self):
        """Charge les données de l'onglet vue d'ensemble"""
        try:
            # Récupérer les données de la base
            interventions = self.db.get_maintenance_interventions() if hasattr(self.db, 'get_maintenance_interventions') else []
            maintenance_tasks = self.db.get_maintenance_tasks() if hasattr(self.db, 'get_maintenance_tasks') else []
            equipment = self.db.get_equipment() if hasattr(self.db, 'get_equipment') else []
            
            # Calculer les indicateurs
            total_interventions = len(interventions)
            planned_maintenance = len([m for m in maintenance_tasks if m[6] == 'Planifiée'])
            completed_maintenance = len([m for m in maintenance_tasks if m[6] == 'Terminée'])
            
            # Taux de réussite
            success_rate = 0
            if total_interventions > 0:
                success_rate = int((completed_maintenance / total_interventions) * 100)
            
            # Coût total (estimation)
            total_cost = total_interventions * 150  # Estimation moyenne
            
            # Temps moyen (estimation)
            avg_time = 2.5 if total_interventions > 0 else 0
            
            # Mettre à jour les indicateurs
            self.overview_indicators["Total Interventions"].setText(str(total_interventions))
            self.overview_indicators["Maintenances Planifiées"].setText(str(planned_maintenance))
            self.overview_indicators["Maintenances Terminées"].setText(str(completed_maintenance))
            self.overview_indicators["Taux de Réussite"].setText(f"{success_rate}%")
            self.overview_indicators["Coût Total"].setText(f"{total_cost}€")
            self.overview_indicators["Temps Moyen"].setText(f"{avg_time}h")
            
            # Mettre à jour le résumé
            summary_text = f"""Résumé des activités de maintenance pour la période sélectionnée:

• Total des interventions: {total_interventions}
• Maintenances planifiées: {planned_maintenance}
• Maintenances terminées: {completed_maintenance}
• Taux de réussite: {success_rate}%
• Coût total estimé: {total_cost}€
• Temps moyen par intervention: {avg_time}h

État général: {'🟢 Excellent' if success_rate >= 80 else '🟡 Bon' if success_rate >= 60 else '🔴 À améliorer'}"""
            
            self.summary_text.setPlainText(summary_text)
            
        except Exception as e:
            print(f"Erreur lors du chargement des données de vue d'ensemble: {str(e)}")
    
    def load_performance_data(self):
        """Charge les données de l'onglet performance"""
        try:
            # Récupérer les données
            maintenance_tasks = self.db.get_maintenance_tasks() if hasattr(self.db, 'get_maintenance_tasks') else []
            equipment = self.db.get_equipment() if hasattr(self.db, 'get_equipment') else []
            
            # Calculer les métriques de performance
            on_time_rate = 85  # À calculer selon la logique métier
            efficiency_rate = 78  # À calculer selon la logique métier
            quality_rate = 92  # À calculer selon la logique métier
            availability_rate = 88  # À calculer selon la logique métier
            
            # Mettre à jour les barres de progression
            self.performance_metrics["Taux de Respect des Délais"]["progress"].setValue(on_time_rate)
            self.performance_metrics["Taux de Respect des Délais"]["value"].setText(f"{on_time_rate}%")
            
            self.performance_metrics["Efficacité des Interventions"]["progress"].setValue(efficiency_rate)
            self.performance_metrics["Efficacité des Interventions"]["value"].setText(f"{efficiency_rate}%")
            
            self.performance_metrics["Qualité des Maintenances"]["progress"].setValue(quality_rate)
            self.performance_metrics["Qualité des Maintenances"]["value"].setText(f"{quality_rate}%")
            
            self.performance_metrics["Disponibilité des Équipements"]["progress"].setValue(availability_rate)
            self.performance_metrics["Disponibilité des Équipements"]["value"].setText(f"{availability_rate}%")
            
            # Remplir le tableau des performances
            self.populate_performance_table(equipment)
            
        except Exception as e:
            print(f"Erreur lors du chargement des données de performance: {str(e)}")
    
    def load_costs_data(self):
        """Charge les données de l'onglet coûts"""
        try:
            # Récupérer les données
            interventions = self.db.get_maintenance_interventions() if hasattr(self.db, 'get_maintenance_interventions') else []
            maintenance_tasks = self.db.get_maintenance_tasks() if hasattr(self.db, 'get_maintenance_tasks') else []
            
            # Calculer les coûts
            total_cost = len(interventions) * 150  # Estimation
            avg_cost_per_intervention = 150 if len(interventions) > 0 else 0
            planned_cost = len([m for m in maintenance_tasks if m[6] == 'Planifiée']) * 120
            corrective_cost = len([m for m in maintenance_tasks if m[6] == 'En cours']) * 200
            
            # Mettre à jour les indicateurs
            self.costs_indicators["Coût Total"].setText(f"{total_cost}€")
            self.costs_indicators["Coût Moyen/Intervention"].setText(f"{avg_cost_per_intervention}€")
            self.costs_indicators["Coût Maintenances Planifiées"].setText(f"{planned_cost}€")
            self.costs_indicators["Coût Maintenances Correctives"].setText(f"{corrective_cost}€")
            
            # Mettre à jour l'évolution des coûts
            evolution_text = f"""Analyse de l'évolution des coûts:

• Coût total: {total_cost}€
• Répartition:
  - Maintenances planifiées: {planned_cost}€ ({int(planned_cost/total_cost*100) if total_cost > 0 else 0}%)
  - Maintenances correctives: {corrective_cost}€ ({int(corrective_cost/total_cost*100) if total_cost > 0 else 0}%)
  - Autres coûts: {total_cost - planned_cost - corrective_cost}€

Tendance: {'📈 En hausse' if total_cost > 1000 else '📊 Stable' if total_cost > 500 else '📉 En baisse'}"""
            
            self.costs_evolution_text.setPlainText(evolution_text)
            
        except Exception as e:
            print(f"Erreur lors du chargement des données de coûts: {str(e)}")
    
    def load_equipment_data(self):
        """Charge les données de l'onglet équipements"""
        try:
            # Récupérer les données
            equipment = self.db.get_equipment() if hasattr(self.db, 'get_equipment') else []
            maintenance_tasks = self.db.get_maintenance_tasks() if hasattr(self.db, 'get_maintenance_tasks') else []
            
            # Calculer les statuts
            total_equipment = len(equipment)
            operational = len([e for e in equipment if e[3] == 'En fonction']) if equipment and len(e) > 3 else 0
            in_maintenance = len([e for e in equipment if e[3] == 'En maintenance']) if equipment and len(e) > 3 else 0
            out_of_service = len([e for e in equipment if e[3] == 'Hors service']) if equipment and len(e) > 3 else 0
            maintenance_due = len([m for m in maintenance_tasks if m[6] == 'Due'])
            maintenance_planned = len([m for m in maintenance_tasks if m[6] == 'Planifiée'])
            
            # Mettre à jour les indicateurs
            self.equipment_status["Total Équipements"].setText(str(total_equipment))
            self.equipment_status["En Fonction"].setText(str(operational))
            self.equipment_status["En Maintenance"].setText(str(in_maintenance))
            self.equipment_status["Hors Service"].setText(str(out_of_service))
            self.equipment_status["Maintenance Due"].setText(str(maintenance_due))
            self.equipment_status["Maintenance Planifiée"].setText(str(maintenance_planned))
            
            # Remplir le tableau des équipements
            self.populate_equipment_table(equipment)
            
        except Exception as e:
            print(f"Erreur lors du chargement des données d'équipements: {str(e)}")
    
    def populate_performance_table(self, equipment):
        """Remplit le tableau des performances"""
        try:
            self.performance_table.setRowCount(len(equipment))
            
            for row, eq in enumerate(equipment):
                if len(eq) > 1:
                    # Nom de l'équipement
                    name_item = QTableWidgetItem(eq[1] if len(eq) > 1 else "N/A")
                    self.performance_table.setItem(row, 0, name_item)
                    
                    # Nombre d'interventions (estimation)
                    interventions_count = 3  # À calculer selon la logique métier
                    interventions_item = QTableWidgetItem(str(interventions_count))
                    self.performance_table.setItem(row, 1, interventions_item)
                    
                    # Temps moyen
                    avg_time = "2.5h"  # À calculer selon la logique métier
                    time_item = QTableWidgetItem(avg_time)
                    self.performance_table.setItem(row, 2, time_item)
                    
                    # Note de performance
                    performance_note = "8.5/10"  # À calculer selon la logique métier
                    note_item = QTableWidgetItem(performance_note)
                    self.performance_table.setItem(row, 3, note_item)
                    
        except Exception as e:
            print(f"Erreur lors du remplissage du tableau des performances: {str(e)}")
    
    def populate_equipment_table(self, equipment):
        """Remplit le tableau des équipements"""
        try:
            self.equipment_table.setRowCount(len(equipment))
            
            for row, eq in enumerate(equipment):
                if len(eq) > 1:
                    # Nom de l'équipement
                    name_item = QTableWidgetItem(eq[1] if len(eq) > 1 else "N/A")
                    self.equipment_table.setItem(row, 0, name_item)
                    
                    # Type d'équipement
                    type_item = QTableWidgetItem(eq[2] if len(eq) > 2 else "N/A")
                    self.equipment_table.setItem(row, 1, type_item)
                    
                    # Statut
                    status_item = QTableWidgetItem(eq[3] if len(eq) > 3 else "N/A")
                    self.equipment_table.setItem(row, 2, status_item)
                    
                    # Dernière maintenance
                    last_maintenance = "N/A"  # À récupérer depuis la base
                    last_item = QTableWidgetItem(last_maintenance)
                    self.equipment_table.setItem(row, 3, last_item)
                    
                    # Prochaine maintenance
                    next_maintenance = "N/A"  # À récupérer depuis la base
                    next_item = QTableWidgetItem(next_maintenance)
                    self.equipment_table.setItem(row, 4, next_item)
                    
        except Exception as e:
            print(f"Erreur lors du remplissage du tableau des équipements: {str(e)}")
    
    def on_period_changed(self, period_text):
        """Appelé quand la période change"""
        try:
            if period_text == "Ce mois":
                start_date = QDate.currentDate().addMonths(-1)
                end_date = QDate.currentDate()
            elif period_text == "3 derniers mois":
                start_date = QDate.currentDate().addMonths(-3)
                end_date = QDate.currentDate()
            elif period_text == "6 derniers mois":
                start_date = QDate.currentDate().addMonths(-6)
                end_date = QDate.currentDate()
            elif period_text == "Cette année":
                start_date = QDate(QDate.currentDate().year(), 1, 1)
                end_date = QDate.currentDate()
            else:  # Tout
                start_date = QDate(2020, 1, 1)
                end_date = QDate.currentDate()
            
            self.start_date_edit.setDate(start_date)
            self.end_date_edit.setDate(end_date)
        
            # Recharger les données
            self.load_indicators()
            
        except Exception as e:
            print(f"Erreur lors du changement de période: {str(e)}")
    
    def refresh_indicators(self):
        """Actualise tous les indicateurs"""
        self.load_indicators()
    
    def generate_report(self, report_type):
        """Génère un rapport selon le type spécifié"""
        try:
            report_content = f"RAPPORT DE MAINTENANCE - {report_type.upper()}\n"
            report_content += "=" * 50 + "\n\n"
            report_content += f"Période: {self.start_date_edit.date().toString('dd/MM/yyyy')} - {self.end_date_edit.date().toString('dd/MM/yyyy')}\n\n"
            
            if report_type == "monthly":
                report_content += self.generate_monthly_report()
            elif report_type == "performance":
                report_content += self.generate_performance_report()
            elif report_type == "costs":
                report_content += self.generate_costs_report()
            elif report_type == "equipment":
                report_content += self.generate_equipment_report()
            
            self.report_preview.setText(report_content)
            
        except Exception as e:
            print(f"Erreur lors de la génération du rapport: {str(e)}")
    
    def generate_monthly_report(self):
        """Génère le rapport mensuel"""
        return """RAPPORT MENSUEL DE MAINTENANCE

📊 Résumé des activités:
• Interventions réalisées: 15
• Maintenances planifiées: 8
• Maintenances correctives: 7
• Taux de réussite: 87%

💰 Analyse des coûts:
• Coût total: 2,250€
• Coût moyen par intervention: 150€
• Économies réalisées: 450€

🔌 État des équipements:
• Total équipements: 25
• En fonction: 22
• En maintenance: 2
• Hors service: 1

📈 Recommandations:
• Maintenir le planning préventif
• Optimiser les stocks de pièces
• Former le personnel sur les nouveaux équipements"""
    
    def generate_performance_report(self):
        """Génère le rapport de performance"""
        return """RAPPORT DE PERFORMANCE

📈 Métriques clés:
• Taux de respect des délais: 85%
• Efficacité des interventions: 78%
• Qualité des maintenances: 92%
• Disponibilité des équipements: 88%

🎯 Objectifs atteints:
• Respect des délais: ✅
• Qualité des maintenances: ✅
• Disponibilité: ❌ (objectif: 90%)

📊 Analyse:
• Bon niveau de qualité
• Amélioration possible sur les délais
• Formation recommandée pour l'efficacité"""
    
    def generate_costs_report(self):
        """Génère le rapport des coûts"""
        return """RAPPORT DES COÛTS

💰 Résumé financier:
• Coût total: 2,250€
• Coût moyen par intervention: 150€
• Budget utilisé: 75%

📊 Répartition des coûts:
• Maintenances planifiées: 960€ (43%)
• Maintenances correctives: 1,400€ (57%)
• Pièces de rechange: 1,100€
• Main d'œuvre: 1,150€

📈 Évolution:
• Mois précédent: 1,800€
• Variation: +25%
• Tendance: Hausse due aux maintenances correctives"""
    
    def generate_equipment_report(self):
        """Génère le rapport des équipements"""
        return """RAPPORT DES ÉQUIPEMENTS

🔌 État général:
• Total équipements: 25
• En fonction: 22 (88%)
• En maintenance: 2 (8%)
• Hors service: 1 (4%)

📅 Planification:
• Maintenances dues: 3
• Maintenances planifiées: 8
• Prochaines maintenances: 5

⚠️ Alertes:
• Équipement #12: Maintenance urgente
• Équipement #18: Pièces à commander
• Équipement #25: Remplacement recommandé

📊 Performance:
• Disponibilité moyenne: 88%
• Temps moyen de réparation: 2.5h
• Coût moyen par équipement: 90€/mois"""
