"""
Gestion professionnelle des interventions et bons de travail
Module complet pour la gestion des interventions de maintenance
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                             QTableWidgetItem, QPushButton, QLineEdit, QComboBox,
                             QLabel, QFrame, QSplitter, QGroupBox, QFormLayout,
                             QTextEdit, QDateEdit, QHeaderView, QMessageBox,
                             QDialog, QTabWidget, QProgressBar, QCheckBox,
                             QSpinBox, QDoubleSpinBox, QCalendarWidget, QTimeEdit,
                             QListWidget, QListWidgetItem, QTreeWidget, QTreeWidgetItem)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QTime, QDateTime, QTimer
from PyQt5.QtGui import QFont, QPixmap, QIcon, QColor
from utils.data_refresh_manager import RefreshableWidget, notify_data_changed

class InterventionsManagement(QWidget, RefreshableWidget):
    """Gestionnaire professionnel des interventions et bons de travail"""

    intervention_created = pyqtSignal(int)
    intervention_updated = pyqtSignal(int)
    work_order_created = pyqtSignal(int)

    def __init__(self, db, parent=None):
        super().__init__(parent)
        RefreshableWidget.__init__(self)
        self.db = db
        self.current_intervention = None
        self.current_work_order = None

        self.setup_ui()
        self.load_data()
        self.enable_auto_refresh(['maintenance', 'interventions'])

        # Timer pour actualisation automatique
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_data)
        self.refresh_timer.start(60000)  # Actualisation toutes les minutes

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Barre d'outils interventions
        toolbar = self.create_interventions_toolbar()
        layout.addWidget(toolbar)

        # Onglets principaux
        self.main_tabs = QTabWidget()
        self.main_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dcdde1;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f8f9fa;
                border: 1px solid #dcdde1;
                padding: 12px 20px;
                margin-right: 2px;
                font-weight: bold;
                min-width: 150px;
            }
            QTabBar::tab:selected {
                background-color: #e74c3c;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #c0392b;
                color: white;
            }
        """)

        # Onglet Interventions
        self.interventions_tab = self.create_interventions_tab()
        self.main_tabs.addTab(self.interventions_tab, "🔧 Interventions")

        # Onglet Bons de Travail
        self.work_orders_tab = self.create_work_orders_tab()
        self.main_tabs.addTab(self.work_orders_tab, "📋 Bons de Travail")

        # Onglet Planification
        self.planning_tab = self.create_planning_tab()
        self.main_tabs.addTab(self.planning_tab, "📅 Planification")

        layout.addWidget(self.main_tabs)

    def create_interventions_toolbar(self):
        """Crée la barre d'outils des interventions"""
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 10px;
            }
        """)

        layout = QHBoxLayout(toolbar_frame)
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(15)

        # Filtres
        filter_label = QLabel("🔍 Filtres :")
        filter_label.setStyleSheet("font-weight: bold; color: #495057;")
        layout.addWidget(filter_label)

        # Filtre par statut
        self.status_filter = QComboBox()
        self.status_filter.addItems([
            "Tous", "En attente", "Planifiée", "En cours",
            "Terminée", "Annulée", "En pause"
        ])
        self.status_filter.setStyleSheet("""
            QComboBox {
                padding: 8px 12px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 14px;
                min-width: 120px;
            }
        """)
        self.status_filter.currentTextChanged.connect(self.filter_interventions)
        layout.addWidget(self.status_filter)

        # Filtre par priorité
        priority_label = QLabel("⚡ Priorité :")
        priority_label.setStyleSheet("font-weight: bold; color: #495057;")
        layout.addWidget(priority_label)

        self.priority_filter = QComboBox()
        self.priority_filter.addItems(["Toutes", "Faible", "Normale", "Élevée", "Urgente", "Critique"])
        self.priority_filter.setStyleSheet("""
            QComboBox {
                padding: 8px 12px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 14px;
                min-width: 100px;
            }
        """)
        self.priority_filter.currentTextChanged.connect(self.filter_interventions)
        layout.addWidget(self.priority_filter)

        # Filtre par technicien
        technician_label = QLabel("👷 Technicien :")
        technician_label.setStyleSheet("font-weight: bold; color: #495057;")
        layout.addWidget(technician_label)

        self.technician_filter = QComboBox()
        self.technician_filter.addItem("Tous")
        self.load_technicians_filter()
        self.technician_filter.setStyleSheet("""
            QComboBox {
                padding: 8px 12px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 14px;
                min-width: 150px;
            }
        """)
        self.technician_filter.currentTextChanged.connect(self.filter_interventions)
        layout.addWidget(self.technician_filter)

        layout.addStretch()

        # Boutons d'actions
        actions_layout = QHBoxLayout()
        actions_layout.setSpacing(8)

        # Bouton Nouvelle Intervention
        new_intervention_btn = QPushButton("🔧 Nouvelle Intervention")
        new_intervention_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        new_intervention_btn.clicked.connect(self.create_intervention)
        actions_layout.addWidget(new_intervention_btn)

        # Bouton Nouveau Bon de Travail
        new_work_order_btn = QPushButton("📋 Nouveau Bon de Travail")
        new_work_order_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        new_work_order_btn.clicked.connect(self.create_work_order)
        actions_layout.addWidget(new_work_order_btn)

        # Bouton Planifier
        schedule_btn = QPushButton("📅 Planifier")
        schedule_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        schedule_btn.clicked.connect(self.schedule_intervention)
        actions_layout.addWidget(schedule_btn)

        layout.addLayout(actions_layout)

        return toolbar_frame

    def create_interventions_tab(self):
        """Crée l'onglet des interventions"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Splitter pour diviser en deux parties
        splitter = QSplitter(Qt.Horizontal)

        # Panneau gauche : Liste des interventions
        left_panel = self.create_interventions_list_panel()
        splitter.addWidget(left_panel)

        # Panneau droit : Détails de l'intervention
        right_panel = self.create_intervention_details_panel()
        splitter.addWidget(right_panel)

        # Proportions du splitter
        splitter.setSizes([700, 400])
        layout.addWidget(splitter)

        return tab

    def create_interventions_list_panel(self):
        """Crée le panneau de liste des interventions"""
        panel = QFrame()
        panel.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }
        """)

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # En-tête
        header_layout = QHBoxLayout()

        title_label = QLabel("🔧 Interventions de Maintenance")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #495057;
            }
        """)
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # Compteur d'interventions
        self.interventions_count_label = QLabel("0 intervention(s)")
        self.interventions_count_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        header_layout.addWidget(self.interventions_count_label)

        layout.addLayout(header_layout)

        # Table des interventions
        self.interventions_table = QTableWidget()
        self.interventions_table.setColumnCount(9)
        self.interventions_table.setHorizontalHeaderLabels([
            "ID", "Équipement", "Type", "Priorité", "Statut",
            "Technicien", "Date Prévue", "Durée", "Progression"
        ])

        # Configuration de la table
        self.interventions_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.interventions_table.setSelectionMode(QTableWidget.SingleSelection)
        self.interventions_table.setAlternatingRowColors(True)
        self.interventions_table.setSortingEnabled(True)

        # Style de la table
        self.interventions_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #e3f2fd;
                border: none;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                padding: 10px;
                border: none;
                border-bottom: 2px solid #dee2e6;
                font-weight: bold;
                color: #495057;
            }
        """)

        # Ajuster les colonnes
        header = self.interventions_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # ID
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # Équipement
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Type
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Priorité
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Statut
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Technicien
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Date
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # Durée
        header.setSectionResizeMode(8, QHeaderView.ResizeToContents)  # Progression

        # Connecter les signaux
        self.interventions_table.itemSelectionChanged.connect(self.on_intervention_selection_changed)
        self.interventions_table.itemDoubleClicked.connect(self.edit_intervention)

        layout.addWidget(self.interventions_table)

        return panel

    def create_intervention_details_panel(self):
        """Crée le panneau de détails de l'intervention"""
        panel = QFrame()
        panel.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }
        """)

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)

        # En-tête des détails
        header_label = QLabel("📋 Détails de l'Intervention")
        header_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #495057;
                padding-bottom: 10px;
                border-bottom: 2px solid #e74c3c;
            }
        """)
        layout.addWidget(header_label)

        # Onglets de détails
        self.intervention_details_tabs = QTabWidget()
        self.intervention_details_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                background-color: #f8f9fa;
            }
            QTabBar::tab {
                background-color: #e9ecef;
                border: 1px solid #dee2e6;
                padding: 8px 16px;
                margin-right: 2px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #e74c3c;
                color: white;
            }
        """)

        # Onglet Informations générales
        self.general_info_tab = self.create_intervention_general_tab()
        self.intervention_details_tabs.addTab(self.general_info_tab, "ℹ️ Général")

        # Onglet Pièces utilisées
        self.parts_tab = self.create_intervention_parts_tab()
        self.intervention_details_tabs.addTab(self.parts_tab, "🔧 Pièces")

        # Onglet Historique
        self.history_tab = self.create_intervention_history_tab()
        self.intervention_details_tabs.addTab(self.history_tab, "📋 Historique")

        layout.addWidget(self.intervention_details_tabs)

        # Boutons d'actions
        actions_frame = QFrame()
        actions_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-radius: 6px;
                padding: 10px;
            }
        """)

        actions_layout = QHBoxLayout(actions_frame)
        actions_layout.setSpacing(10)

        # Bouton Modifier
        self.edit_intervention_btn = QPushButton("✏️ Modifier")
        self.edit_intervention_btn.setStyleSheet("""
            QPushButton {
                background-color: #ffc107;
                color: #212529;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
            QPushButton:disabled {
                background-color: #6c757d;
                color: white;
            }
        """)
        self.edit_intervention_btn.clicked.connect(self.edit_intervention)
        self.edit_intervention_btn.setEnabled(False)
        actions_layout.addWidget(self.edit_intervention_btn)

        # Bouton Démarrer/Arrêter
        self.start_stop_btn = QPushButton("▶️ Démarrer")
        self.start_stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
        self.start_stop_btn.clicked.connect(self.toggle_intervention_status)
        self.start_stop_btn.setEnabled(False)
        actions_layout.addWidget(self.start_stop_btn)

        # Bouton Terminer
        self.complete_btn = QPushButton("✅ Terminer")
        self.complete_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
        self.complete_btn.clicked.connect(self.complete_intervention)
        self.complete_btn.setEnabled(False)
        actions_layout.addWidget(self.complete_btn)

        actions_layout.addStretch()

        # Bouton Supprimer
        self.delete_intervention_btn = QPushButton("🗑️ Supprimer")
        self.delete_intervention_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
        self.delete_intervention_btn.clicked.connect(self.delete_intervention)
        self.delete_intervention_btn.setEnabled(False)
        actions_layout.addWidget(self.delete_intervention_btn)

        layout.addWidget(actions_frame)

        return panel

    def create_intervention_general_tab(self):
        """Crée l'onglet d'informations générales de l'intervention"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # Formulaire d'informations
        form_layout = QFormLayout()
        form_layout.setSpacing(10)

        # Labels d'informations (lecture seule)
        self.intervention_info_labels = {}

        info_fields = [
            ("equipment_name", "Équipement :"),
            ("intervention_type", "Type d'intervention :"),
            ("priority", "Priorité :"),
            ("status", "Statut :"),
            ("technician", "Technicien assigné :"),
            ("scheduled_date", "Date prévue :"),
            ("actual_start", "Début réel :"),
            ("actual_end", "Fin réelle :"),
            ("estimated_duration", "Durée estimée :"),
            ("actual_duration", "Durée réelle :"),
            ("description", "Description :"),
            ("notes", "Notes :")
        ]

        for field_id, label_text in info_fields:
            label = QLabel(label_text)
            label.setStyleSheet("font-weight: bold; color: #495057;")

            if field_id in ["description", "notes"]:
                value_label = QLabel()
                value_label.setWordWrap(True)
                value_label.setMaximumHeight(80)
            else:
                value_label = QLabel()

            value_label.setStyleSheet("""
                QLabel {
                    background-color: white;
                    border: 1px solid #dee2e6;
                    border-radius: 4px;
                    padding: 8px;
                    color: #495057;
                }
            """)

            self.intervention_info_labels[field_id] = value_label
            form_layout.addRow(label, value_label)

        layout.addLayout(form_layout)
        layout.addStretch()

        return tab

    def create_intervention_parts_tab(self):
        """Crée l'onglet des pièces utilisées"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # Barre d'outils pièces
        parts_toolbar = QHBoxLayout()

        add_part_btn = QPushButton("➕ Ajouter Pièce")
        add_part_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        add_part_btn.clicked.connect(self.add_part_to_intervention)
        parts_toolbar.addWidget(add_part_btn)

        parts_toolbar.addStretch()

        layout.addLayout(parts_toolbar)

        # Table des pièces utilisées
        self.intervention_parts_table = QTableWidget()
        self.intervention_parts_table.setColumnCount(5)
        self.intervention_parts_table.setHorizontalHeaderLabels([
            "Pièce", "Code", "Quantité", "Prix Unitaire", "Total"
        ])

        self.intervention_parts_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                padding: 8px;
                border: none;
                border-bottom: 1px solid #dee2e6;
                font-weight: bold;
                color: #495057;
            }
        """)

        layout.addWidget(self.intervention_parts_table)

        return tab

    def create_intervention_history_tab(self):
        """Crée l'onglet d'historique de l'intervention"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # Liste des événements
        self.intervention_history_list = QListWidget()
        self.intervention_history_list.setStyleSheet("""
            QListWidget {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 5px;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f8f9fa;
                margin: 2px 0;
            }
            QListWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
        """)

        layout.addWidget(self.intervention_history_list)

        return tab

    def create_work_orders_tab(self):
        """Crée l'onglet des bons de travail"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Barre d'outils bons de travail
        work_orders_toolbar = QFrame()
        work_orders_toolbar.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 10px;
            }
        """)

        toolbar_layout = QHBoxLayout(work_orders_toolbar)
        toolbar_layout.setContentsMargins(15, 10, 15, 10)
        toolbar_layout.setSpacing(15)

        # Recherche
        search_label = QLabel("🔍 Recherche :")
        search_label.setStyleSheet("font-weight: bold; color: #495057;")
        toolbar_layout.addWidget(search_label)

        self.work_orders_search = QLineEdit()
        self.work_orders_search.setPlaceholderText("Numéro, équipement, description...")
        self.work_orders_search.setStyleSheet("""
            QLineEdit {
                padding: 8px 12px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 14px;
                min-width: 250px;
            }
        """)
        self.work_orders_search.textChanged.connect(self.filter_work_orders)
        toolbar_layout.addWidget(self.work_orders_search)

        # Filtre par statut
        status_label = QLabel("📊 Statut :")
        status_label.setStyleSheet("font-weight: bold; color: #495057;")
        toolbar_layout.addWidget(status_label)

        self.work_orders_status_filter = QComboBox()
        self.work_orders_status_filter.addItems([
            "Tous", "Créé", "Approuvé", "En cours", "Terminé", "Annulé"
        ])
        self.work_orders_status_filter.setStyleSheet("""
            QComboBox {
                padding: 8px 12px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 14px;
                min-width: 120px;
            }
        """)
        self.work_orders_status_filter.currentTextChanged.connect(self.filter_work_orders)
        toolbar_layout.addWidget(self.work_orders_status_filter)

        toolbar_layout.addStretch()

        # Bouton Nouveau Bon de Travail
        new_work_order_btn = QPushButton("📋 Nouveau Bon de Travail")
        new_work_order_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        new_work_order_btn.clicked.connect(self.create_work_order)
        toolbar_layout.addWidget(new_work_order_btn)

        layout.addWidget(work_orders_toolbar)

        # Table des bons de travail
        self.work_orders_table = QTableWidget()
        self.work_orders_table.setColumnCount(8)
        self.work_orders_table.setHorizontalHeaderLabels([
            "N° Bon", "Équipement", "Type", "Priorité", "Statut",
            "Demandeur", "Date Création", "Date Échéance"
        ])

        # Configuration de la table
        self.work_orders_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.work_orders_table.setSelectionMode(QTableWidget.SingleSelection)
        self.work_orders_table.setAlternatingRowColors(True)
        self.work_orders_table.setSortingEnabled(True)

        # Style de la table
        self.work_orders_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #e3f2fd;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                padding: 10px;
                border: none;
                border-bottom: 2px solid #dee2e6;
                font-weight: bold;
                color: #495057;
            }
        """)

        # Ajuster les colonnes
        header = self.work_orders_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # N° Bon
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # Équipement
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Type
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Priorité
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Statut
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Demandeur
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Date Création
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # Date Échéance

        # Connecter les signaux
        self.work_orders_table.itemSelectionChanged.connect(self.on_work_order_selection_changed)
        self.work_orders_table.itemDoubleClicked.connect(self.edit_work_order)

        layout.addWidget(self.work_orders_table)

        return tab

    def create_planning_tab(self):
        """Crée l'onglet de planification"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Splitter pour diviser en deux parties
        planning_splitter = QSplitter(Qt.Horizontal)

        # Panneau gauche : Calendrier
        calendar_panel = QFrame()
        calendar_panel.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }
        """)

        calendar_layout = QVBoxLayout(calendar_panel)
        calendar_layout.setContentsMargins(15, 15, 15, 15)
        calendar_layout.setSpacing(10)

        calendar_title = QLabel("📅 Calendrier des Interventions")
        calendar_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #495057;
                padding-bottom: 10px;
                border-bottom: 2px solid #3498db;
            }
        """)
        calendar_layout.addWidget(calendar_title)

        # Calendrier
        self.planning_calendar = QCalendarWidget()
        self.planning_calendar.setStyleSheet("""
            QCalendarWidget {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 4px;
            }
            QCalendarWidget QToolButton {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 5px;
                margin: 2px;
            }
            QCalendarWidget QToolButton:hover {
                background-color: #e9ecef;
            }
            QCalendarWidget QAbstractItemView:enabled {
                background-color: white;
                selection-background-color: #3498db;
                selection-color: white;
            }
        """)
        self.planning_calendar.clicked.connect(self.on_calendar_date_selected)
        calendar_layout.addWidget(self.planning_calendar)

        planning_splitter.addWidget(calendar_panel)

        # Panneau droit : Interventions du jour sélectionné
        daily_panel = QFrame()
        daily_panel.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }
        """)

        daily_layout = QVBoxLayout(daily_panel)
        daily_layout.setContentsMargins(15, 15, 15, 15)
        daily_layout.setSpacing(10)

        self.daily_title = QLabel("📋 Interventions du jour")
        self.daily_title.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #495057;
                padding-bottom: 10px;
                border-bottom: 2px solid #27ae60;
            }
        """)
        daily_layout.addWidget(self.daily_title)

        # Liste des interventions du jour
        self.daily_interventions_list = QListWidget()
        self.daily_interventions_list.setStyleSheet("""
            QListWidget {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 5px;
            }
            QListWidget::item {
                padding: 10px;
                border-bottom: 1px solid #f8f9fa;
                margin: 2px 0;
                border-radius: 4px;
            }
            QListWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            QListWidget::item:hover {
                background-color: #f8f9fa;
            }
        """)
        daily_layout.addWidget(self.daily_interventions_list)

        planning_splitter.addWidget(daily_panel)

        # Proportions du splitter
        planning_splitter.setSizes([400, 300])
        layout.addWidget(planning_splitter)

        return tab

    def load_data(self):
        """Charge toutes les données"""
        self.load_interventions()
        self.load_work_orders()
        self.load_daily_interventions()

    def load_technicians_filter(self):
        """Charge les techniciens pour le filtre"""
        try:
            cursor = self.db.cursor
            cursor.execute("SELECT id, name FROM technicians WHERE status = 'Actif' ORDER BY name")
            technicians = cursor.fetchall()

            for tech_id, tech_name in technicians:
                self.technician_filter.addItem(tech_name, tech_id)

        except Exception as e:
            print(f"Erreur chargement techniciens : {str(e)}")

    def load_interventions(self):
        """Charge la liste des interventions"""
        try:
            cursor = self.db.cursor
            cursor.execute("""
                SELECT i.id, e.name as equipment_name, i.intervention_type,
                       i.priority, i.status, t.name as technician_name,
                       i.scheduled_date, i.estimated_duration, i.progress,
                       i.description, i.actual_start_date, i.actual_end_date
                FROM maintenance_interventions i
                LEFT JOIN equipment e ON i.equipment_id = e.id
                LEFT JOIN technicians t ON i.technician_id = t.id
                ORDER BY i.scheduled_date DESC, i.priority DESC
            """)

            interventions = cursor.fetchall()
            self.populate_interventions_table(interventions)
            self.interventions_count_label.setText(f"{len(interventions)} intervention(s)")

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des interventions : {str(e)}")

    def populate_interventions_table(self, interventions):
        """Remplit la table des interventions"""
        self.interventions_table.setRowCount(len(interventions))

        for row, intervention in enumerate(interventions):
            # ID
            self.interventions_table.setItem(row, 0, QTableWidgetItem(str(intervention[0])))

            # Équipement
            self.interventions_table.setItem(row, 1, QTableWidgetItem(intervention[1] or ""))

            # Type
            self.interventions_table.setItem(row, 2, QTableWidgetItem(intervention[2] or ""))

            # Priorité avec couleur
            priority_item = QTableWidgetItem(intervention[3] or "")
            priority_color = self.get_priority_color(intervention[3])
            priority_item.setBackground(QColor(priority_color))
            self.interventions_table.setItem(row, 3, priority_item)

            # Statut avec couleur
            status_item = QTableWidgetItem(intervention[4] or "")
            status_color = self.get_status_color(intervention[4])
            status_item.setBackground(QColor(status_color))
            self.interventions_table.setItem(row, 4, status_item)

            # Technicien
            self.interventions_table.setItem(row, 5, QTableWidgetItem(intervention[5] or "Non assigné"))

            # Date prévue
            scheduled_date = ""
            if intervention[6]:
                scheduled_date = QDate.fromString(intervention[6], "yyyy-MM-dd").toString("dd/MM/yyyy")
            self.interventions_table.setItem(row, 6, QTableWidgetItem(scheduled_date))

            # Durée estimée
            duration = f"{intervention[7] or 0}h"
            self.interventions_table.setItem(row, 7, QTableWidgetItem(duration))

            # Progression
            progress = intervention[8] or 0
            progress_item = QTableWidgetItem(f"{progress}%")

            # Barre de progression visuelle
            progress_widget = QProgressBar()
            progress_widget.setValue(progress)
            progress_widget.setStyleSheet("""
                QProgressBar {
                    border: 1px solid #dee2e6;
                    border-radius: 4px;
                    text-align: center;
                    font-weight: bold;
                }
                QProgressBar::chunk {
                    background-color: #28a745;
                    border-radius: 3px;
                }
            """)

            self.interventions_table.setItem(row, 8, progress_item)
            self.interventions_table.setCellWidget(row, 8, progress_widget)

    def get_priority_color(self, priority):
        """Retourne la couleur associée à la priorité"""
        colors = {
            "Faible": "#d4edda",
            "Normale": "#d1ecf1",
            "Élevée": "#fff3cd",
            "Urgente": "#f8d7da",
            "Critique": "#f5c6cb"
        }
        return colors.get(priority, "#f8f9fa")

    def get_status_color(self, status):
        """Retourne la couleur associée au statut"""
        colors = {
            "En attente": "#d1ecf1",
            "Planifiée": "#d4edda",
            "En cours": "#fff3cd",
            "Terminée": "#d4edda",
            "Annulée": "#f8d7da",
            "En pause": "#e2e3e5"
        }
        return colors.get(status, "#f8f9fa")

    def filter_interventions(self):
        """Filtre les interventions selon les critères"""
        status_filter = self.status_filter.currentText()
        priority_filter = self.priority_filter.currentText()
        technician_filter = self.technician_filter.currentText()

        for row in range(self.interventions_table.rowCount()):
            show_row = True

            # Filtre par statut
            if status_filter != "Tous":
                status_item = self.interventions_table.item(row, 4)
                if not status_item or status_item.text() != status_filter:
                    show_row = False

            # Filtre par priorité
            if priority_filter != "Toutes":
                priority_item = self.interventions_table.item(row, 3)
                if not priority_item or priority_item.text() != priority_filter:
                    show_row = False

            # Filtre par technicien
            if technician_filter != "Tous":
                technician_item = self.interventions_table.item(row, 5)
                if not technician_item or technician_item.text() != technician_filter:
                    show_row = False

            self.interventions_table.setRowHidden(row, not show_row)

    def on_intervention_selection_changed(self):
        """Gère le changement de sélection d'intervention"""
        selected_rows = set()
        for item in self.interventions_table.selectedItems():
            selected_rows.add(item.row())

        if selected_rows:
            # Prendre la première intervention sélectionnée
            row = min(selected_rows)
            intervention_id_item = self.interventions_table.item(row, 0)
            if intervention_id_item:
                intervention_id = int(intervention_id_item.text())
                self.load_intervention_details(intervention_id)
                self.current_intervention = intervention_id

                # Activer les boutons
                self.edit_intervention_btn.setEnabled(True)
                self.start_stop_btn.setEnabled(True)
                self.complete_btn.setEnabled(True)
                self.delete_intervention_btn.setEnabled(True)

                # Mettre à jour le texte du bouton démarrer/arrêter
                status_item = self.interventions_table.item(row, 4)
                if status_item:
                    status = status_item.text()
                    if status == "En cours":
                        self.start_stop_btn.setText("⏸️ Pause")
                        self.start_stop_btn.setStyleSheet("""
                            QPushButton {
                                background-color: #ffc107;
                                color: #212529;
                                border: none;
                                border-radius: 6px;
                                padding: 10px 16px;
                                font-weight: bold;
                                font-size: 13px;
                            }
                            QPushButton:hover {
                                background-color: #e0a800;
                            }
                        """)
                    else:
                        self.start_stop_btn.setText("▶️ Démarrer")
                        self.start_stop_btn.setStyleSheet("""
                            QPushButton {
                                background-color: #28a745;
                                color: white;
                                border: none;
                                border-radius: 6px;
                                padding: 10px 16px;
                                font-weight: bold;
                                font-size: 13px;
                            }
                            QPushButton:hover {
                                background-color: #218838;
                            }
                        """)
        else:
            self.clear_intervention_details()
            self.current_intervention = None

            # Désactiver les boutons
            self.edit_intervention_btn.setEnabled(False)
            self.start_stop_btn.setEnabled(False)
            self.complete_btn.setEnabled(False)
            self.delete_intervention_btn.setEnabled(False)

    def load_intervention_details(self, intervention_id):
        """Charge les détails d'une intervention"""
        try:
            cursor = self.db.cursor
            cursor.execute("""
                SELECT i.*, e.name as equipment_name, t.name as technician_name
                FROM maintenance_interventions i
                LEFT JOIN equipment e ON i.equipment_id = e.id
                LEFT JOIN technicians t ON i.technician_id = t.id
                WHERE i.id = ?
            """, (intervention_id,))

            intervention = cursor.fetchone()
            if intervention:
                # Remplir les informations générales
                self.intervention_info_labels["equipment_name"].setText(intervention[15] or "")
                self.intervention_info_labels["intervention_type"].setText(intervention[3] or "")
                self.intervention_info_labels["priority"].setText(intervention[4] or "")
                self.intervention_info_labels["status"].setText(intervention[5] or "")
                self.intervention_info_labels["technician"].setText(intervention[16] or "Non assigné")

                # Dates
                if intervention[6]:  # scheduled_date
                    scheduled_date = QDateTime.fromString(intervention[6], "yyyy-MM-dd hh:mm:ss").toString("dd/MM/yyyy hh:mm")
                    self.intervention_info_labels["scheduled_date"].setText(scheduled_date)
                else:
                    self.intervention_info_labels["scheduled_date"].setText("")

                if intervention[7]:  # actual_start_date
                    start_date = QDateTime.fromString(intervention[7], "yyyy-MM-dd hh:mm:ss").toString("dd/MM/yyyy hh:mm")
                    self.intervention_info_labels["actual_start"].setText(start_date)
                else:
                    self.intervention_info_labels["actual_start"].setText("")

                if intervention[8]:  # actual_end_date
                    end_date = QDateTime.fromString(intervention[8], "yyyy-MM-dd hh:mm:ss").toString("dd/MM/yyyy hh:mm")
                    self.intervention_info_labels["actual_end"].setText(end_date)
                else:
                    self.intervention_info_labels["actual_end"].setText("")

                # Durées
                self.intervention_info_labels["estimated_duration"].setText(f"{intervention[9] or 0}h")
                self.intervention_info_labels["actual_duration"].setText(f"{intervention[10] or 0}h")

                # Description et notes
                self.intervention_info_labels["description"].setText(intervention[11] or "")
                self.intervention_info_labels["notes"].setText(intervention[12] or "")

                # Charger les pièces utilisées
                self.load_intervention_parts(intervention_id)

                # Charger l'historique
                self.load_intervention_history(intervention_id)

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des détails : {str(e)}")

    def clear_intervention_details(self):
        """Efface les détails de l'intervention"""
        for label in self.intervention_info_labels.values():
            label.setText("")

        self.intervention_parts_table.setRowCount(0)
        self.intervention_history_list.clear()

    def load_intervention_parts(self, intervention_id):
        """Charge les pièces utilisées dans l'intervention"""
        try:
            cursor = self.db.cursor
            cursor.execute("""
                SELECT sp.name, sp.code, ip.quantity, sp.unit_price,
                       (ip.quantity * sp.unit_price) as total
                FROM intervention_parts ip
                JOIN spare_parts sp ON ip.part_id = sp.id
                WHERE ip.intervention_id = ?
            """, (intervention_id,))

            parts = cursor.fetchall()
            self.intervention_parts_table.setRowCount(len(parts))

            total_cost = 0
            for row, part in enumerate(parts):
                self.intervention_parts_table.setItem(row, 0, QTableWidgetItem(part[0] or ""))
                self.intervention_parts_table.setItem(row, 1, QTableWidgetItem(part[1] or ""))
                self.intervention_parts_table.setItem(row, 2, QTableWidgetItem(str(part[2] or 0)))
                self.intervention_parts_table.setItem(row, 3, QTableWidgetItem(f"{part[3] or 0:.2f} €"))
                self.intervention_parts_table.setItem(row, 4, QTableWidgetItem(f"{part[4] or 0:.2f} €"))

                total_cost += part[4] or 0

            # Ajouter une ligne de total si des pièces sont présentes
            if parts:
                total_row = self.intervention_parts_table.rowCount()
                self.intervention_parts_table.setRowCount(total_row + 1)

                total_item = QTableWidgetItem("TOTAL")
                total_item.setFont(QFont("", -1, QFont.Bold))
                self.intervention_parts_table.setItem(total_row, 3, total_item)

                total_cost_item = QTableWidgetItem(f"{total_cost:.2f} €")
                total_cost_item.setFont(QFont("", -1, QFont.Bold))
                total_cost_item.setBackground(QColor("#e3f2fd"))
                self.intervention_parts_table.setItem(total_row, 4, total_cost_item)

        except Exception as e:
            print(f"Erreur chargement pièces intervention : {str(e)}")

    def load_intervention_history(self, intervention_id):
        """Charge l'historique de l'intervention"""
        try:
            cursor = self.db.cursor
            cursor.execute("""
                SELECT event_date, event_type, description, user_name
                FROM intervention_history
                WHERE intervention_id = ?
                ORDER BY event_date DESC
            """, (intervention_id,))

            history = cursor.fetchall()
            self.intervention_history_list.clear()

            for event in history:
                event_date = QDateTime.fromString(event[0], "yyyy-MM-dd hh:mm:ss").toString("dd/MM/yyyy hh:mm")
                event_text = f"[{event_date}] {event[1]} - {event[2]}"
                if event[3]:
                    event_text += f" (par {event[3]})"

                item = QListWidgetItem(event_text)

                # Colorer selon le type d'événement
                if event[1] == "Création":
                    item.setBackground(QColor("#d4edda"))
                elif event[1] == "Démarrage":
                    item.setBackground(QColor("#d1ecf1"))
                elif event[1] == "Pause":
                    item.setBackground(QColor("#fff3cd"))
                elif event[1] == "Terminaison":
                    item.setBackground(QColor("#d4edda"))
                elif event[1] == "Annulation":
                    item.setBackground(QColor("#f8d7da"))

                self.intervention_history_list.addItem(item)

        except Exception as e:
            print(f"Erreur chargement historique intervention : {str(e)}")

    def load_work_orders(self):
        """Charge la liste des bons de travail"""
        try:
            cursor = self.db.cursor
            cursor.execute("""
                SELECT wo.id, wo.work_order_number, e.name as equipment_name,
                       wo.work_type, wo.priority, wo.status, wo.requester_name,
                       wo.creation_date, wo.due_date, wo.description
                FROM work_orders wo
                LEFT JOIN equipment e ON wo.equipment_id = e.id
                ORDER BY wo.creation_date DESC
            """)

            work_orders = cursor.fetchall()
            self.populate_work_orders_table(work_orders)

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors du chargement des bons de travail : {str(e)}")

    def populate_work_orders_table(self, work_orders):
        """Remplit la table des bons de travail"""
        self.work_orders_table.setRowCount(len(work_orders))

        for row, work_order in enumerate(work_orders):
            # N° Bon
            self.work_orders_table.setItem(row, 0, QTableWidgetItem(work_order[1] or ""))

            # Équipement
            self.work_orders_table.setItem(row, 1, QTableWidgetItem(work_order[2] or ""))

            # Type
            self.work_orders_table.setItem(row, 2, QTableWidgetItem(work_order[3] or ""))

            # Priorité avec couleur
            priority_item = QTableWidgetItem(work_order[4] or "")
            priority_color = self.get_priority_color(work_order[4])
            priority_item.setBackground(QColor(priority_color))
            self.work_orders_table.setItem(row, 3, priority_item)

            # Statut avec couleur
            status_item = QTableWidgetItem(work_order[5] or "")
            status_color = self.get_work_order_status_color(work_order[5])
            status_item.setBackground(QColor(status_color))
            self.work_orders_table.setItem(row, 4, status_item)

            # Demandeur
            self.work_orders_table.setItem(row, 5, QTableWidgetItem(work_order[6] or ""))

            # Date de création
            creation_date = ""
            if work_order[7]:
                creation_date = QDate.fromString(work_order[7], "yyyy-MM-dd").toString("dd/MM/yyyy")
            self.work_orders_table.setItem(row, 6, QTableWidgetItem(creation_date))

            # Date d'échéance
            due_date = ""
            if work_order[8]:
                due_date = QDate.fromString(work_order[8], "yyyy-MM-dd").toString("dd/MM/yyyy")
                # Colorer en rouge si en retard
                due_item = QTableWidgetItem(due_date)
                if QDate.fromString(work_order[8], "yyyy-MM-dd") < QDate.currentDate():
                    due_item.setBackground(QColor("#ffebee"))
                    due_item.setForeground(QColor("#d32f2f"))
                self.work_orders_table.setItem(row, 7, due_item)
            else:
                self.work_orders_table.setItem(row, 7, QTableWidgetItem(""))

    def get_work_order_status_color(self, status):
        """Retourne la couleur associée au statut du bon de travail"""
        colors = {
            "Créé": "#d1ecf1",
            "Approuvé": "#d4edda",
            "En cours": "#fff3cd",
            "Terminé": "#d4edda",
            "Annulé": "#f8d7da"
        }
        return colors.get(status, "#f8f9fa")

    def filter_work_orders(self):
        """Filtre les bons de travail"""
        search_text = self.work_orders_search.text().lower()
        status_filter = self.work_orders_status_filter.currentText()

        for row in range(self.work_orders_table.rowCount()):
            show_row = True

            # Filtre par texte de recherche
            if search_text:
                row_text = ""
                for col in range(3):  # N° Bon, Équipement, Type
                    item = self.work_orders_table.item(row, col)
                    if item:
                        row_text += item.text().lower() + " "

                if search_text not in row_text:
                    show_row = False

            # Filtre par statut
            if status_filter != "Tous":
                status_item = self.work_orders_table.item(row, 4)
                if not status_item or status_item.text() != status_filter:
                    show_row = False

            self.work_orders_table.setRowHidden(row, not show_row)

    def on_work_order_selection_changed(self):
        """Gère le changement de sélection de bon de travail"""
        selected_rows = set()
        for item in self.work_orders_table.selectedItems():
            selected_rows.add(item.row())

        if selected_rows:
            row = min(selected_rows)
            # Ici on pourrait charger les détails du bon de travail
            # Pour l'instant, on stocke juste l'ID
            work_order_number_item = self.work_orders_table.item(row, 0)
            if work_order_number_item:
                self.current_work_order = work_order_number_item.text()
        else:
            self.current_work_order = None

    def load_daily_interventions(self):
        """Charge les interventions du jour sélectionné"""
        selected_date = self.planning_calendar.selectedDate()
        date_str = selected_date.toString("yyyy-MM-dd")

        self.daily_title.setText(f"📋 Interventions du {selected_date.toString('dd/MM/yyyy')}")

        try:
            cursor = self.db.cursor
            cursor.execute("""
                SELECT i.id, e.name as equipment_name, i.intervention_type,
                       i.priority, i.status, t.name as technician_name,
                       i.scheduled_date, i.estimated_duration
                FROM maintenance_interventions i
                LEFT JOIN equipment e ON i.equipment_id = e.id
                LEFT JOIN technicians t ON i.technician_id = t.id
                WHERE DATE(i.scheduled_date) = ?
                ORDER BY i.scheduled_date
            """, (date_str,))

            interventions = cursor.fetchall()
            self.daily_interventions_list.clear()

            for intervention in interventions:
                time_str = ""
                if intervention[6]:
                    time_str = QDateTime.fromString(intervention[6], "yyyy-MM-dd hh:mm:ss").toString("hh:mm")

                item_text = f"[{time_str}] {intervention[1]} - {intervention[2]}"
                item_text += f"\nTechnicien: {intervention[5] or 'Non assigné'}"
                item_text += f" | Priorité: {intervention[3]} | Statut: {intervention[4]}"

                item = QListWidgetItem(item_text)

                # Colorer selon la priorité
                priority_color = self.get_priority_color(intervention[3])
                item.setBackground(QColor(priority_color))

                # Stocker l'ID de l'intervention
                item.setData(Qt.UserRole, intervention[0])

                self.daily_interventions_list.addItem(item)

        except Exception as e:
            print(f"Erreur chargement interventions du jour : {str(e)}")

    def on_calendar_date_selected(self, date):
        """Gère la sélection d'une date dans le calendrier"""
        self.load_daily_interventions()

    # Méthodes d'actions
    def create_intervention(self):
        """Crée une nouvelle intervention"""
        from gui.intervention_dialog_pro import InterventionDialogPro
        dialog = InterventionDialogPro(self.db, self)
        if dialog.exec_() == QDialog.Accepted:
            self.refresh_data()
            notify_data_changed('maintenance')
            self.intervention_created.emit(dialog.intervention_id)

    def edit_intervention(self):
        """Modifie l'intervention sélectionnée"""
        if self.current_intervention:
            from gui.intervention_dialog_pro import InterventionDialogPro
            dialog = InterventionDialogPro(self.db, self, self.current_intervention)
            if dialog.exec_() == QDialog.Accepted:
                self.refresh_data()
                notify_data_changed('maintenance')
                self.intervention_updated.emit(self.current_intervention)

    def toggle_intervention_status(self):
        """Démarre ou met en pause l'intervention"""
        if self.current_intervention:
            try:
                cursor = self.db.cursor

                # Récupérer le statut actuel
                cursor.execute("SELECT status FROM maintenance_interventions WHERE id = ?",
                             (self.current_intervention,))
                current_status = cursor.fetchone()[0]

                if current_status == "En cours":
                    # Mettre en pause
                    new_status = "En pause"
                    event_type = "Pause"
                    message = "Intervention mise en pause"
                else:
                    # Démarrer
                    new_status = "En cours"
                    event_type = "Démarrage"
                    message = "Intervention démarrée"

                    # Mettre à jour la date de début réel si pas encore définie
                    cursor.execute("""
                        UPDATE maintenance_interventions
                        SET actual_start_date = COALESCE(actual_start_date, datetime('now'))
                        WHERE id = ?
                    """, (self.current_intervention,))

                # Mettre à jour le statut
                cursor.execute("""
                    UPDATE maintenance_interventions
                    SET status = ?, last_updated = datetime('now')
                    WHERE id = ?
                """, (new_status, self.current_intervention))

                # Ajouter à l'historique
                cursor.execute("""
                    INSERT INTO intervention_history
                    (intervention_id, event_date, event_type, description, user_name)
                    VALUES (?, datetime('now'), ?, ?, ?)
                """, (self.current_intervention, event_type, message, "Système"))

                self.db.conn.commit()

                self.refresh_data()
                notify_data_changed('maintenance')

                QMessageBox.information(self, "Succès", message)

            except Exception as e:
                self.db.conn.rollback()
                QMessageBox.critical(self, "Erreur", f"Erreur lors de la mise à jour : {str(e)}")

    def complete_intervention(self):
        """Termine l'intervention"""
        if self.current_intervention:
            from gui.complete_intervention_dialog import CompleteInterventionDialog
            dialog = CompleteInterventionDialog(self.db, self, self.current_intervention)
            if dialog.exec_() == QDialog.Accepted:
                self.refresh_data()
                notify_data_changed('maintenance')

    def delete_intervention(self):
        """Supprime l'intervention sélectionnée"""
        if self.current_intervention:
            reply = QMessageBox.question(
                self, "Confirmation",
                "Êtes-vous sûr de vouloir supprimer cette intervention ?",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                try:
                    cursor = self.db.cursor

                    # Supprimer l'historique
                    cursor.execute("DELETE FROM intervention_history WHERE intervention_id = ?",
                                 (self.current_intervention,))

                    # Supprimer les pièces utilisées
                    cursor.execute("DELETE FROM intervention_parts WHERE intervention_id = ?",
                                 (self.current_intervention,))

                    # Supprimer l'intervention
                    cursor.execute("DELETE FROM maintenance_interventions WHERE id = ?",
                                 (self.current_intervention,))

                    self.db.conn.commit()

                    self.refresh_data()
                    notify_data_changed('maintenance')

                    QMessageBox.information(self, "Succès", "Intervention supprimée avec succès.")

                except Exception as e:
                    self.db.conn.rollback()
                    QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression : {str(e)}")

    def create_work_order(self):
        """Crée un nouveau bon de travail"""
        from gui.work_order_dialog_pro import WorkOrderDialogPro
        dialog = WorkOrderDialogPro(self.db, self)
        if dialog.exec_() == QDialog.Accepted:
            self.refresh_data()
            notify_data_changed('maintenance')
            self.work_order_created.emit(dialog.work_order_id)

    def edit_work_order(self):
        """Modifie le bon de travail sélectionné"""
        if self.current_work_order:
            from gui.work_order_dialog_pro import WorkOrderDialogPro
            dialog = WorkOrderDialogPro(self.db, self, self.current_work_order)
            if dialog.exec_() == QDialog.Accepted:
                self.refresh_data()
                notify_data_changed('maintenance')

    def schedule_intervention(self):
        """Planifie une nouvelle intervention"""
        from gui.schedule_intervention_dialog import ScheduleInterventionDialog
        dialog = ScheduleInterventionDialog(self.db, self)
        if dialog.exec_() == QDialog.Accepted:
            self.refresh_data()
            notify_data_changed('maintenance')

    def add_part_to_intervention(self):
        """Ajoute une pièce à l'intervention"""
        if self.current_intervention:
            from gui.add_part_dialog import AddPartDialog
            dialog = AddPartDialog(self.db, self, self.current_intervention)
            if dialog.exec_() == QDialog.Accepted:
                self.load_intervention_parts(self.current_intervention)

    def refresh_data(self):
        """Actualise toutes les données (pour le système d'actualisation automatique)"""
        self.load_interventions()
        self.load_work_orders()
        self.load_daily_interventions()

        # Recharger les détails si une intervention est sélectionnée
        if self.current_intervention:
            self.load_intervention_details(self.current_intervention)