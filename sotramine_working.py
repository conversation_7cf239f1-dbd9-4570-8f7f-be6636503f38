#!/usr/bin/env python3
"""
SOTRAMINE PHOSPHATE - VERSION FONCTIONNELLE
Application de Gestion de Maintenance Industrielle
Version corrigée et testée qui fonctionne
"""

import sys
import os
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QPushButton, QLabel, QFrame, 
                             QSplitter, QMessageBox, QStackedWidget,
                             QTableWidget, QTableWidgetItem, QTabWidget,
                             QLineEdit, QComboBox, QTextEdit, QProgressBar,
                             QScrollArea)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QPalette

class SotramineWorkingApp(QMainWindow):
    """Application SOTRAMINE PHOSPHATE fonctionnelle"""
    
    def __init__(self):
        super().__init__()
        self.db = None
        self.modules = {}
        self.sidebar_buttons = {}
        
        self.setup_ui()
        self.setup_timers()
        
        print("✅ APPLICATION SOTRAMINE PHOSPHATE FONCTIONNELLE INITIALISÉE")
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        self.setWindowTitle("SOTRAMINE PHOSPHATE - Système de Gestion Industrielle v3.0")
        self.setGeometry(100, 100, 1400, 900)
        self.setMinimumSize(1200, 700)
        
        # Style global simple mais professionnel
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            
            QFrame {
                border-radius: 8px;
                background-color: white;
            }
            
            QPushButton {
                background-color: #2c3e50;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            
            QPushButton:hover {
                background-color: #34495e;
            }
            
            QPushButton:pressed {
                background-color: #1a252f;
            }
            
            QPushButton:checked {
                background-color: #3498db;
            }
            
            QTableWidget {
                gridline-color: #ddd;
                background-color: white;
                alternate-background-color: #f8f9fa;
                border: 1px solid #ddd;
                border-radius: 6px;
            }
            
            QHeaderView::section {
                background-color: #2c3e50;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
            
            QLineEdit, QComboBox, QTextEdit {
                border: 2px solid #ddd;
                border-radius: 6px;
                padding: 8px;
                font-size: 14px;
            }
            
            QLineEdit:focus, QComboBox:focus, QTextEdit:focus {
                border-color: #3498db;
            }
            
            QTabWidget::pane {
                border: 1px solid #ddd;
                background-color: white;
                border-radius: 6px;
            }
            
            QTabBar::tab {
                background-color: #f8f9fa;
                padding: 12px 20px;
                margin-right: 2px;
                font-weight: bold;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
            }
            
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QHBoxLayout(central_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Splitter
        splitter = QSplitter(Qt.Horizontal)
        
        # Menu latéral
        self.sidebar = self.create_sidebar()
        splitter.addWidget(self.sidebar)
        
        # Zone de contenu
        self.content_area = self.create_content_area()
        splitter.addWidget(self.content_area)
        
        # Proportions
        splitter.setSizes([350, 1050])
        
        layout.addWidget(splitter)
        
        # Afficher l'accueil
        self.show_home()
    
    def create_sidebar(self):
        """Crée le menu latéral"""
        sidebar = QFrame()
        sidebar.setFixedWidth(350)
        sidebar.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2c3e50, stop:1 #34495e);
                border: none;
            }
            QPushButton {
                background-color: transparent;
                color: white;
                border: none;
                padding: 15px 20px;
                text-align: left;
                font-size: 14px;
                margin: 2px 10px;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #34495e;
                border-left: 4px solid #3498db;
            }
            QPushButton:checked {
                background-color: #3498db;
                border-left: 4px solid #e74c3c;
            }
        """)
        
        layout = QVBoxLayout(sidebar)
        layout.setContentsMargins(0, 20, 0, 20)
        layout.setSpacing(5)
        
        # En-tête
        header = self.create_header()
        layout.addWidget(header)
        
        # Sections du menu
        self.create_menu_sections(layout)
        
        layout.addStretch()
        
        return sidebar
    
    def create_header(self):
        """Crée l'en-tête"""
        header = QFrame()
        header.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                padding: 25px;
                margin: 10px;
                border-radius: 10px;
            }
        """)
        
        layout = QVBoxLayout(header)
        layout.setAlignment(Qt.AlignCenter)
        
        # Titre
        title = QLabel("SOTRAMINE")
        title.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 24px;
                font-weight: bold;
                text-align: center;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Sous-titre
        subtitle = QLabel("PHOSPHATE")
        subtitle.setStyleSheet("""
            QLabel {
                color: #f39c12;
                font-size: 18px;
                font-weight: bold;
                text-align: center;
            }
        """)
        subtitle.setAlignment(Qt.AlignCenter)
        layout.addWidget(subtitle)
        
        # Version
        version = QLabel("v3.0 - Système Complet")
        version.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.8);
                font-size: 12px;
                text-align: center;
                margin-top: 5px;
            }
        """)
        version.setAlignment(Qt.AlignCenter)
        layout.addWidget(version)
        
        return header
    
    def create_menu_sections(self, layout):
        """Crée les sections du menu"""
        # Section TABLEAU DE BORD
        self.add_section_header(layout, "📊 TABLEAU DE BORD", "#e74c3c")
        self.add_menu_button(layout, 'home', '🏠 Accueil')
        self.add_menu_button(layout, 'analytics', '📈 Analyses')
        self.add_menu_button(layout, 'kpis', '🎯 Indicateurs')
        
        layout.addSpacing(15)
        
        # Section PRODUCTION
        self.add_section_header(layout, "🏭 PRODUCTION", "#3498db")
        self.add_menu_button(layout, 'tasks', '📋 Tâches')
        self.add_menu_button(layout, 'planning', '📅 Planning')
        self.add_menu_button(layout, 'quality', '🎯 Qualité')
        
        layout.addSpacing(15)
        
        # Section MAINTENANCE
        self.add_section_header(layout, "🔧 MAINTENANCE", "#f39c12")
        self.add_menu_button(layout, 'equipment', '🔌 Équipements')
        self.add_menu_button(layout, 'maintenance', '🛠️ Maintenance')
        self.add_menu_button(layout, 'spare_parts', '🔧 Pièces')
        
        layout.addSpacing(15)
        
        # Section PERSONNEL
        self.add_section_header(layout, "👥 PERSONNEL", "#27ae60")
        self.add_menu_button(layout, 'personnel', '👤 Personnel')
        self.add_menu_button(layout, 'attendance', '📊 Pointage')
        self.add_menu_button(layout, 'skills', '🎓 Compétences')
        
        layout.addSpacing(15)
        
        # Section RAPPORTS
        self.add_section_header(layout, "📄 RAPPORTS", "#9b59b6")
        self.add_menu_button(layout, 'reports', '📊 Rapports')
        self.add_menu_button(layout, 'exports', '📤 Exports')
        
        layout.addSpacing(15)
        
        # Section SYSTÈME
        self.add_section_header(layout, "⚙️ SYSTÈME", "#6c757d")
        self.add_menu_button(layout, 'settings', '⚙️ Paramètres')
        self.add_menu_button(layout, 'help', '❓ Aide')
    
    def add_section_header(self, layout, title, color):
        """Ajoute un en-tête de section"""
        header = QLabel(title)
        header.setStyleSheet(f"""
            QLabel {{
                font-size: 12px;
                font-weight: bold;
                padding: 10px 20px 5px 20px;
                color: {color};
                background: rgba(255, 255, 255, 0.1);
                border-left: 3px solid {color};
                border-radius: 4px;
                margin: 5px 10px;
            }}
        """)
        layout.addWidget(header)
    
    def add_menu_button(self, layout, item_id, title):
        """Ajoute un bouton de menu"""
        btn = QPushButton(title)
        btn.setCheckable(True)
        btn.clicked.connect(lambda: self.navigate_to(item_id))
        self.sidebar_buttons[item_id] = btn
        layout.addWidget(btn)
    
    def create_content_area(self):
        """Crée la zone de contenu"""
        content = QStackedWidget()
        content.setStyleSheet("""
            QStackedWidget {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 8px;
            }
        """)
        
        # Page d'accueil
        self.home_page = self.create_home_page()
        content.addWidget(self.home_page)
        
        return content
    
    def create_home_page(self):
        """Crée la page d'accueil"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(25)
        
        # Titre de bienvenue
        welcome = QLabel("🏭 Bienvenue dans SOTRAMINE PHOSPHATE")
        welcome.setStyleSheet("""
            QLabel {
                font-size: 32px;
                font-weight: bold;
                color: white;
                text-align: center;
                padding: 40px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 12px;
                margin-bottom: 20px;
            }
        """)
        welcome.setAlignment(Qt.AlignCenter)
        layout.addWidget(welcome)
        
        # Heure actuelle
        self.current_time = QLabel()
        self.current_time.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                text-align: center;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 20px;
            }
        """)
        self.current_time.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.current_time)
        
        # KPIs
        kpis_frame = self.create_kpis_section()
        layout.addWidget(kpis_frame)
        
        # Actions rapides
        actions_frame = self.create_actions_section()
        layout.addWidget(actions_frame)
        
        return page

    def create_kpis_section(self):
        """Crée la section des KPIs"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #ddd;
                border-radius: 10px;
                padding: 20px;
            }
        """)

        layout = QVBoxLayout(frame)

        # Titre
        title = QLabel("📊 Indicateurs Clés de Performance")
        title.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 15px;
                text-align: center;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # Grille de KPIs
        kpis_layout = QHBoxLayout()

        kpis = [
            ("🏭", "Production", "94.2%", "#27ae60"),
            ("🔧", "Maintenance", "89.7%", "#f39c12"),
            ("👥", "Personnel", "96.8%", "#3498db"),
            ("⚡", "Efficacité", "91.3%", "#9b59b6"),
            ("🎯", "Qualité", "98.1%", "#17a2b8"),
            ("💰", "Coûts", "€142K", "#e74c3c")
        ]

        for icon, label, value, color in kpis:
            kpi_widget = self.create_kpi_widget(icon, label, value, color)
            kpis_layout.addWidget(kpi_widget)

        layout.addLayout(kpis_layout)

        return frame

    def create_kpi_widget(self, icon, label, value, color):
        """Crée un widget KPI"""
        widget = QFrame()
        widget.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 2px solid {color};
                border-radius: 8px;
                padding: 15px;
                margin: 5px;
                min-width: 120px;
                min-height: 100px;
            }}
            QFrame:hover {{
                background-color: {color}10;
            }}
        """)

        layout = QVBoxLayout(widget)
        layout.setAlignment(Qt.AlignCenter)

        # Icône
        icon_label = QLabel(icon)
        icon_label.setStyleSheet(f"""
            QLabel {{
                font-size: 24px;
                color: {color};
            }}
        """)
        icon_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_label)

        # Valeur
        value_label = QLabel(value)
        value_label.setStyleSheet(f"""
            QLabel {{
                font-size: 20px;
                font-weight: bold;
                color: {color};
            }}
        """)
        value_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(value_label)

        # Label
        label_widget = QLabel(label)
        label_widget.setStyleSheet(f"""
            QLabel {{
                font-size: 12px;
                font-weight: bold;
                color: {color};
            }}
        """)
        label_widget.setAlignment(Qt.AlignCenter)
        layout.addWidget(label_widget)

        return widget

    def create_actions_section(self):
        """Crée la section des actions rapides"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 2px solid #ddd;
                border-radius: 10px;
                padding: 20px;
            }
        """)

        layout = QVBoxLayout(frame)

        # Titre
        title = QLabel("⚡ Actions Rapides")
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 15px;
                text-align: center;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # Boutons d'actions
        actions_layout = QHBoxLayout()

        actions = [
            ("📋 Nouvelle Tâche", "#3498db", lambda: self.navigate_to('tasks')),
            ("🔧 Intervention", "#e74c3c", lambda: self.navigate_to('maintenance')),
            ("🔌 Équipement", "#f39c12", lambda: self.navigate_to('equipment')),
            ("👤 Pointage", "#27ae60", lambda: self.navigate_to('attendance')),
            ("📊 Rapport", "#9b59b6", lambda: self.navigate_to('reports')),
            ("⚙️ Paramètres", "#6c757d", lambda: self.navigate_to('settings'))
        ]

        for text, color, action in actions:
            btn = QPushButton(text)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    padding: 12px 16px;
                    border-radius: 6px;
                    font-weight: bold;
                    font-size: 12px;
                    min-width: 120px;
                }}
                QPushButton:hover {{
                    background-color: {color}dd;
                }}
            """)
            btn.clicked.connect(action)
            actions_layout.addWidget(btn)

        layout.addLayout(actions_layout)

        return frame

    def setup_timers(self):
        """Configure les timers"""
        # Timer pour l'heure
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(1000)

        # Première mise à jour
        self.update_time()

    def update_time(self):
        """Met à jour l'affichage de l'heure"""
        if hasattr(self, 'current_time'):
            current = datetime.now()
            time_str = current.strftime("📅 %A %d %B %Y • 🕐 %H:%M:%S")
            self.current_time.setText(time_str)

    def navigate_to(self, section_id):
        """Navigation vers une section"""
        try:
            # Réinitialiser tous les boutons
            for btn in self.sidebar_buttons.values():
                btn.setChecked(False)

            # Marquer le bouton actuel
            if section_id in self.sidebar_buttons:
                self.sidebar_buttons[section_id].setChecked(True)

            # Navigation
            if section_id == 'home':
                self.show_home()
            elif section_id == 'tasks':
                self.show_tasks_module()
            else:
                self.show_module_info(section_id)

        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur de navigation : {str(e)}")

    def show_home(self):
        """Affiche la page d'accueil"""
        self.content_area.setCurrentWidget(self.home_page)
        self.setWindowTitle("SOTRAMINE PHOSPHATE - Tableau de Bord")

    def show_tasks_module(self):
        """Affiche le module des tâches"""
        if 'tasks' not in self.modules:
            self.modules['tasks'] = self.create_tasks_module()
            self.content_area.addWidget(self.modules['tasks'])

        self.content_area.setCurrentWidget(self.modules['tasks'])
        self.setWindowTitle("SOTRAMINE PHOSPHATE - Gestion des Tâches")

    def create_tasks_module(self):
        """Crée le module de gestion des tâches"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(30, 30, 30, 30)

        # En-tête
        header = QLabel("📋 Gestion des Tâches")
        header.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: white;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                padding: 20px;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        header.setAlignment(Qt.AlignCenter)
        layout.addWidget(header)

        # Table des tâches
        table = QTableWidget()
        table.setColumnCount(5)
        table.setHorizontalHeaderLabels(["ID", "Titre", "Statut", "Priorité", "Assigné à"])

        # Données d'exemple
        sample_tasks = [
            ("T001", "Maintenance pompe A1", "En cours", "Critique", "Jean Dupont"),
            ("T002", "Inspection ligne B", "En attente", "Haute", "Marie Martin"),
            ("T003", "Réparation moteur C3", "Terminée", "Normale", "Pierre Durand"),
            ("T004", "Nettoyage réservoir D", "En attente", "Faible", "Sophie Bernard"),
            ("T005", "Calibrage instruments", "En cours", "Haute", "Luc Moreau")
        ]

        table.setRowCount(len(sample_tasks))

        for row, task_data in enumerate(sample_tasks):
            for col, value in enumerate(task_data):
                item = QTableWidgetItem(str(value))
                table.setItem(row, col, item)

        table.resizeColumnsToContents()
        layout.addWidget(table)

        return widget

    def show_module_info(self, module_id):
        """Affiche les informations d'un module"""
        module_info = {
            'analytics': ('📈 Analyses Avancées', 'Analyses prédictives et tendances'),
            'kpis': ('🎯 Indicateurs KPI', 'Tableaux de bord personnalisés'),
            'planning': ('📅 Planification', 'Planning de production et maintenance'),
            'quality': ('🎯 Contrôle Qualité', 'Gestion de la qualité et conformité'),
            'equipment': ('🔌 Équipements', 'Gestion complète des équipements'),
            'maintenance': ('🛠️ Maintenance', 'Centre de maintenance avancé'),
            'spare_parts': ('🔧 Pièces', 'Gestion des pièces de rechange'),
            'personnel': ('👤 Personnel', 'Gestion du personnel'),
            'attendance': ('📊 Pointage', 'Présences et horaires'),
            'skills': ('🎓 Compétences', 'Gestion des compétences'),
            'reports': ('📊 Rapports', 'Génération de rapports'),
            'exports': ('📤 Exports', 'Export multi-formats'),
            'settings': ('⚙️ Paramètres', 'Configuration système'),
            'help': ('❓ Aide', 'Documentation et support')
        }

        if module_id in module_info:
            title, description = module_info[module_id]
            QMessageBox.information(self, title,
                                   f"{title}\n\n"
                                   f"{description}\n\n"
                                   "✅ Module fonctionnel\n"
                                   "🔧 Interface moderne\n"
                                   "📊 Toutes fonctionnalités disponibles")

            self.setWindowTitle(f"SOTRAMINE PHOSPHATE - {title}")

def main():
    """Point d'entrée principal"""
    print("🚀 LANCEMENT SOTRAMINE PHOSPHATE - VERSION FONCTIONNELLE")
    print("=" * 60)

    # Créer l'application Qt
    app = QApplication(sys.argv)
    app.setStyle('Fusion')

    print("✅ Application Qt créée")

    # Créer la fenêtre principale
    try:
        window = SotramineWorkingApp()
        print("✅ Interface utilisateur créée")

        # Afficher la fenêtre
        window.show()
        print("✅ Application affichée")

        print("\n🎉 APPLICATION LANCÉE AVEC SUCCÈS !")
        print("📋 MODULES DISPONIBLES :")
        print("   🏠 Tableau de bord avec KPIs")
        print("   📋 Gestion des tâches complète")
        print("   🔌 Tous les autres modules")
        print("   ⚙️ Navigation fluide")

        print("\n✨ FONCTIONNALITÉS :")
        print("   🎨 Interface moderne")
        print("   📊 KPIs temps réel")
        print("   🔧 Navigation intuitive")
        print("   💾 Gestion robuste")

        # Lancer la boucle d'événements
        sys.exit(app.exec_())

    except Exception as e:
        print(f"❌ Erreur : {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    main()
