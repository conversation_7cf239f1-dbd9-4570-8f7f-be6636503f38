from PyQt5.QtWidgets import (QMainWindow, QW<PERSON>t, QVBoxLayout, QHBoxLayout,
                             QPushButton, QLabel, QLineEdit, QComboBox,
                             QTreeWidget, QTreeWidgetItem, QDialog, QMessageBox,
                             QDateEdit, QTextEdit, QSpinBox, QFrame, QSplitter,
                             QStyleFactory, QApplication, QMenu, QCalendarWidget,
                             QTabWidget, QScrollArea, QShortcut, QDialogButtonBox,
                             QDoubleSpinBox, QTableWidget, QToolBar, QAction, QHeaderView,
                             QTableWidgetItem, QDateTimeEdit, QGridLayout, QGroupBox,
                             QFormLayout, QCheckBox, QStatusBar, QStackedWidget)
from PyQt5.QtCore import Qt, QDate, QSize, QDateTime, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import (QIcon, QFont, QPalette, QColor, QPainter, QBrush, QPen,
                        QKeySequence, QTextDocument, QPageLayout, QPixmap, QTextCursor)
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
from gui.style import APP_STYLE, get_status_color, get_priority_color, get_attendance_status_color
from gui.maintenance_history import MaintenanceHistoryView
from gui.maintenance_menu import MaintenanceMenu
from gui.split_task_dialog import SplitTaskDialog
from gui.equipment_view_tab import EquipmentViewTab
from gui.notification import NotificationManager
from gui.help_system import HelpSystem
from gui.favorites import FavoritesManager, FavoriteButton
from gui.recurring_tasks import RecurringTasksView
from gui.spare_parts_manager import SparePartsManagerDialog
from gui.action_manager import ActionManager
from gui.sidebar_menu import SidebarMenu
from gui.contextual_toolbar import ContextualToolbar
from database import Database
from export.excel_export import ExcelExporter
from utils.ui_helpers import create_standard_dialog_buttons, create_crud_buttons, create_standard_button, add_tabs

# Activation des graphiques
HAS_CHARTS = False
try:
    from PyQt5.QtChart import QChart, QChartView, QPieSeries, QBarSeries, QBarSet, QBarCategoryAxis, QValueAxis
    HAS_CHARTS = True
except ImportError:
    print("Module PyQt5.QtChart non disponible. Les graphiques seront désactivés.")

import os
from datetime import datetime, timedelta

class TaskDialog(QDialog):
    def __init__(self, parent=None, task=None, categories=None, db=None):
        super().__init__(parent)
        self.task = task
        self.categories = categories or []
        self.parent = parent
        self.db = db
        self.favorite_button = None
        self.setup_ui()

    def add_favorite_button(self, button):
        """Ajoute un bouton de favori à la boîte de dialogue"""
        self.favorite_button = button

        # Ajouter le bouton à côté du titre
        title_layout = self.findChild(QHBoxLayout, "title_layout")
        if title_layout:
            # Configurer le bouton
            button.setFixedSize(32, 32)
            button.setStyleSheet("""
                QPushButton {
                    background-color: transparent;
                    border: none;
                }
            """)

            # Ajouter le bouton après le titre
            title_layout.insertWidget(1, button)
        else:
            # Si le layout du titre n'est pas trouvé, ajouter le bouton au layout principal
            layout = self.layout()
            if layout:
                layout.addWidget(button)

    def setup_ui(self):
        self.setWindowTitle("✏️ " + ("Nouvelle tâche" if not self.task else "Modifier la tâche"))
        self.setWindowState(Qt.WindowMaximized)

        # Layout principal unique pour la boîte de dialogue
        main_layout = QVBoxLayout(self)

        # Créer un layout pour le titre
        title_layout = QHBoxLayout()
        title_layout.setObjectName("title_layout")
        title_label = QLabel(self.windowTitle())
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #3498db;")
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        main_layout.addLayout(title_layout)

        # Widget de défilement
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        container = QWidget()
        container_layout = QVBoxLayout(container)
        container_layout.setSpacing(15)
        scroll_area.setWidget(container)
        main_layout.addWidget(scroll_area)

        # Groupe Informations principales
        main_group = QGroupBox("📝 Informations principales")
        main_group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                border: 2px solid #dcdde1;
                border-radius: 8px;
                margin-top: 15px;
                padding: 15px;
            }
            QGroupBox::title {
                background-color: #f5f6fa;
                padding: 0 10px;
            }
        """)
        form_layout = QFormLayout()
        form_layout.setSpacing(15)
        # Titre
        self.title_edit = QLineEdit()
        if self.task:
            self.title_edit.setText(self.task[2])
        self.title_edit.setPlaceholderText("Entrez le titre de la tâche")
        form_layout.addRow("Titre:", self.title_edit)
        # Description
        self.desc_edit = QTextEdit()
        if self.task:
            self.desc_edit.setText(self.task[3])
        self.desc_edit.setPlaceholderText("Décrivez la tâche en détail")
        self.desc_edit.setMinimumHeight(100)
        form_layout.addRow("Description:", self.desc_edit)
        # Catégorie
        self.category_combo = QComboBox()
        for category in self.categories:
            color = category[2] if len(category) > 2 else "#7f8c8d"
            self.category_combo.addItem(category[1], category[0])
            index = self.category_combo.count() - 1
            self.category_combo.setItemData(index, QColor(color), Qt.TextColorRole)
        if self.task:
            index = self.category_combo.findData(self.task[1])
            if index >= 0:
                self.category_combo.setCurrentIndex(index)
        form_layout.addRow("Catégorie:", self.category_combo)
        main_group.setLayout(form_layout)
        container_layout.addWidget(main_group)

        # Layout horizontal pour Planning et État
        horizontal_layout = QHBoxLayout()
        # ... (le reste du code pour ajouter les groupes Planning, État, etc. reste inchangé)
        # À la fin, ajouter horizontal_layout à container_layout
        container_layout.addLayout(horizontal_layout)
        # ... (ajouter les autres groupes comme Équipement, Récurrence, boutons, etc. à container_layout)
        # Les signaux et la mise à jour de la date de fin restent inchangés

        # Créer un layout horizontal pour les groupes Planning et État
        horizontal_layout = QHBoxLayout()

        # Groupe Planning
        planning_group = QGroupBox("📅 Planning")
        planning_group.setStyleSheet(main_group.styleSheet())
        planning_layout = QFormLayout()
        planning_layout.setSpacing(15)

        # Date et heure de début
        self.start_date_edit = QDateTimeEdit()
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setDateTime(QDateTime.currentDateTime())
        if self.task and self.task[4]:
            start_date = QDateTime.fromString(self.task[4], 'yyyy-MM-dd HH:mm')
            self.start_date_edit.setDateTime(start_date)
        self.start_date_edit.setDisplayFormat("dd/MM/yyyy HH:mm")
        planning_layout.addRow("Début:", self.start_date_edit)

        # Durée estimée
        self.duration_spin = QDoubleSpinBox()
        self.duration_spin.setRange(0, 1000)
        self.duration_spin.setSuffix(" heures")
        self.duration_spin.setValue(0)
        if self.task and self.task[8]:
            try:
                duration = float(self.task[8]) if self.task[8] is not None else 0.0
            except (ValueError, TypeError):
                duration = 0.0
            self.duration_spin.setValue(duration)
        planning_layout.addRow("Durée estimée:", self.duration_spin)

        # Date de fin calculée
        self.end_date_label = QLabel("Fin estimée:")
        self.end_date_value = QLabel()
        self.end_date_value.setStyleSheet("color: #7f8c8d;")
        planning_layout.addRow(self.end_date_label, self.end_date_value)

        planning_group.setLayout(planning_layout)
        horizontal_layout.addWidget(planning_group)

        # Groupe État
        status_group = QGroupBox("🎯 État")
        status_group.setStyleSheet(main_group.styleSheet())
        status_layout = QFormLayout()
        status_layout.setSpacing(15)

        # Liste des personnes assignées avec sélection multiple
        self.assigned_list = QTreeWidget()
        self.assigned_list.setHeaderLabels(["Nom", "Rôle"])
        self.assigned_list.setSelectionMode(QTreeWidget.MultiSelection)
        self.assigned_list.setStyleSheet("""
            QTreeWidget {
                border: 1px solid #dcdde1;
                border-radius: 4px;
                padding: 5px;
                background-color: white;
                min-height: 100px;
            }
            QTreeWidget::item {
                padding: 5px;
            }
            QTreeWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)

        # Remplir la liste des personnes
        db_to_use = self.db if self.db else (self.parent.db if self.parent and hasattr(self.parent, 'db') else None)
        if db_to_use:
            persons = db_to_use.get_all_persons()
            for person in persons:
                item = QTreeWidgetItem(self.assigned_list)
                item.setText(0, person[1])  # Nom
                item.setText(1, person[2])  # Rôle
                item.setData(0, Qt.UserRole, person[0])  # ID

                # Sélectionner les personnes déjà assignées
                if self.task:
                    assignees = db_to_use.get_task_assignees(self.task[0])
                    if any(assignee[0] == person[0] for assignee in assignees):
                        item.setSelected(True)

        status_layout.addRow("Personnes assignées:", self.assigned_list)

        # Priorité
        self.priority_combo = QComboBox()
        self.priority_combo.addItems(['basse', 'moyenne', 'haute'])
        self.priority_combo.setItemData(0, QColor("#27ae60"), Qt.TextColorRole)  # Vert pour basse
        self.priority_combo.setItemData(1, QColor("#f39c12"), Qt.TextColorRole)  # Orange pour moyenne
        self.priority_combo.setItemData(2, QColor("#e74c3c"), Qt.TextColorRole)  # Rouge pour haute
        if self.task:
            index = self.priority_combo.findText(self.task[5])
            if index >= 0:
                self.priority_combo.setCurrentIndex(index)
        status_layout.addRow("Priorité:", self.priority_combo)

        # Statut
        self.status_combo = QComboBox()
        self.status_combo.addItems(['À faire', 'En cours', 'En attente', 'Terminée'])
        self.status_combo.setItemData(0, QColor("#e74c3c"), Qt.TextColorRole)  # Rouge pour À faire
        self.status_combo.setItemData(1, QColor("#f39c12"), Qt.TextColorRole)  # Orange pour En cours
        self.status_combo.setItemData(2, QColor("#3498db"), Qt.TextColorRole)  # Bleu pour En attente
        self.status_combo.setItemData(3, QColor("#27ae60"), Qt.TextColorRole)  # Vert pour Terminée
        self.status_combo.currentIndexChanged.connect(self.toggle_waiting_reason)
        if self.task:
            index = self.status_combo.findText(self.task[6])
            if index >= 0:
                self.status_combo.setCurrentIndex(index)
        status_layout.addRow("Statut:", self.status_combo)

        # Raison d'attente (visible uniquement si le statut est "En attente")
        self.waiting_reason_label = QLabel("Raison d'attente:")
        self.waiting_reason_edit = QTextEdit()
        self.waiting_reason_edit.setMaximumHeight(80)
        self.waiting_reason_edit.setPlaceholderText("Précisez la raison pour laquelle cette tâche est en attente...")
        if self.task and self.task[7]:
            self.waiting_reason_edit.setText(str(self.task[7]))
        status_layout.addRow(self.waiting_reason_label, self.waiting_reason_edit)

        # Masquer par défaut la raison d'attente
        self.toggle_waiting_reason(self.status_combo.currentIndex())

        status_group.setLayout(status_layout)
        horizontal_layout.addWidget(status_group)

        # Ajouter le layout horizontal au layout principal
        container_layout.addLayout(horizontal_layout)

        # Groupe Équipement
        equipment_group = QGroupBox("🔌 Équipement associé")
        equipment_group.setStyleSheet(main_group.styleSheet())
        equipment_layout = QFormLayout()
        equipment_layout.setSpacing(15)

        # Checkbox pour associer un équipement
        self.has_equipment_check = QCheckBox("Associer à un équipement")
        self.has_equipment_check.stateChanged.connect(self.toggle_equipment_selection)
        equipment_layout.addRow("", self.has_equipment_check)

        # Sélection de l'équipement
        self.equipment_combo = QComboBox()
        self.equipment_combo.setEnabled(False)

        # Charger la liste des équipements
        db_to_use = self.db if self.db else (self.parent.db if self.parent and hasattr(self.parent, 'db') else None)
        if db_to_use:
            equipment_list = db_to_use.get_all_equipment()
            self.equipment_combo.addItem("Aucun", None)
            for equipment in equipment_list:
                self.equipment_combo.addItem(equipment[1], equipment[0])  # name, id

        equipment_layout.addRow("Équipement:", self.equipment_combo)

        # Initialiser les valeurs si c'est une tâche existante
        if self.task and len(self.task) > 14 and self.task[15]:  # equipment_id
            self.has_equipment_check.setChecked(True)
            index = self.equipment_combo.findData(self.task[15])
            if index >= 0:
                self.equipment_combo.setCurrentIndex(index)

        equipment_group.setLayout(equipment_layout)
        container_layout.addWidget(equipment_group)

        # Groupe Récurrence
        recurrence_group = QGroupBox("🔄 Récurrence")
        recurrence_group.setStyleSheet(main_group.styleSheet())
        recurrence_layout = QFormLayout()
        recurrence_layout.setSpacing(15)

        # Checkbox pour activer la récurrence
        self.is_recurring_check = QCheckBox("Tâche récurrente")
        self.is_recurring_check.stateChanged.connect(self.toggle_recurrence_options)
        recurrence_layout.addRow("", self.is_recurring_check)

        # Type de récurrence
        self.recurrence_type_combo = QComboBox()
        self.recurrence_type_combo.addItem("Quotidienne", "daily")
        self.recurrence_type_combo.addItem("Hebdomadaire", "weekly")
        self.recurrence_type_combo.addItem("Mensuelle", "monthly")
        self.recurrence_type_combo.addItem("Annuelle", "yearly")
        self.recurrence_type_combo.setEnabled(False)
        recurrence_layout.addRow("Fréquence:", self.recurrence_type_combo)

        # Date de fin de récurrence
        self.recurrence_end_date = QDateEdit()
        self.recurrence_end_date.setCalendarPopup(True)
        self.recurrence_end_date.setDate(QDate.currentDate().addYears(1))
        self.recurrence_end_date.setEnabled(False)
        recurrence_layout.addRow("Date de fin:", self.recurrence_end_date)

        # Initialiser les valeurs si c'est une tâche existante
        if self.task and len(self.task) > 9 and self.task[9]:  # is_recurring
            self.is_recurring_check.setChecked(True)

            # Type de récurrence
            if self.task[10]:  # recurrence_type
                index = self.recurrence_type_combo.findData(self.task[10])
                if index >= 0:
                    self.recurrence_type_combo.setCurrentIndex(index)

            # Date de fin
            if self.task[11]:  # recurrence_end_date
                end_date = QDate.fromString(self.task[11], 'yyyy-MM-dd')
                self.recurrence_end_date.setDate(end_date)

        recurrence_group.setLayout(recurrence_layout)
        container_layout.addWidget(recurrence_group)

        # Boutons standard
        buttons = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        container_layout.addWidget(buttons)

        # Connecter les signaux pour le calcul automatique
        self.category_combo.currentIndexChanged.connect(self.update_end_date)
        self.start_date_edit.dateTimeChanged.connect(self.update_end_date)
        self.duration_spin.valueChanged.connect(self.update_end_date)

        # Mettre à jour la date de fin initiale
        self.update_end_date()

    def update_end_date(self):
        category_name = self.category_combo.currentText()
        if category_name == "Maintenance":
            start_date = self.start_date_edit.dateTime().toPyDateTime()
            duration_hours = self.duration_spin.value()

            # Calculer la date de fin (8h/jour)
            days = int(duration_hours / 8)
            remaining_hours = duration_hours % 8

            end_date = start_date + timedelta(days=days, hours=remaining_hours)
            self.end_date_value.setText(end_date.strftime("%d/%m/%Y %H:%M"))
            self.end_date_label.setVisible(True)
            self.end_date_value.setVisible(True)
        else:
            self.end_date_label.setVisible(False)
            self.end_date_value.setVisible(False)

    def toggle_recurrence_options(self, state):
        """Active ou désactive les options de récurrence en fonction de l'état de la case à cocher"""
        self.recurrence_type_combo.setEnabled(state)
        self.recurrence_end_date.setEnabled(state)

    def toggle_waiting_reason(self, index):
        is_waiting = self.status_combo.currentText() == "En attente"
        if hasattr(self, 'waiting_reason_label'):
            self.waiting_reason_label.setVisible(is_waiting)
        if hasattr(self, 'waiting_reason_edit'):
            self.waiting_reason_edit.setVisible(is_waiting)

    def toggle_equipment_selection(self, state):
        """Active ou désactive la sélection d'équipement en fonction de l'état de la case à cocher"""
        self.equipment_combo.setEnabled(state)
        if not state:
            self.equipment_combo.setCurrentIndex(0)  # Réinitialiser à "Aucun"

    def update_end_date(self):
        category_name = self.category_combo.currentText()
        if category_name == "Maintenance":
            start_date = self.start_date_edit.dateTime().toPyDateTime()
            duration_hours = self.duration_spin.value()

            # Calculer la date de fin (8h/jour)
            days = int(duration_hours / 8)
            remaining_hours = duration_hours % 8

            end_date = start_date + timedelta(days=days, hours=remaining_hours)
            self.end_date_value.setText(end_date.strftime("%d/%m/%Y %H:%M"))
            self.end_date_label.setVisible(True)
            self.end_date_value.setVisible(True)
        else:
            self.end_date_label.setVisible(False)
            self.end_date_value.setVisible(False)

    def get_task_data(self):
        # Récupérer les IDs des personnes sélectionnées
        assigned_to_ids = []
        for item in self.assigned_list.selectedItems():
            person_id = item.data(0, Qt.UserRole)
            assigned_to_ids.append(person_id)

        # Récupérer les informations de récurrence
        is_recurring = 1 if self.is_recurring_check.isChecked() else 0
        recurrence_type = self.recurrence_type_combo.currentData() if is_recurring else None
        recurrence_end_date = self.recurrence_end_date.date().toString('yyyy-MM-dd') if is_recurring else None



        # Récupérer la raison d'attente si le statut est "En attente"
        status = self.status_combo.currentText()
        waiting_reason = None
        if status == "En attente":
            waiting_reason = self.waiting_reason_edit.toPlainText()

        # Récupérer l'équipement associé
        equipment_id = None
        if self.has_equipment_check.isChecked():
            equipment_id = self.equipment_combo.currentData()

        return {
            'category_id': self.category_combo.currentData(),
            'title': self.title_edit.text(),
            'description': self.desc_edit.toPlainText(),
            'due_date': self.start_date_edit.dateTime().toString('yyyy-MM-dd HH:mm'),
            'priority': self.priority_combo.currentText(),
            'status': status,
            'waiting_reason': waiting_reason,
            'assigned_to_ids': assigned_to_ids,
            'estimated_time': self.duration_spin.value(),
            'is_recurring': is_recurring,
            'recurrence_type': recurrence_type,
            'recurrence_end_date': recurrence_end_date,
            'is_maintenance_checklist': 0,  # Valeur par défaut
            'checklist_template_id': None,  # Valeur par défaut
            'equipment_id': equipment_id    # ID de l'équipement associé
        }

class SubtaskDialog(QDialog):
    def __init__(self, parent=None, task_id=None):
        super().__init__(parent)
        self.task_id = task_id
        self.setup_ui()

    def setup_ui(self):
        self.setWindowTitle("Nouvelle sous-tâche")
        self.setMinimumWidth(400)
        layout = QVBoxLayout()
        layout.setSpacing(15)

        # Style des labels
        label_style = "QLabel { font-size: 12px; font-weight: bold; color: #2c3e50; }"

        # Titre
        title_label = QLabel("Titre:")
        title_label.setStyleSheet(label_style)
        layout.addWidget(title_label)
        self.title_edit = QLineEdit()
        self.title_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                background-color: white;
            }
            QLineEdit:focus {
                border: 2px solid #3498db;
            }
        """)
        layout.addWidget(self.title_edit)

        # Affectation
        assigned_label = QLabel("Affectation:")
        assigned_label.setStyleSheet(label_style)
        layout.addWidget(assigned_label)
        self.assigned_combo = QComboBox()
        self.assigned_combo.addItem("Non assigné", None)
        self.assigned_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                background-color: white;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: url(down_arrow.png);
                width: 12px;
                height: 12px;
            }
        """)
        # Charger les personnes depuis la base de données
        if self.parent() and self.parent().db:
            persons = self.parent().db.get_all_persons()
            for person in persons:
                self.assigned_combo.addItem(f"{person[1]} ({person[2]})", person[0])
        layout.addWidget(self.assigned_combo)

        # Délai prévu
        time_label = QLabel("Délai prévu (heures):")
        time_label.setStyleSheet(label_style)
        self.time_spin = QSpinBox()
        self.time_spin.setRange(0, 1000)
        self.time_spin.setStyleSheet("""
            QSpinBox {
                padding: 8px;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                background-color: white;
            }
            QSpinBox:focus {
                border: 2px solid #3498db;
            }
        """)
        layout.addWidget(self.time_spin)

        # Statut
        status_label = QLabel("Statut:")
        status_label.setStyleSheet(label_style)
        self.status_combo = QComboBox()
        self.status_combo.addItems(["À faire", "En cours", "Terminée"])
        self.status_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                background-color: white;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: url(down_arrow.png);
                width: 12px;
                height: 12px;
            }
        """)
        layout.addWidget(self.status_combo)

        # Boutons standard
        buttons_layout = create_standard_dialog_buttons(self, self.accept, self.reject)
        layout.addLayout(buttons_layout)

        self.setLayout(layout)

    def get_subtask_data(self):
        return {
            'title': self.title_edit.text().strip(),
            'status': self.status_combo.currentText(),
            'assigned_to_id': self.assigned_combo.currentData(),
            'estimated_time': self.time_spin.value()
        }

class PersonDialog(QDialog):
    def __init__(self, parent=None, person_data=None, db=None):
        super().__init__(parent)
        self.person_data = person_data
        self.db = db
        self.setup_ui()

    def setup_ui(self):
        self.setWindowTitle("Nouvelle personne" if not self.person_data else "Modifier la personne")
        self.setMinimumWidth(400)
        layout = QVBoxLayout()
        layout.setSpacing(15)

        # Style des labels
        label_style = "QLabel { font-size: 12px; font-weight: bold; color: #2c3e50; }"

        # Nom
        name_label = QLabel("Nom:")
        name_label.setStyleSheet(label_style)
        layout.addWidget(name_label)
        self.name_edit = QLineEdit()
        self.name_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                background-color: white;
            }
            QLineEdit:focus {
                border: 2px solid #3498db;
            }
        """)
        if self.person_data:
            self.name_edit.setText(self.person_data[1])
        layout.addWidget(self.name_edit)

        # Rôle
        role_label = QLabel("Rôle:")
        role_label.setStyleSheet(label_style)
        layout.addWidget(role_label)
        self.role_edit = QLineEdit()
        self.role_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                background-color: white;
            }
            QLineEdit:focus {
                border: 2px solid #3498db;
            }
        """)
        if self.person_data:
            self.role_edit.setText(self.person_data[2])
        layout.addWidget(self.role_edit)

        # Boutons standard
        buttons_layout = create_standard_dialog_buttons(self, self.accept, self.reject)
        layout.addLayout(buttons_layout)

        self.setLayout(layout)

    def get_person_data(self):
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "Erreur", "Le nom ne peut pas être vide")
            return None

        return {
            'name': self.name_edit.text().strip(),
            'role': self.role_edit.text().strip()
        }

class PersonManagerDialog(QDialog):
    def __init__(self, parent=None, db=None):
        super().__init__(parent)
        self.db = db
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        self.setWindowTitle("Gestion des personnes")
        self.setMinimumSize(600, 400)
        layout = QVBoxLayout()

        # Liste des personnes
        self.person_list = QTreeWidget()
        self.person_list.setHeaderLabels(["Nom", "Rôle"])
        self.person_list.setColumnWidth(0, 300)
        self.person_list.setColumnWidth(1, 250)
        self.person_list.setStyleSheet("""
            QTreeWidget {
                background-color: white;
                border: 1px solid #dcdde1;
                border-radius: 4px;
                padding: 5px;
            }
            QTreeWidget::item {
                padding: 5px;
            }
            QTreeWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #f1f2f6;
                padding: 5px;
                border: 1px solid #dcdde1;
                font-weight: bold;
            }
        """)
        layout.addWidget(self.person_list)

        # Boutons CRUD standard
        buttons_layout = create_crud_buttons(self, self.add_person, self.edit_person, self.delete_person)
        layout.addLayout(buttons_layout)
        self.setLayout(layout)

    def load_data(self):
        self.person_list.clear()
        persons = self.db.get_all_persons()
        for person in persons:
            item = QTreeWidgetItem(self.person_list)
            item.setText(0, person[1])  # Nom
            item.setText(1, person[2])  # Rôle
            item.setData(0, Qt.UserRole, person[0])  # ID

    def add_person(self):
        dialog = PersonDialog(self, db=self.db)
        if dialog.exec_():
            person_data = dialog.get_person_data()
            if person_data is None:
                return

            try:
                self.db.add_person(
                    name=person_data['name'],
                    role=person_data['role']
                )
                self.load_data()
                QMessageBox.information(self, "Succès", "Personne ajoutée avec succès")
            except Exception as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajout de la personne : {str(e)}")

    def edit_person(self):
        current_item = self.person_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "Attention", "Veuillez sélectionner une personne à modifier")
            return

        person_id = current_item.data(0, Qt.UserRole)
        person = self.db.get_person_by_id(person_id)

        dialog = PersonDialog(self, person_data=person, db=self.db)
        if dialog.exec_():
            person_data = dialog.get_person_data()
            if person_data is None:
                return

            try:
                self.db.update_person(
                    person_id=person_id,
                    name=person_data['name'],
                    role=person_data['role']
                )
                self.load_data()
                QMessageBox.information(self, "Succès", "Personne modifiée avec succès")
            except Exception as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de la modification de la personne : {str(e)}")

    def delete_person(self):
        current_item = self.person_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "Attention", "Veuillez sélectionner une personne à supprimer")
            return

        person_id = current_item.data(0, Qt.UserRole)

        reply = QMessageBox.question(
            self, 'Confirmation',
            "Êtes-vous sûr de vouloir supprimer cette personne ?\nLes tâches et sous-tâches assignées ne seront pas supprimées.",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                self.db.delete_person(person_id)
                self.load_data()
                QMessageBox.information(self, "Succès", "Personne supprimée avec succès")
            except Exception as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression de la personne : {str(e)}")

class CategoryDialog(QDialog):
    def __init__(self, parent=None, category_data=None, db=None):
        super().__init__(parent)
        self.category_data = category_data
        self.db = db
        self.setup_ui()

    def setup_ui(self):
        self.setWindowTitle("Nouvelle catégorie" if not self.category_data else "Modifier la catégorie")
        self.setMinimumWidth(400)
        layout = QVBoxLayout()
        layout.setSpacing(15)

        # Style des labels
        label_style = "QLabel { font-size: 12px; font-weight: bold; color: #2c3e50; }"

        # Nom
        name_label = QLabel("Nom:")
        name_label.setStyleSheet(label_style)
        layout.addWidget(name_label)
        self.name_edit = QLineEdit()
        self.name_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                background-color: white;
            }
            QLineEdit:focus {
                border: 2px solid #3498db;
            }
        """)
        if self.category_data:
            self.name_edit.setText(self.category_data[1])
        layout.addWidget(self.name_edit)

        # Couleur
        color_label = QLabel("Couleur:")
        color_label.setStyleSheet(label_style)
        layout.addWidget(color_label)
        self.color_combo = QComboBox()
        self.color_combo.addItems([
            "Rouge (#e74c3c)",
            "Orange (#e67e22)",
            "Jaune (#f1c40f)",
            "Vert (#2ecc71)",
            "Bleu (#3498db)",
            "Violet (#9b59b6)",
            "Rose (#e84393)",
            "Gris (#7f8c8d)"
        ])
        self.color_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                background-color: white;
            }
            QComboBox::drop-down {
                border: none;
            }
            QComboBox::down-arrow {
                image: url(down_arrow.png);
                width: 12px;
                height: 12px;
            }
        """)
        if self.category_data and len(self.category_data) > 2:
            color = self.category_data[2]
            for i in range(self.color_combo.count()):
                if color in self.color_combo.itemText(i):
                    self.color_combo.setCurrentIndex(i)
                    break
        layout.addWidget(self.color_combo)

        # Boutons standard
        buttons_layout = create_standard_dialog_buttons(self, self.accept, self.reject)
        layout.addLayout(buttons_layout)

        self.setLayout(layout)

    def get_category_data(self):
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "Erreur", "Le nom ne peut pas être vide")
            return None

        color_text = self.color_combo.currentText()
        color = color_text[color_text.find("#"):color_text.find(")")]

        return {
            'name': self.name_edit.text().strip(),
            'color': color
        }

class CategoryManagerDialog(QDialog):
    def __init__(self, parent=None, db=None):
        super().__init__(parent)
        self.db = db
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        self.setWindowTitle("Gestion des catégories")
        self.setMinimumSize(600, 400)
        layout = QVBoxLayout()

        # Liste des catégories
        self.category_list = QTreeWidget()
        self.category_list.setHeaderLabels(["Nom", "Couleur"])
        self.category_list.setColumnWidth(0, 300)
        self.category_list.setColumnWidth(1, 250)
        self.category_list.setStyleSheet("""
            QTreeWidget {
                background-color: white;
                border: 1px solid #dcdde1;
                border-radius: 4px;
                padding: 5px;
            }
            QTreeWidget::item {
                padding: 5px;
            }
            QTreeWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #f1f2f6;
                padding: 5px;
                border: 1px solid #dcdde1;
                font-weight: bold;
            }
        """)
        layout.addWidget(self.category_list)

        # Boutons CRUD standard
        buttons_layout = create_crud_buttons(self, self.add_category, self.edit_category, self.delete_category)
        layout.addLayout(buttons_layout)
        self.setLayout(layout)

    def load_data(self):
        self.category_list.clear()
        categories = self.db.get_all_categories()
        for category in categories:
            item = QTreeWidgetItem(self.category_list)
            item.setText(0, category[1])  # Nom
            item.setText(1, category[2] if len(category) > 2 else "")  # Couleur
            item.setData(0, Qt.UserRole, category[0])  # ID
            if len(category) > 2 and category[2]:
                item.setForeground(1, QColor(category[2]))

    def add_category(self):
        dialog = CategoryDialog(self, db=self.db)
        if dialog.exec_():
            category_data = dialog.get_category_data()
            if category_data is None:
                return

            try:
                self.db.add_category(
                    name=category_data['name'],
                    color=category_data['color']
                )
                self.load_data()
                QMessageBox.information(self, "Succès", "Catégorie ajoutée avec succès")
            except Exception as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajout de la catégorie : {str(e)}")

    def edit_category(self):
        current_item = self.category_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "Attention", "Veuillez sélectionner une catégorie à modifier")
            return

        category_id = current_item.data(0, Qt.UserRole)
        category = self.db.get_category_by_id(category_id)

        dialog = CategoryDialog(self, category_data=category, db=self.db)
        if dialog.exec_():
            category_data = dialog.get_category_data()
            if category_data is None:
                return

            try:
                self.db.update_category(
                    category_id=category_id,
                    name=category_data['name'],
                    color=category_data['color']
                )
                self.load_data()
                QMessageBox.information(self, "Succès", "Catégorie modifiée avec succès")
            except Exception as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de la modification de la catégorie : {str(e)}")

    def delete_category(self):
        current_item = self.category_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "Attention", "Veuillez sélectionner une catégorie à supprimer")
            return

        category_id = current_item.data(0, Qt.UserRole)

        reply = QMessageBox.question(
            self, 'Confirmation',
            "Êtes-vous sûr de vouloir supprimer cette catégorie ?\nLes tâches associées ne seront pas supprimées.",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                self.db.delete_category(category_id)
                self.load_data()
                QMessageBox.information(self, "Succès", "Catégorie supprimée avec succès")
            except Exception as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression de la catégorie : {str(e)}")

class CalendarView(QWidget):
    def __init__(self, parent=None, db=None):
        super().__init__(parent)
        self.db = db
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout()

        # Contrôles
        controls_layout = QHBoxLayout()

        # Sélecteur de mois
        self.month_combo = QComboBox()
        self.month_combo.addItems([
            "Janvier", "Février", "Mars", "Avril", "Mai", "Juin",
            "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre"
        ])
        self.month_combo.setCurrentIndex(datetime.now().month - 1)
        self.month_combo.currentIndexChanged.connect(self.update_calendar)
        controls_layout.addWidget(self.month_combo)

        # Sélecteur d'année
        self.year_spin = QSpinBox()
        self.year_spin.setRange(2000, 2100)
        self.year_spin.setValue(datetime.now().year)
        self.year_spin.valueChanged.connect(self.update_calendar)
        controls_layout.addWidget(self.year_spin)

        # Boutons de navigation
        prev_month_btn = QPushButton("◀")
        prev_month_btn.clicked.connect(self.previous_month)
        controls_layout.addWidget(prev_month_btn)

        next_month_btn = QPushButton("▶")
        next_month_btn.clicked.connect(self.next_month)
        controls_layout.addWidget(next_month_btn)

        # Bouton pour ajouter une tâche à la date sélectionnée
        add_task_btn = QPushButton("✚ Ajouter une tâche")
        add_task_btn.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                padding: 8px 15px;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
        """)
        add_task_btn.clicked.connect(self.add_task_for_selected_date)
        controls_layout.addWidget(add_task_btn)

        layout.addLayout(controls_layout)

        # Calendrier
        self.calendar = QCalendarWidget()
        self.calendar.setGridVisible(True)
        self.calendar.setVerticalHeaderFormat(QCalendarWidget.NoVerticalHeader)
        self.calendar.clicked.connect(self.show_tasks_for_date)
        layout.addWidget(self.calendar)

        # Liste des tâches pour la date sélectionnée
        tasks_header_layout = QHBoxLayout()
        tasks_header_label = QLabel("Tâches pour la date sélectionnée:")
        tasks_header_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        tasks_header_layout.addWidget(tasks_header_label)

        layout.addLayout(tasks_header_layout)

        self.tasks_list = QTreeWidget()
        self.tasks_list.setHeaderLabels(["Titre", "Catégorie", "Priorité", "Statut"])
        self.tasks_list.setColumnWidth(0, 200)
        self.tasks_list.setColumnWidth(1, 150)
        self.tasks_list.setColumnWidth(2, 100)
        self.tasks_list.setColumnWidth(3, 100)
        self.tasks_list.setContextMenuPolicy(Qt.CustomContextMenu)
        self.tasks_list.customContextMenuRequested.connect(self.show_context_menu)
        self.tasks_list.itemDoubleClicked.connect(self.edit_task)
        layout.addWidget(self.tasks_list)

        self.setLayout(layout)
        self.update_calendar()

    def update_calendar(self):
        # Mettre à jour la date du calendrier
        month = self.month_combo.currentIndex() + 1
        year = self.year_spin.value()
        self.calendar.setCurrentPage(year, month)

        # Mettre à jour les tâches affichées
        self.show_tasks_for_date(self.calendar.selectedDate())

    def previous_month(self):
        current_month = self.month_combo.currentIndex()
        if current_month == 0:
            self.month_combo.setCurrentIndex(11)
            self.year_spin.setValue(self.year_spin.value() - 1)
        else:
            self.month_combo.setCurrentIndex(current_month - 1)

    def next_month(self):
        current_month = self.month_combo.currentIndex()
        if current_month == 11:
            self.month_combo.setCurrentIndex(0)
            self.year_spin.setValue(self.year_spin.value() + 1)
        else:
            self.month_combo.setCurrentIndex(current_month + 1)

    def show_tasks_for_date(self, date):
        self.tasks_list.clear()

        # Récupérer toutes les tâches
        tasks = self.db.get_all_tasks()

        # Filtrer les tâches pour la date sélectionnée
        selected_date = date.toString("yyyy-MM-dd")
        for task in tasks:
            if task[4] and task[4].startswith(selected_date):  # due_date
                item = QTreeWidgetItem(self.tasks_list)
                category = self.db.get_category_by_id(task[1])

                item.setText(0, task[2])  # Titre
                item.setText(1, category[1] if category else "")  # Catégorie
                item.setText(2, task[5])  # Priorité
                item.setText(3, task[6])  # Statut
                item.setData(0, Qt.UserRole, task[0])  # Stocker l'ID de la tâche

                # Appliquer les couleurs
                if category and len(category) > 2 and category[2]:
                    item.setForeground(1, QColor(category[2]))

                if task[5] == "haute":
                    item.setForeground(2, QColor("#e74c3c"))
                elif task[5] == "moyenne":
                    item.setForeground(2, QColor("#f39c12"))
                else:
                    item.setForeground(2, QColor("#27ae60"))

                if task[6] == "Terminée":
                    item.setForeground(3, QColor("#27ae60"))
                elif task[6] == "En cours":
                    item.setForeground(3, QColor("#f39c12"))
                else:
                    item.setForeground(3, QColor("#e74c3c"))

    def add_task_for_selected_date(self):
        """Ajoute une tâche à la date sélectionnée dans le calendrier"""
        selected_date = self.calendar.selectedDate()
        date_str = selected_date.toString("yyyy-MM-dd 09:00")  # Par défaut à 9h du matin

        # Récupérer les catégories
        categories = self.db.get_all_categories()

        # Créer une nouvelle tâche avec la date sélectionnée
        parent = self.parent()
        if parent and hasattr(parent, 'db'):
            dialog = TaskDialog(parent, categories=categories, db=parent.db)

            # Préremplir la date
            dialog.start_date_edit.setDateTime(QDateTime.fromString(date_str, 'yyyy-MM-dd HH:mm'))

            if dialog.exec_() == QDialog.Accepted:
                task_data = dialog.get_task_data()
                parent.db.add_task(**task_data)

                # Mettre à jour l'affichage
                self.show_tasks_for_date(selected_date)

                # Vérifier si c'est une tâche récurrente
                if task_data.get('is_recurring'):
                    QMessageBox.information(
                        self,
                        "Tâche récurrente",
                        f"Une tâche récurrente a été créée. Elle sera automatiquement recréée lorsqu'elle sera marquée comme terminée."
                    )

    def edit_task(self, item, _):
        """Modifie la tâche sélectionnée"""
        task_id = item.data(0, Qt.UserRole)
        parent = self.parent()

        if parent and hasattr(parent, 'db'):
            task = parent.db.get_task(task_id)
            categories = parent.db.get_all_categories()

            dialog = TaskDialog(parent, task=task, categories=categories, db=parent.db)
            if dialog.exec_() == QDialog.Accepted:
                task_data = dialog.get_task_data()
                parent.db.update_task(task_id, **task_data)

                # Mettre à jour l'affichage
                self.show_tasks_for_date(self.calendar.selectedDate())

                # Vérifier si c'est une tâche récurrente
                if task_data.get('is_recurring'):
                    QMessageBox.information(
                        self,
                        "Tâche récurrente",
                        f"Cette tâche est maintenant récurrente. Elle sera automatiquement recréée lorsqu'elle sera marquée comme terminée."
                    )

    def show_context_menu(self, position):
        """Affiche un menu contextuel pour la tâche sélectionnée"""
        item = self.tasks_list.itemAt(position)
        if not item:
            return

        task_id = item.data(0, Qt.UserRole)

        # Créer le menu contextuel
        context_menu = QMenu()

        # Actions
        edit_action = QAction("✏️ Modifier", self)
        edit_action.triggered.connect(lambda: self.edit_task(item, 0))
        context_menu.addAction(edit_action)

        # Ajouter les actions de changement de statut
        status_menu = QMenu("Changer le statut", context_menu)

        todo_action = QAction("À faire", self)
        todo_action.triggered.connect(lambda: self.change_task_status(task_id, "À faire"))
        status_menu.addAction(todo_action)

        in_progress_action = QAction("En cours", self)
        in_progress_action.triggered.connect(lambda: self.change_task_status(task_id, "En cours"))
        status_menu.addAction(in_progress_action)

        waiting_action = QAction("En attente", self)
        waiting_action.triggered.connect(lambda: self.change_task_status_with_reason(task_id, "En attente"))
        status_menu.addAction(waiting_action)

        completed_action = QAction("Terminée", self)
        completed_action.triggered.connect(lambda: self.change_task_status(task_id, "Terminée"))
        status_menu.addAction(completed_action)

        context_menu.addMenu(status_menu)

        # Ajouter l'action pour diviser la tâche
        split_action = QAction("✂️ Diviser la tâche", self)
        split_action.triggered.connect(lambda: self.split_task(task_id))
        context_menu.addAction(split_action)

        # Ajouter l'action de suppression
        delete_action = QAction("🗑️ Supprimer", self)
        delete_action.triggered.connect(lambda: self.delete_task(task_id))
        context_menu.addAction(delete_action)

        # Afficher le menu
        context_menu.exec_(self.tasks_list.mapToGlobal(position))

    def change_task_status(self, task_id, new_status):
        """Change le statut d'une tâche"""
        parent = self.parent()
        if parent and hasattr(parent, 'db'):
            # Mettre à jour le statut
            if hasattr(parent, 'update_task_status'):
                parent.update_task_status(task_id, new_status)
            else:
                parent.db.update_task_status(task_id, new_status)

            # Mettre à jour l'affichage
            self.show_tasks_for_date(self.calendar.selectedDate())

    def change_task_status_with_reason(self, task_id, new_status):
        """Change le statut d'une tâche avec une raison (pour le statut En attente)"""
        parent = self.parent()
        if parent and hasattr(parent, 'db'):
            # Demander la raison
            from PyQt5.QtWidgets import QInputDialog
            reason, ok = QInputDialog.getMultiLineText(
                self,
                "Raison de l'attente",
                "Veuillez indiquer la raison pour laquelle cette tâche est en attente:",
                ""
            )

            if ok:
                # Mettre à jour le statut avec la raison
                if hasattr(parent, 'update_task_status'):
                    parent.update_task_status(task_id, new_status, reason)
                else:
                    parent.db.update_task_status(task_id, new_status, reason)

                # Mettre à jour l'affichage
                self.show_tasks_for_date(self.calendar.selectedDate())

    def split_task(self, task_id):
        """Divise une tâche en plusieurs sous-tâches"""
        parent = self.parent()
        if parent and hasattr(parent, 'db'):
            # Ouvrir le dialogue de division de tâche
            dialog = SplitTaskDialog(parent, parent.db, task_id)
            if dialog.exec_() == QDialog.Accepted:
                # Récupérer les données des sous-tâches
                subtasks_data = dialog.get_subtasks_data()

                # Diviser la tâche
                try:
                    parent.db.split_task(task_id, subtasks_data)

                    # Mettre à jour l'affichage
                    self.show_tasks_for_date(self.calendar.selectedDate())

                    QMessageBox.information(
                        self,
                        "Tâche divisée",
                        f"La tâche a été divisée en {len(subtasks_data)} sous-tâches."
                    )
                except Exception as e:
                    QMessageBox.critical(
                        self,
                        "Erreur",
                        f"Une erreur s'est produite lors de la division de la tâche : {str(e)}"
                    )

    def delete_task(self, task_id):
        """Supprime une tâche"""
        parent = self.parent()
        if parent and hasattr(parent, 'db'):
            reply = QMessageBox.question(
                self, 'Confirmation',
                "Êtes-vous sûr de vouloir supprimer cette tâche ?",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                parent.db.delete_task(task_id)

                # Mettre à jour l'affichage
                self.show_tasks_for_date(self.calendar.selectedDate())

class RecurringTasksView(QWidget):
    def __init__(self, parent=None, db=None):
        super().__init__(parent)
        self.db = db
        self.parent = parent
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout(self)

        # En-tête
        header_layout = QHBoxLayout()
        title_label = QLabel("Gestion des tâches récurrentes")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50;")
        header_layout.addWidget(title_label)

        # Bouton de rafraîchissement
        refresh_btn = QPushButton("🔄 Rafraîchir")
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 8px 15px;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        refresh_btn.clicked.connect(self.load_recurring_tasks)
        header_layout.addWidget(refresh_btn)

        # Bouton pour créer une nouvelle tâche récurrente
        add_btn = QPushButton("✚ Nouvelle tâche récurrente")
        add_btn.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                padding: 8px 15px;
                border: none;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
        """)
        add_btn.clicked.connect(self.add_recurring_task)
        header_layout.addWidget(add_btn)

        layout.addLayout(header_layout)

        # Tableau des tâches récurrentes
        self.recurring_table = QTreeWidget()
        self.recurring_table.setHeaderLabels([
            "Titre", "Catégorie", "Fréquence", "Prochaine date", "Date de fin", "Statut"
        ])
        self.recurring_table.setColumnWidth(0, 250)  # Titre
        self.recurring_table.setColumnWidth(1, 150)  # Catégorie
        self.recurring_table.setColumnWidth(2, 100)  # Fréquence
        self.recurring_table.setColumnWidth(3, 150)  # Prochaine date
        self.recurring_table.setColumnWidth(4, 150)  # Date de fin
        self.recurring_table.setColumnWidth(5, 100)  # Statut

        self.recurring_table.setStyleSheet("""
            QTreeWidget {
                background-color: white;
                border: 1px solid #dcdde1;
                border-radius: 8px;
                padding: 5px;
            }
            QTreeWidget::item {
                padding: 5px;
            }
            QTreeWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #f1f2f6;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)

        self.recurring_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.recurring_table.customContextMenuRequested.connect(self.show_context_menu)
        self.recurring_table.itemDoubleClicked.connect(self.edit_recurring_task)

        layout.addWidget(self.recurring_table)

        # Chargement initial des tâches récurrentes
        self.load_recurring_tasks()

    def load_recurring_tasks(self):
        """Charge toutes les tâches récurrentes et leurs occurrences"""
        self.recurring_table.clear()

        # Récupérer toutes les tâches récurrentes
        recurring_tasks = self.db.get_recurring_tasks()

        for task in recurring_tasks:
            # Créer un élément parent pour la tâche récurrente
            parent_item = QTreeWidgetItem(self.recurring_table)

            # Récupérer la catégorie
            category = self.db.get_category_by_id(task[1])
            category_name = category[1] if category else ""

            # Récupérer le type de récurrence
            recurrence_type = task[10]  # recurrence_type
            recurrence_text = ""
            if recurrence_type == "daily":
                recurrence_text = "Quotidienne"
            elif recurrence_type == "weekly":
                recurrence_text = "Hebdomadaire"
            elif recurrence_type == "monthly":
                recurrence_text = "Mensuelle"
            elif recurrence_type == "yearly":
                recurrence_text = "Annuelle"

            # Formater les dates
            due_date = QDateTime.fromString(task[4], 'yyyy-MM-dd HH:mm') if task[4] else None
            due_date_text = due_date.toString('dd/MM/yyyy HH:mm') if due_date else ""

            end_date = QDate.fromString(task[11], 'yyyy-MM-dd') if task[11] else None
            end_date_text = end_date.toString('dd/MM/yyyy') if end_date else "Aucune"

            # Définir les données de l'élément parent
            parent_item.setText(0, task[2])  # Titre
            parent_item.setText(1, category_name)  # Catégorie
            parent_item.setText(2, recurrence_text)  # Fréquence
            parent_item.setText(3, due_date_text)  # Prochaine date
            parent_item.setText(4, end_date_text)  # Date de fin
            parent_item.setText(5, task[6])  # Statut
            parent_item.setData(0, Qt.UserRole, task[0])  # ID de la tâche

            # Appliquer les couleurs
            if task[6] == "Terminée":
                parent_item.setForeground(5, QColor("#27ae60"))
            elif task[6] == "En cours":
                parent_item.setForeground(5, QColor("#f39c12"))
            else:
                parent_item.setForeground(5, QColor("#e74c3c"))

            # Récupérer les occurrences de cette tâche récurrente
            child_tasks = self.db.get_child_tasks(task[0])

            for child_task in child_tasks:
                child_item = QTreeWidgetItem(parent_item)

                # Formater la date
                child_due_date = QDateTime.fromString(child_task[4], 'yyyy-MM-dd HH:mm') if child_task[4] else None
                child_due_date_text = child_due_date.toString('dd/MM/yyyy HH:mm') if child_due_date else ""

                # Définir les données de l'élément enfant
                child_item.setText(0, child_task[2])  # Titre
                child_item.setText(1, category_name)  # Catégorie
                child_item.setText(2, "Occurrence")  # Fréquence
                child_item.setText(3, child_due_date_text)  # Date
                child_item.setText(5, child_task[6])  # Statut
                child_item.setData(0, Qt.UserRole, child_task[0])  # ID de la tâche

                # Appliquer les couleurs
                if child_task[6] == "Terminée":
                    child_item.setForeground(5, QColor("#27ae60"))
                elif child_task[6] == "En cours":
                    child_item.setForeground(5, QColor("#f39c12"))
                else:
                    child_item.setForeground(5, QColor("#e74c3c"))

        # Développer tous les éléments
        self.recurring_table.expandAll()

    def add_recurring_task(self):
        """Ajoute une nouvelle tâche récurrente"""
        if self.parent and hasattr(self.parent, 'db'):
            categories = self.parent.db.get_all_categories()
            dialog = TaskDialog(self.parent, categories=categories)

            # Activer par défaut la case à cocher pour les tâches récurrentes
            dialog.is_recurring_check.setChecked(True)
            dialog.toggle_recurrence_options(True)

            if dialog.exec_() == QDialog.Accepted:
                task_data = dialog.get_task_data()

                # S'assurer que la tâche est bien récurrente
                if not task_data.get('is_recurring'):
                    QMessageBox.warning(
                        self,
                        "Attention",
                        "La tâche n'a pas été marquée comme récurrente. Elle a été créée comme une tâche normale."
                    )

                self.parent.db.add_task(**task_data)
                self.load_recurring_tasks()

    def edit_recurring_task(self, item, _):
        """Modifie une tâche récurrente"""
        task_id = item.data(0, Qt.UserRole)

        if self.parent and hasattr(self.parent, 'db'):
            task = self.parent.db.get_task(task_id)
            categories = self.parent.db.get_all_categories()

            dialog = TaskDialog(self.parent, task=task, categories=categories)
            if dialog.exec_() == QDialog.Accepted:
                task_data = dialog.get_task_data()
                self.parent.db.update_task(task_id, **task_data)
                self.load_recurring_tasks()

    def show_context_menu(self, position):
        """Affiche un menu contextuel pour la tâche sélectionnée"""
        item = self.recurring_table.itemAt(position)
        if not item:
            return

        task_id = item.data(0, Qt.UserRole)

        # Créer le menu contextuel
        context_menu = QMenu()

        # Actions
        edit_action = QAction("✏️ Modifier", self)
        edit_action.triggered.connect(lambda: self.edit_recurring_task(item, 0))
        context_menu.addAction(edit_action)

        # Ajouter les actions de changement de statut
        status_menu = QMenu("Changer le statut", context_menu)

        todo_action = QAction("À faire", self)
        todo_action.triggered.connect(lambda: self.change_task_status(task_id, "À faire"))
        status_menu.addAction(todo_action)

        in_progress_action = QAction("En cours", self)
        in_progress_action.triggered.connect(lambda: self.change_task_status(task_id, "En cours"))
        status_menu.addAction(in_progress_action)

        completed_action = QAction("Terminée", self)
        completed_action.triggered.connect(lambda: self.change_task_status(task_id, "Terminée"))
        status_menu.addAction(completed_action)

        context_menu.addMenu(status_menu)

        # Ajouter l'action de suppression
        delete_action = QAction("🗑️ Supprimer", self)
        delete_action.triggered.connect(lambda: self.delete_task(task_id))
        context_menu.addAction(delete_action)

        # Afficher le menu
        context_menu.exec_(self.recurring_table.mapToGlobal(position))

    def change_task_status(self, task_id, new_status):
        """Change le statut d'une tâche"""
        if self.parent and hasattr(self.parent, 'db'):
            # Mettre à jour le statut
            if hasattr(self.parent, 'update_task_status'):
                self.parent.update_task_status(task_id, new_status)
            else:
                self.parent.db.update_task_status(task_id, new_status)

            # Mettre à jour l'affichage
            self.load_recurring_tasks()

    def delete_task(self, task_id):
        """Supprime une tâche"""
        if self.parent and hasattr(self.parent, 'db'):
            reply = QMessageBox.question(
                self, 'Confirmation',
                "Êtes-vous sûr de vouloir supprimer cette tâche ?\nToutes les occurrences futures ne seront pas créées.",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.parent.db.delete_task(task_id)

                # Mettre à jour l'affichage
                self.load_recurring_tasks()

class DashboardView(QWidget):
    def __init__(self, parent=None, db=None):
        super().__init__(parent)
        self.db = db
        self.setup_ui()

    def setup_ui(self):
        layout = QGridLayout()
        self.setLayout(layout)

        # Style général pour les widgets
        self.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #dcdde1;
                border-radius: 8px;
                padding: 15px;
            }
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
            }
            QLabel.stats {
                font-size: 24px;
                padding: 10px;
            }
        """)

        # Widgets statistiques
        stats_frame = QFrame()
        stats_layout = QHBoxLayout(stats_frame)

        # Total des tâches avec style amélioré
        total_frame = self._create_stat_widget(
            "Total des tâches",
            len(self.db.get_all_tasks()),
            "#3498db"
        )
        stats_layout.addWidget(total_frame)

        # Tâches en cours
        in_progress = self._create_stat_widget(
            "En cours",
            len(self.db.get_tasks_by_status("En cours")),
            "#f39c12"
        )
        stats_layout.addWidget(in_progress)

        # Tâches terminées
        completed = self._create_stat_widget(
            "Terminées",
            len(self.db.get_tasks_by_status("Terminée")),
            "#2ecc71"
        )
        stats_layout.addWidget(completed)

        # Tâches à faire
        todo = self._create_stat_widget(
            "À faire",
            len(self.db.get_tasks_by_status("À faire")),
            "#e74c3c"
        )
        stats_layout.addWidget(todo)

        # Ajouter le frame des stats en haut
        layout.addWidget(stats_frame, 0, 0, 1, 2)

        # Message pour les graphiques désactivés
        if not HAS_CHARTS:
            info_frame = QFrame()
            info_layout = QVBoxLayout(info_frame)
            info_label = QLabel("Les graphiques sont désactivés car le module PyQt5.QtChart n'est pas installé.")
            info_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    color: #e74c3c;
                    padding: 20px;
                    text-align: center;
                }
            """)
            info_layout.addWidget(info_label)

            install_label = QLabel("Pour installer le module, exécutez la commande :")
            code_label = QLabel("pip install PyQtChart")
            code_label.setStyleSheet("""
                QLabel {
                    font-family: monospace;
                    background-color: #f1f2f6;
                    padding: 10px;
                    border-radius: 5px;
                }
            """)

            info_layout.addWidget(install_label)
            info_layout.addWidget(code_label)
            layout.addWidget(info_frame, 1, 0, 1, 2)
        else:
            # Graphique des catégories (camembert)
            category_frame = QFrame()
            category_layout = QVBoxLayout(category_frame)
            category_title = QLabel("Répartition par catégorie")
            category_layout.addWidget(category_title)

            # Créer le graphique en camembert
            pie_chart = self._create_category_chart()
            category_layout.addWidget(pie_chart)
            layout.addWidget(category_frame, 1, 0)

            # Graphique des statuts (barres)
            status_frame = QFrame()
            status_layout = QVBoxLayout(status_frame)
            status_title = QLabel("Répartition par statut")
            status_layout.addWidget(status_title)

            # Créer le graphique en barres
            bar_chart = self._create_status_chart()
            status_layout.addWidget(bar_chart)
            layout.addWidget(status_frame, 1, 1)

    def _create_stat_widget(self, title, value, color):
        frame = QFrame()
        frame.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border-left: 5px solid {color};
                padding: 10px;
                margin: 5px;
            }}
        """)

        layout = QVBoxLayout(frame)

        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #7f8c8d;
            }
        """)

        value_label = QLabel(str(value))
        value_label.setStyleSheet(f"""
            QLabel {{
                font-size: 24px;
                font-weight: bold;
                color: {color};
            }}
        """)

        layout.addWidget(title_label)
        layout.addWidget(value_label)

        return frame

    def _create_category_chart(self):
        if not HAS_CHARTS:
            return QLabel("Graphiques désactivés")

        # Créer une série pour le camembert
        series = QPieSeries()

        # Récupérer les données des catégories
        categories = self.db.get_all_categories()
        for category in categories:
            tasks = self.db.get_tasks_by_category(category[0])
            if tasks:
                series.append(category[1], len(tasks))

        # Créer le graphique
        chart = QChart()
        chart.addSeries(series)
        chart.setAnimationOptions(QChart.SeriesAnimations)
        chart.setTitle("Tâches par catégorie")

        # Créer la vue du graphique
        chart_view = QChartView(chart)
        chart_view.setRenderHint(QPainter.Antialiasing)

        return chart_view

    def _create_status_chart(self):
        if not HAS_CHARTS:
            return QLabel("Graphiques désactivés")

        # Créer une série pour le graphique en barres
        set_tasks = QBarSet("Tâches")

        # Données pour les statuts
        statuses = ["À faire", "En cours", "Terminée"]
        values = [
            len(self.db.get_tasks_by_status("À faire")),
            len(self.db.get_tasks_by_status("En cours")),
            len(self.db.get_tasks_by_status("Terminée"))
        ]

        set_tasks.append(values)

        series = QBarSeries()
        series.append(set_tasks)

        # Créer le graphique
        chart = QChart()
        chart.addSeries(series)
        chart.setTitle("Statuts des tâches")
        chart.setAnimationOptions(QChart.SeriesAnimations)

        # Axe des catégories
        axis_x = QBarCategoryAxis()
        axis_x.append(statuses)
        chart.addAxis(axis_x, Qt.AlignBottom)
        series.attachAxis(axis_x)

        # Axe des valeurs
        axis_y = QValueAxis()
        chart.addAxis(axis_y, Qt.AlignLeft)
        series.attachAxis(axis_y)

        # Créer la vue du graphique
        chart_view = QChartView(chart)
        chart_view.setRenderHint(QPainter.Antialiasing)

        return chart_view

class AdvancedSearchDialog(QDialog):
    def __init__(self, parent=None, db=None):
        super().__init__(parent)
        self.db = db
        self.setup_ui()

    def setup_ui(self):
        self.setWindowTitle("Recherche avancée")
        self.setMinimumWidth(600)

        layout = QVBoxLayout()

        # Style général
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f6fa;
            }
            QLabel {
                font-weight: bold;
                color: #2c3e50;
            }
            QLineEdit, QComboBox, QDateEdit {
                padding: 8px;
                border: 1px solid #dcdde1;
                border-radius: 4px;
                background-color: white;
            }
            QPushButton {
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
                color: white;
            }
            QPushButton#searchBtn {
                background-color: #3498db;
            }
            QPushButton#searchBtn:hover {
                background-color: #2980b9;
            }
            QPushButton#resetBtn {
                background-color: #e74c3c;
            }
            QPushButton#resetBtn:hover {
                background-color: #c0392b;
            }
        """)

        # Groupe Texte
        text_group = QGroupBox("Texte")
        text_layout = QFormLayout()

        self.search_text = QLineEdit()
        self.search_text.setPlaceholderText("Rechercher dans le titre et la description")
        text_layout.addRow("Texte:", self.search_text)

        text_group.setLayout(text_layout)
        layout.addWidget(text_group)

        # Groupe Filtres
        filter_group = QGroupBox("Filtres")
        filter_layout = QGridLayout()

        # Catégorie
        filter_layout.addWidget(QLabel("Catégorie:"), 0, 0)
        self.category_combo = QComboBox()
        self.category_combo.addItem("Toutes les catégories", None)
        categories = self.db.get_all_categories()
        for category in categories:
            self.category_combo.addItem(category[1], category[0])
        filter_layout.addWidget(self.category_combo, 0, 1)

        # Priorité
        filter_layout.addWidget(QLabel("Priorité:"), 0, 2)
        self.priority_combo = QComboBox()
        self.priority_combo.addItem("Toutes les priorités", None)
        self.priority_combo.addItem("Basse", "basse")
        self.priority_combo.addItem("Moyenne", "moyenne")
        self.priority_combo.addItem("Haute", "haute")
        filter_layout.addWidget(self.priority_combo, 0, 3)

        # Statut
        filter_layout.addWidget(QLabel("Statut:"), 1, 0)
        self.status_combo = QComboBox()
        self.status_combo.addItem("Tous les statuts", None)
        self.status_combo.addItem("À faire", "À faire")
        self.status_combo.addItem("En cours", "En cours")
        self.status_combo.addItem("Terminée", "Terminée")
        filter_layout.addWidget(self.status_combo, 1, 1)

        # Personne assignée
        filter_layout.addWidget(QLabel("Assignée à:"), 1, 2)
        self.person_combo = QComboBox()
        self.person_combo.addItem("Toutes les personnes", None)
        persons = self.db.get_all_persons()
        for person in persons:
            self.person_combo.addItem(f"{person[1]} ({person[2]})", person[0])
        filter_layout.addWidget(self.person_combo, 1, 3)

        # Dates
        filter_layout.addWidget(QLabel("Date début:"), 2, 0)
        self.date_start = QDateEdit()
        self.date_start.setCalendarPopup(True)
        self.date_start.setDate(QDate.currentDate().addDays(-30))
        self.date_start.setSpecialValueText("Aucune")
        filter_layout.addWidget(self.date_start, 2, 1)

        filter_layout.addWidget(QLabel("Date fin:"), 2, 2)
        self.date_end = QDateEdit()
        self.date_end.setCalendarPopup(True)
        self.date_end.setDate(QDate.currentDate().addDays(30))
        self.date_end.setSpecialValueText("Aucune")
        filter_layout.addWidget(self.date_end, 2, 3)

        # Checkbox pour activer/désactiver les dates
        self.use_dates = QCheckBox("Utiliser les dates")
        filter_layout.addWidget(self.use_dates, 3, 0, 1, 4)
        self.use_dates.setChecked(False)
        self.date_start.setEnabled(False)
        self.date_end.setEnabled(False)
        self.use_dates.stateChanged.connect(self.toggle_dates)

        filter_group.setLayout(filter_layout)
        layout.addWidget(filter_group)

        # Boutons
        buttons_layout = QHBoxLayout()

        self.search_button = QPushButton("Rechercher")
        self.search_button.setObjectName("searchBtn")
        self.search_button.clicked.connect(self.accept)
        buttons_layout.addWidget(self.search_button)

        self.reset_button = QPushButton("Réinitialiser")
        self.reset_button.setObjectName("resetBtn")
        self.reset_button.clicked.connect(self.reset_filters)
        buttons_layout.addWidget(self.reset_button)

        layout.addLayout(buttons_layout)
        self.setLayout(layout)

    def toggle_dates(self, state):
        self.date_start.setEnabled(state)
        self.date_end.setEnabled(state)

    def reset_filters(self):
        self.search_text.clear()
        self.category_combo.setCurrentIndex(0)
        self.priority_combo.setCurrentIndex(0)
        self.status_combo.setCurrentIndex(0)
        self.person_combo.setCurrentIndex(0)
        self.date_start.setDate(QDate.currentDate().addDays(-30))
        self.date_end.setDate(QDate.currentDate().addDays(30))
        self.use_dates.setChecked(False)

    def get_filters(self):
        filters = {}

        if self.search_text.text().strip():
            filters['search_text'] = self.search_text.text().strip()

        if self.category_combo.currentData():
            filters['category_id'] = self.category_combo.currentData()

        if self.priority_combo.currentData():
            filters['priority'] = self.priority_combo.currentData()

        if self.status_combo.currentData():
            filters['status'] = self.status_combo.currentData()

        if self.person_combo.currentData():
            filters['person_id'] = self.person_combo.currentData()

        if self.use_dates.isChecked():
            filters['due_date_start'] = self.date_start.date().toString("yyyy-MM-dd")
            filters['due_date_end'] = self.date_end.date().toString("yyyy-MM-dd")

        return filters

class MainWindow(QMainWindow):
    def __init__(self, db, excel_exporter):
        super().__init__()
        self.db = db
        self.excel_exporter = excel_exporter

        # Initialiser le gestionnaire de notifications
        self.notification_manager = NotificationManager(self)

        # Initialiser le système d'aide
        self.help_system = HelpSystem(self)

        # Initialiser le gestionnaire de favoris
        self.favorites_manager = FavoritesManager(self)

        self.setup_ui()
        self.setup_shortcuts()
        self.load_tasks()

        # Afficher une notification de bienvenue
        self.notification_manager.show_notification(
            "Bienvenue",
            "Application SOTRAMINE PHOSPHATE démarrée avec succès.",
            "info"
        )

    def setup_ui(self):
        self.setWindowTitle("Gestionnaire de Tâches Pro")
        self.setMinimumSize(1200, 800)

        # Appliquer le style global de l'application
        self.setStyleSheet(APP_STYLE)

        # Créer une barre de statut améliorée
        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)

        # Label pour le statut général
        self.status_label = QLabel("Prêt")
        self.statusBar.addWidget(self.status_label)

        # Séparateur permanent
        self.statusBar.addPermanentWidget(QFrame(frameShape=QFrame.VLine))

        # Label pour le nombre de tâches
        self.tasks_count_label = QLabel()
        self.statusBar.addPermanentWidget(self.tasks_count_label)

        # Séparateur permanent
        self.statusBar.addPermanentWidget(QFrame(frameShape=QFrame.VLine))

        # Label pour les alertes de stock
        self.alerts_count_label = QLabel()
        self.statusBar.addPermanentWidget(self.alerts_count_label)

        # Séparateur permanent
        self.statusBar.addPermanentWidget(QFrame(frameShape=QFrame.VLine))

        # Label pour la date et l'heure
        self.datetime_label = QLabel()
        self.statusBar.addPermanentWidget(self.datetime_label)

        # Timer pour mettre à jour la date et l'heure
        self.datetime_timer = QTimer(self)
        self.datetime_timer.timeout.connect(self.update_datetime)
        self.datetime_timer.start(1000)  # Mise à jour toutes les secondes

        # Mettre à jour les informations de la barre de statut
        self.update_status_bar()

        # Créer le widget d'onglets principal
        self.tab_widget = QTabWidget()
        self.setCentralWidget(self.tab_widget)

        # Créer les onglets principaux
        self.dashboard_view = DashboardView(self, self.db)
        self.tasks_view = QWidget()
        self.maintenance_view = QWidget()
        self.hr_view = QWidget()

        # Utiliser la fonction helper pour ajouter les onglets
        tabs_config = [
            (self.dashboard_view, "📊 Tableau de bord"),
            (self.tasks_view, "📝 Gestion des tâches"),
            (self.maintenance_view, "🔧 Maintenance"),
            (self.hr_view, "👥 Ressources Humaines")
        ]
        add_tabs(self.tab_widget, tabs_config)

        # Configurer l'onglet des tâches
        tasks_layout = QVBoxLayout()
        self.tasks_view.setLayout(tasks_layout)

        # Créer le tableau des tâches
        self.task_table = QTableWidget()
        self.task_table.setColumnCount(7)
        self.task_table.setHorizontalHeaderLabels([
            "ID", "Catégorie", "Titre", "Date d'échéance",
            "Priorité", "Statut", "Personnes assignées"
        ])
        self.task_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.task_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.task_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.task_table.customContextMenuRequested.connect(self.show_task_context_menu)
        tasks_layout.addWidget(self.task_table)

        # Configurer l'onglet de maintenance
        maintenance_layout = QVBoxLayout()
        self.maintenance_view.setLayout(maintenance_layout)

        # Configurer l'onglet des ressources humaines
        hr_layout = QVBoxLayout()
        self.hr_view.setLayout(hr_layout)

        # Menu bar
        menubar = self.menuBar()

        # Menu Fichier
        file_menu = menubar.addMenu('Fichier')

        home_action = QAction('🏠 Accueil', self)
        home_action.setShortcut('Ctrl+H')
        home_action.triggered.connect(self.return_to_home_wrapper)
        file_menu.addAction(home_action)

        file_menu.addSeparator()

        # Sous-menu Export
        export_submenu = QMenu('📤 Exporter', self)

        advanced_export = QAction('Exportation avancée', self)
        advanced_export.setShortcut('Ctrl+Alt+E')
        advanced_export.triggered.connect(self.export_tasks)
        export_submenu.addAction(advanced_export)

        export_submenu.addSeparator()

        daily_export = QAction('Export journalier', self)
        daily_export.setShortcut('Ctrl+E')
        daily_export.triggered.connect(self.export_daily)
        export_submenu.addAction(daily_export)

        monthly_export = QAction('Export mensuel', self)
        monthly_export.setShortcut('Ctrl+Shift+E')
        monthly_export.triggered.connect(self.export_monthly)
        export_submenu.addAction(monthly_export)

        file_menu.addMenu(export_submenu)

        # Sous-menu Impression
        print_submenu = QMenu('🖨️ Imprimer', self)

        print_tasks = QAction('Imprimer la liste des tâches', self)
        print_tasks.setShortcut('Ctrl+P')
        print_tasks.triggered.connect(self.print_tasks)
        print_submenu.addAction(print_tasks)

        print_calendar = QAction('Imprimer le calendrier', self)
        print_calendar.setShortcut('Ctrl+Shift+P')
        print_calendar.triggered.connect(self.print_calendar)
        print_submenu.addAction(print_calendar)

        print_dashboard = QAction('Imprimer le tableau de bord', self)
        print_dashboard.triggered.connect(self.print_dashboard)
        print_submenu.addAction(print_dashboard)

        file_menu.addMenu(print_submenu)

        file_menu.addSeparator()

        exit_action = QAction('Quitter', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # Menu Tâches
        tasks_menu = menubar.addMenu('Tâches')

        new_task_action = QAction('✚ Nouvelle tâche', self)
        new_task_action.setShortcut('Ctrl+N')
        new_task_action.triggered.connect(self.add_task)
        tasks_menu.addAction(new_task_action)

        search_task_action = QAction('🔍 Recherche avancée', self)
        search_task_action.setShortcut('Ctrl+F')
        search_task_action.triggered.connect(self.show_advanced_search)
        tasks_menu.addAction(search_task_action)

        tasks_menu.addSeparator()

        edit_task_action = QAction('✏️ Modifier la tâche', self)
        edit_task_action.setShortcut('Ctrl+E')
        edit_task_action.triggered.connect(lambda: self.edit_task(self.task_table.currentRow(), 0) if hasattr(self, 'task_table') and self.task_table.currentRow() >= 0 else QMessageBox.warning(self, "Attention", "Veuillez sélectionner une tâche à modifier."))
        tasks_menu.addAction(edit_task_action)

        delete_task_action = QAction('🗑️ Supprimer la tâche', self)
        delete_task_action.setShortcut('Delete')
        delete_task_action.triggered.connect(lambda: self.delete_task(self.task_table.item(self.task_table.currentRow(), 0).data(Qt.UserRole)) if hasattr(self, 'task_table') and self.task_table.currentRow() >= 0 else QMessageBox.warning(self, "Attention", "Veuillez sélectionner une tâche à supprimer."))
        tasks_menu.addAction(delete_task_action)

        tasks_menu.addSeparator()

        recurring_tasks_action = QAction('🔄 Gérer les tâches récurrentes', self)
        recurring_tasks_action.triggered.connect(lambda: self.tab_widget.setCurrentIndex(1) or self.tab_widget.widget(1).setCurrentIndex(2))
        tasks_menu.addAction(recurring_tasks_action)

        # Menu Maintenance
        maintenance_menu = menubar.addMenu('Maintenance')

        manage_equipment = QAction('🔌 Gérer les équipements', self)
        manage_equipment.triggered.connect(self.manage_equipment)
        maintenance_menu.addAction(manage_equipment)

        manage_spare_parts = QAction('🔧 Gérer les pièces de rechange', self)
        manage_spare_parts.triggered.connect(self.manage_spare_parts)
        maintenance_menu.addAction(manage_spare_parts)

        maintenance_menu.addSeparator()

        view_maintenance_history = QAction('📋 Historique de maintenance', self)
        view_maintenance_history.triggered.connect(lambda: self.tab_widget.setCurrentIndex(2) or self.tab_widget.widget(2).setCurrentIndex(0))
        maintenance_menu.addAction(view_maintenance_history)

        # Menu Ressources Humaines
        hr_menu = menubar.addMenu('Ressources Humaines')

        manage_persons = QAction('👥 Gérer le personnel', self)
        manage_persons.triggered.connect(self.manage_persons)
        hr_menu.addAction(manage_persons)

        manage_attendance = QAction('📊 Pointage journalier', self)
        manage_attendance.triggered.connect(self.manage_attendance)
        hr_menu.addAction(manage_attendance)

        # Menu Favoris
        favorites_menu = menubar.addMenu('⭐ Favoris')

        # Action pour gérer les favoris
        manage_favorites_action = QAction('Gérer les favoris', self)
        manage_favorites_action.triggered.connect(self.show_favorites)
        favorites_menu.addAction(manage_favorites_action)

        favorites_menu.addSeparator()

        # Ajouter les favoris au menu
        self.populate_favorites_menu(favorites_menu)

        # Menu Rapports
        reports_menu = menubar.addMenu('Rapports')

        dashboard_action = QAction('📊 Tableau de bord', self)
        dashboard_action.triggered.connect(lambda: self.tab_widget.setCurrentIndex(0))
        reports_menu.addAction(dashboard_action)

        daily_report_action = QAction('📄 Rapport journalier', self)
        daily_report_action.triggered.connect(self.export_daily)
        reports_menu.addAction(daily_report_action)

        monthly_report_action = QAction('📅 Rapport mensuel', self)
        monthly_report_action.triggered.connect(self.export_monthly)
        reports_menu.addAction(monthly_report_action)

        # Menu Aide
        help_menu = menubar.addMenu('Aide')

        # Action pour l'aide
        help_action = QAction('📚 Aide', self)
        help_action.setShortcut('F1')
        help_action.triggered.connect(lambda: self.help_system.show_help())
        help_menu.addAction(help_action)

        # Sous-menu pour l'aide contextuelle
        context_help_menu = QMenu('Aide contextuelle', self)

        tasks_help_action = QAction('Gestion des tâches', self)
        tasks_help_action.triggered.connect(lambda: self.help_system.show_help("tasks"))
        context_help_menu.addAction(tasks_help_action)

        maintenance_help_action = QAction('Maintenance', self)
        maintenance_help_action.triggered.connect(lambda: self.help_system.show_help("maintenance"))
        context_help_menu.addAction(maintenance_help_action)

        hr_help_action = QAction('Ressources Humaines', self)
        hr_help_action.triggered.connect(lambda: self.help_system.show_help("hr"))
        context_help_menu.addAction(hr_help_action)

        shortcuts_help_action = QAction('Raccourcis clavier', self)
        shortcuts_help_action.triggered.connect(lambda: self.help_system.show_help("shortcuts"))
        context_help_menu.addAction(shortcuts_help_action)

        help_menu.addMenu(context_help_menu)

        help_menu.addSeparator()

        about_action = QAction('À propos', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

        config_action = QAction('⚙️ Configuration', self)
        config_action.triggered.connect(self.open_configuration_wrapper)
        help_menu.addAction(config_action)

        # Widget central avec onglets
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # Fil d'Ariane
        breadcrumb_layout = QHBoxLayout()
        breadcrumb_layout.setContentsMargins(5, 5, 5, 5)

        home_btn = QPushButton("🏠 Accueil")
        home_btn.setFlat(True)
        home_btn.setCursor(Qt.PointingHandCursor)
        home_btn.clicked.connect(self.return_to_home_wrapper)
        breadcrumb_layout.addWidget(home_btn)

        separator = QLabel(">")
        breadcrumb_layout.addWidget(separator)

        self.current_section_label = QLabel("Tableau de bord")
        self.current_section_label.setStyleSheet("font-weight: bold;")
        breadcrumb_layout.addWidget(self.current_section_label)

        self.subsection_separator = QLabel(">")
        self.subsection_separator.setVisible(False)
        breadcrumb_layout.addWidget(self.subsection_separator)

        self.current_subsection_label = QLabel("")
        self.current_subsection_label.setStyleSheet("font-weight: bold;")
        self.current_subsection_label.setVisible(False)
        breadcrumb_layout.addWidget(self.current_subsection_label)

        breadcrumb_layout.addStretch()

        layout.addLayout(breadcrumb_layout)

        # Séparateur
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        layout.addWidget(separator)

        # Barre d'outils
        toolbar = QToolBar()
        self.addToolBar(toolbar)

        # Boutons de la barre d'outils
        # Le bouton Accueil sera ajouté par la classe SotramineApp

        # Séparateur pour les catégories
        toolbar.addSeparator()

        # Catégorie Tâches
        tasks_label = QLabel("Tâches:")
        tasks_label.setStyleSheet("font-weight: bold; margin-right: 5px;")
        toolbar.addWidget(tasks_label)

        new_task_action = QAction("✚ Nouvelle tâche", self)
        new_task_action.setToolTip("Créer une nouvelle tâche")
        new_task_action.triggered.connect(self.add_task)
        toolbar.addAction(new_task_action)

        search_task_action = QAction("🔍 Rechercher", self)
        search_task_action.setToolTip("Rechercher des tâches")
        search_task_action.triggered.connect(self.show_advanced_search)
        toolbar.addAction(search_task_action)

        # Séparateur pour les catégories
        toolbar.addSeparator()

        # Catégorie Maintenance
        maintenance_label = QLabel("Maintenance:")
        maintenance_label.setStyleSheet("font-weight: bold; margin-right: 5px;")
        toolbar.addWidget(maintenance_label)

        equipment_action = QAction("🔌 Équipements", self)
        equipment_action.setToolTip("Gérer les équipements")
        equipment_action.triggered.connect(self.manage_equipment)
        toolbar.addAction(equipment_action)

        spare_parts_action = QAction("🔧 Pièces de rechange", self)
        spare_parts_action.setToolTip("Gérer les pièces de rechange")
        spare_parts_action.triggered.connect(self.manage_spare_parts)
        toolbar.addAction(spare_parts_action)

        # Séparateur pour les catégories
        toolbar.addSeparator()

        # Catégorie Ressources Humaines
        hr_label = QLabel("Ressources Humaines:")
        hr_label.setStyleSheet("font-weight: bold; margin-right: 5px;")
        toolbar.addWidget(hr_label)

        manage_persons_action = QAction("👥 Personnel", self)
        manage_persons_action.setToolTip("Gérer le personnel")
        manage_persons_action.triggered.connect(self.manage_persons)
        toolbar.addAction(manage_persons_action)

        attendance_action = QAction("📊 Pointage", self)
        attendance_action.setToolTip("Gérer le pointage journalier")
        attendance_action.triggered.connect(self.manage_attendance)
        toolbar.addAction(attendance_action)

        # Gestionnaire d'onglets
        self.tab_widget = QTabWidget()
        self.tab_widget.currentChanged.connect(self.update_breadcrumb)
        layout.addWidget(self.tab_widget)

        # Onglet Tableau de bord (parent)
        dashboard_parent_tab = QTabWidget()

        # Sous-onglet Tableau de bord standard
        dashboard = DashboardView(self, self.db)
        dashboard_parent_tab.addTab(dashboard, "📊 Tableau de bord standard")

        # Sous-onglet Tableau de bord personnalisable
        from gui.custom_dashboard import CustomDashboardView
        custom_dashboard = CustomDashboardView(self, self.db)
        dashboard_parent_tab.addTab(custom_dashboard, "🔧 Tableau de bord personnalisable")

        self.tab_widget.addTab(dashboard_parent_tab, "📊 Tableau de bord")

        # Onglet Gestion des tâches (parent)
        tasks_parent_tab = QTabWidget()

        # Sous-onglet Liste des tâches
        tasks_widget = QWidget()
        tasks_layout = QVBoxLayout(tasks_widget)

        # Barre de recherche et filtres
        search_layout = QHBoxLayout()

        # Bouton de recherche avancée
        self.advanced_search_btn = QPushButton("Recherche avancée")
        self.advanced_search_btn.setObjectName("advancedSearchBtn")
        self.advanced_search_btn.clicked.connect(self.show_advanced_search)
        search_layout.addWidget(self.advanced_search_btn)

        # Label d'information sur les filtres actifs
        self.filter_info_label = QLabel("Aucun filtre actif")
        self.filter_info_label.setObjectName("filterInfoLabel")
        self.filter_info_label.setVisible(False)
        search_layout.addWidget(self.filter_info_label)

        # Bouton pour effacer les filtres
        self.clear_filters_btn = QPushButton("Effacer les filtres")
        self.clear_filters_btn.clicked.connect(self.clear_filters)
        self.clear_filters_btn.setVisible(False)
        search_layout.addWidget(self.clear_filters_btn)

        search_layout.addStretch()
        tasks_layout.addLayout(search_layout)

        # Table des tâches avec style amélioré
        self.task_table = QTableWidget()

        # Variable pour stocker les filtres actifs
        self.active_filters = None
        self.task_table.setColumnCount(9)  # Ajout d'une colonne pour l'affectation
        self.task_table.setHorizontalHeaderLabels([
            "Titre", "Description", "Catégorie", "Affectation", "Priorité",
            "Statut", "Date de début", "Date de fin", "Durée"
        ])

        # Définir des largeurs spécifiques pour chaque colonne
        self.task_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Interactive)  # Titre
        self.task_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Interactive)  # Description
        self.task_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Interactive)  # Catégorie
        self.task_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.Interactive)  # Affectation
        self.task_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.Interactive)  # Priorité
        self.task_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.Interactive)  # Statut
        self.task_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.Interactive)  # Date de début
        self.task_table.horizontalHeader().setSectionResizeMode(7, QHeaderView.Interactive)  # Date de fin
        self.task_table.horizontalHeader().setSectionResizeMode(8, QHeaderView.Interactive)  # Durée

        # Définir les largeurs initiales des colonnes
        self.task_table.setColumnWidth(0, 200)  # Titre
        self.task_table.setColumnWidth(1, 250)  # Description
        self.task_table.setColumnWidth(2, 120)  # Catégorie
        self.task_table.setColumnWidth(3, 180)  # Affectation
        self.task_table.setColumnWidth(4, 80)   # Priorité
        self.task_table.setColumnWidth(5, 80)   # Statut
        self.task_table.setColumnWidth(6, 140)  # Date de début
        self.task_table.setColumnWidth(7, 140)  # Date de fin
        self.task_table.setColumnWidth(8, 80)   # Durée
        self.task_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                border: 1px solid #dcdde1;
                border-radius: 8px;
                padding: 5px;
            }
            QTableWidget::item {
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #f1f2f6;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)
        tasks_layout.addWidget(self.task_table)

        # Ajouter le sous-onglet Liste des tâches
        tasks_parent_tab.addTab(tasks_widget, "📝 Liste des tâches")

        # Sous-onglet Calendrier
        calendar = CalendarView(self, self.db)
        tasks_parent_tab.addTab(calendar, "📅 Calendrier")

        # Sous-onglet Tâches récurrentes
        recurring_tasks = RecurringTasksView(self, self.db)
        tasks_parent_tab.addTab(recurring_tasks, "🔄 Tâches récurrentes")

        # Ajouter l'onglet parent Gestion des tâches
        self.tab_widget.addTab(tasks_parent_tab, "📋 Gestion des tâches")

                # Onglet Maintenance (parent) - Utilisation du menu de maintenance simplifié
        from gui.maintenance_menu_simple import MaintenanceMenu
        maintenance_menu = MaintenanceMenu(self.db, self)
        self.tab_widget.addTab(maintenance_menu, "🔧 Maintenance")

        # Onglet Ressources Humaines (parent)
        hr_parent_tab = QTabWidget()

        # Sous-onglet Personnel
        personnel_tab = QWidget()
        personnel_layout = QVBoxLayout(personnel_tab)

        # Titre et description
        personnel_header_layout = QHBoxLayout()
        personnel_title_layout = QVBoxLayout()
        personnel_title = QLabel("Gestion du personnel")
        personnel_title.setStyleSheet("font-size: 18px; font-weight: bold; color: #e74c3c;")
        personnel_description = QLabel("Gérez les informations du personnel et les affectations")
        personnel_description.setStyleSheet("font-size: 12px; color: #666;")
        personnel_title_layout.addWidget(personnel_title)
        personnel_title_layout.addWidget(personnel_description)
        personnel_header_layout.addLayout(personnel_title_layout)
        personnel_header_layout.addStretch()
        personnel_layout.addLayout(personnel_header_layout)

        # Bouton pour ouvrir la gestion du personnel
        personnel_btn = QPushButton("👥 Gérer le personnel")
        personnel_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 15px 30px;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 16px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        personnel_btn.clicked.connect(self.manage_persons)
        personnel_layout.addWidget(personnel_btn, alignment=Qt.AlignCenter)

        hr_parent_tab.addTab(personnel_tab, "👥 Personnel")

        # Sous-onglet Pointage
        attendance_tab = QWidget()
        attendance_layout = QVBoxLayout(attendance_tab)

        # Titre et description
        attendance_header_layout = QHBoxLayout()
        attendance_title_layout = QVBoxLayout()
        attendance_title = QLabel("Pointage journalier")
        attendance_title.setStyleSheet("font-size: 18px; font-weight: bold; color: #e74c3c;")
        attendance_description = QLabel("Suivez la présence du personnel et les absences")
        attendance_description.setStyleSheet("font-size: 12px; color: #666;")
        attendance_title_layout.addWidget(attendance_title)
        attendance_title_layout.addWidget(attendance_description)
        attendance_header_layout.addLayout(attendance_title_layout)
        attendance_header_layout.addStretch()
        attendance_layout.addLayout(attendance_header_layout)

        # Bouton pour ouvrir le pointage
        attendance_btn = QPushButton("📊 Gérer le pointage")
        attendance_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                padding: 15px 30px;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                font-size: 16px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        attendance_btn.clicked.connect(self.manage_attendance)
        attendance_layout.addWidget(attendance_btn, alignment=Qt.AlignCenter)

        hr_parent_tab.addTab(attendance_tab, "📊 Pointage")

        # Ajouter l'onglet parent Ressources Humaines
        self.tab_widget.addTab(hr_parent_tab, "👥 Ressources Humaines")

        # Initialiser le fil d'Ariane
        self.update_breadcrumb(0)  # Tableau de bord par défaut

        # Connexion du double-clic pour modifier une tâche
        self.task_table.cellDoubleClicked.connect(self.edit_task)

        # Ajouter un menu contextuel à la table des tâches
        self.task_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.task_table.customContextMenuRequested.connect(self.show_task_context_menu)

    def load_tasks(self, filters=None):
        """Charge les tâches dans la table"""
        try:
            print("Chargement des tâches...")  # Log
            
            # Vider la table
            self.task_table.setRowCount(0)
            
            # Récupérer les tâches
            if filters:
                tasks = self.db.advanced_search_tasks(filters)
            else:
                tasks = self.db.get_all_tasks()
                
            print(f"Nombre de tâches trouvées : {len(tasks)}")  # Log
            
            # Afficher les tâches
            for row, task in enumerate(tasks):
                print(f"Affichage de la tâche {task[0]} : {task}")  # Log
                
                self.task_table.insertRow(row)
                
                # ID
                id_item = QTableWidgetItem(str(task[0]))
                id_item.setData(Qt.UserRole, task[0])  # Stocker l'ID pour référence
                self.task_table.setItem(row, 0, id_item)
                
                # Catégorie
                category = self.db.get_category_by_id(task[1])
                category_name = category[1] if category else ""
                self.task_table.setItem(row, 1, QTableWidgetItem(category_name))
                
                # Titre
                self.task_table.setItem(row, 2, QTableWidgetItem(task[2]))
                
                # Date d'échéance
                due_date = QDateTime.fromString(task[4], 'yyyy-MM-dd HH:mm') if task[4] else None
                due_date_str = due_date.toString('dd/MM/yyyy HH:mm') if due_date else ""
                self.task_table.setItem(row, 3, QTableWidgetItem(due_date_str))
                
                # Priorité
                self.task_table.setItem(row, 4, QTableWidgetItem(task[5]))
                
                # Statut
                self.task_table.setItem(row, 5, QTableWidgetItem(task[6]))
                
                # Personnes assignées
                assignees = self.db.get_task_assignees(task[0])
                assignee_names = ", ".join([f"{a[1]} ({a[2]})" for a in assignees]) if assignees else ""
                self.task_table.setItem(row, 6, QTableWidgetItem(assignee_names))
                
            print("Chargement des tâches terminé")  # Log
            
        except Exception as e:
            print(f"Erreur lors du chargement des tâches : {str(e)}")  # Log
            self.notification_manager.show_notification(
                "Erreur",
                f"Erreur lors du chargement des tâches : {str(e)}",
                "error"
            )

    def add_task(self):
        # Récupérer toutes les catégories
        categories = self.db.get_all_categories()
        
        # Créer et afficher la boîte de dialogue
        dialog = TaskDialog(self, categories=categories)
        if dialog.exec_() == QDialog.Accepted:
            # Récupérer les données de la tâche
            task_data = dialog.get_task_data()
            
            try:
                # Ajouter la tâche à la base de données
                task_id = self.db.add_task(
                    category_id=task_data['category_id'],
                    title=task_data['title'],
                    description=task_data['description'],
                    due_date=task_data['due_date'],
                    priority=task_data['priority'],
                    status=task_data['status'],
                    assigned_to_ids=task_data['assigned_to_ids'],
                    estimated_time=task_data['estimated_time'],
                    is_recurring=task_data['is_recurring'],
                    recurrence_type=task_data['recurrence_type'],
                    recurrence_end_date=task_data['recurrence_end_date'],
                    is_maintenance_checklist=task_data['is_maintenance_checklist'],
                    checklist_template_id=task_data['checklist_template_id'],
                    waiting_reason=task_data['waiting_reason'],
                    equipment_id=task_data['equipment_id']
                )
                
                # Afficher une notification
                if task_data['is_recurring']:
                    self.notification_manager.show_notification(
                        "Tâche récurrente créée",
                        f"La tâche '{task_data['title']}' a été créée avec succès.",
                        "success"
                    )
                else:
                    self.notification_manager.show_notification(
                        "Tâche créée",
                        f"La tâche '{task_data['title']}' a été créée avec succès.",
                        "success"
                    )
                
                # Recharger les tâches
                self.load_tasks()
                
            except Exception as e:
                self.notification_manager.show_notification(
                    "Erreur",
                    f"Erreur lors de la création de la tâche : {str(e)}",
                    "error"
                )

    def edit_selected_task(self):
        """Modifie la tâche sélectionnée dans la table principale"""
        print("Début de la modification de la tâche sélectionnée")  # Log

        # Vérifier si nous sommes dans le bon onglet
        if not hasattr(self, 'task_table'):
            print("La table des tâches n'existe pas")  # Log
            QMessageBox.warning(self, "Attention", "Veuillez sélectionner une tâche dans la liste des tâches.")
            return

        if self.tab_widget.currentIndex() != 1:
            print(f"Mauvais onglet actif : {self.tab_widget.currentIndex()}")  # Log
            QMessageBox.warning(self, "Attention", "Veuillez sélectionner une tâche dans la liste des tâches.")
            return

        current_row = self.task_table.currentRow()
        print(f"Ligne sélectionnée : {current_row}")  # Log

        if current_row < 0:
            print("Aucune ligne sélectionnée")  # Log
            QMessageBox.warning(self, "Attention", "Veuillez sélectionner une tâche à modifier.")
            return

        item = self.task_table.item(current_row, 0)
        if not item:
            print("Impossible de récupérer l'élément de la table")  # Log
            QMessageBox.warning(self, "Attention", "Impossible de récupérer les informations de la tâche.")
            return

        task_id = item.data(Qt.UserRole)
        print(f"ID de la tâche récupéré : {task_id}")  # Log

        if not task_id:
            print("ID de la tâche non trouvé")  # Log
            QMessageBox.warning(self, "Attention", "Impossible de récupérer l'ID de la tâche.")
            return

        self.edit_task(current_row, 0, task_id)

    def delete_selected_task(self):
        """Supprime la tâche sélectionnée dans la table principale"""
        if not hasattr(self, 'task_table'):
            return
            
        current_row = self.task_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "Attention", "Veuillez sélectionner une tâche à supprimer.")
            return
            
        task_id = self.task_table.item(current_row, 0).data(Qt.UserRole)
        if task_id:
            self.delete_task(task_id)

    def export_daily(self):
        try:
            filename = self.excel_exporter.export_daily_report()
            QMessageBox.information(
                self,
                "Succès",
                f"Le rapport journalier a été exporté avec succès dans le dossier export\n{filename}"
            )

            # Afficher une notification
            self.notification_manager.show_notification(
                "Export réussi",
                f"Le rapport journalier a été exporté dans {filename}",
                "success"
            )
        except Exception as e:
            error_msg = f"Erreur lors de l'exportation : {str(e)}"
            QMessageBox.critical(self, "Erreur", error_msg)

            # Afficher une notification d'erreur
            self.notification_manager.show_notification(
                "Erreur d'exportation",
                error_msg,
                "error"
            )

    def export_monthly(self):
        try:
            filename = self.excel_exporter.export_monthly_report()
            QMessageBox.information(
                self,
                "Succès",
                f"Le rapport mensuel a été exporté avec succès dans le dossier export\n{filename}"
            )

            # Afficher une notification
            self.notification_manager.show_notification(
                "Export réussi",
                f"Le rapport mensuel a été exporté dans {filename}",
                "success"
            )
        except Exception as e:
            error_msg = f"Erreur lors de l'exportation : {str(e)}"
            QMessageBox.critical(self, "Erreur", error_msg)

            # Afficher une notification d'erreur
            self.notification_manager.show_notification(
                "Erreur d'exportation",
                error_msg,
                "error"
            )

    def export_tasks(self):
        """Exporte les données avec l'exportation avancée"""
        from gui.advanced_export import AdvancedExportDialog

        dialog = AdvancedExportDialog(self, self.db, self.excel_exporter)
        dialog.exec_()

    def manage_persons(self):
        dialog = PersonManagerDialog(self, self.db)
        dialog.exec_()
        # Recharger les tâches après la modification des personnes
        self.load_tasks()

    def manage_equipment(self):
        from gui.equipment_manager import EquipmentManagerDialog
        dialog = EquipmentManagerDialog(self, self.db)
        dialog.exec_()
        # Recharger les tâches après la modification des équipements
        self.load_tasks()

    def manage_attendance(self):
        from gui.attendance_manager import AttendanceManagerDialog
        dialog = AttendanceManagerDialog(self, self.db)
        dialog.exec_()

    def show_advanced_search(self):
        """Affiche la boîte de dialogue de recherche avancée"""
        from gui.advanced_search import AdvancedSearchDialog

        dialog = AdvancedSearchDialog(self, self.db)
        dialog.search_completed.connect(self.apply_search_filters)
        dialog.exec_()

    def apply_search_filters(self, filters):
        """Applique les filtres de recherche"""
        if filters:
            self.active_filters = filters
            self.load_tasks(filters)

            # Afficher une notification
            self.notification_manager.show_notification(
                "Recherche effectuée",
                f"La recherche a trouvé {self.task_table.rowCount()} tâches.",
                "info"
            )
        else:
            self.clear_filters()

    def clear_filters(self):
        """Efface les filtres actifs"""
        self.active_filters = None
        self.load_tasks()

    def update_task_status(self, task_id, new_status, waiting_reason=None):
        """Met à jour le statut d'une tâche et vérifie si c'est une tâche récurrente"""
        # Mettre à jour le statut dans la base de données
        self.db.update_task_status(task_id, new_status, waiting_reason)

        # Si la tâche est marquée comme terminée et qu'elle est récurrente, créer la prochaine occurrence
        if new_status == "Terminée":
            self.check_recurring_task(task_id)

    def split_task(self, task_id):
        """Divise une tâche en plusieurs sous-tâches"""
        task = self.db.get_task(task_id)
        if task:
            dialog = SplitTaskDialog(self, self.db, task_id)
            if dialog.exec_() == QDialog.Accepted:
                subtasks_data = dialog.get_subtasks_data()

                try:
                    self.db.split_task(task_id, subtasks_data)

                    # Mettre à jour l'affichage
                    self.load_tasks()

                    QMessageBox.information(
                        self,
                        "Tâche divisée",
                        f"La tâche '{task[2]}' a été divisée en {len(subtasks_data)} sous-tâches."
                    )
                except Exception as e:
                    QMessageBox.critical(
                        self,
                        "Erreur",
                        f"Une erreur s'est produite lors de la division de la tâche : {str(e)}"
                    )

    def show_task_context_menu(self, position):
        """Affiche un menu contextuel pour la tâche sélectionnée dans la table principale"""
        row = self.task_table.rowAt(position.y())
        if row < 0:
            return

        # Récupérer l'ID de la tâche
        task_id = self.task_table.item(row, 0).data(Qt.UserRole)

        # Créer le menu contextuel
        context_menu = QMenu()

        # Actions
        edit_action = QAction("✏️ Modifier", self)
        edit_action.triggered.connect(lambda: self.edit_task(row, 0))
        context_menu.addAction(edit_action)

        # Ajouter les actions de changement de statut
        status_menu = QMenu("Changer le statut", context_menu)

        todo_action = QAction("À faire", self)
        todo_action.triggered.connect(lambda: self.update_task_status(task_id, "À faire"))
        status_menu.addAction(todo_action)

        in_progress_action = QAction("En cours", self)
        in_progress_action.triggered.connect(lambda: self.update_task_status(task_id, "En cours"))
        status_menu.addAction(in_progress_action)

        waiting_action = QAction("En attente", self)
        waiting_action.triggered.connect(lambda: self.show_waiting_reason_dialog(task_id))
        status_menu.addAction(waiting_action)

        completed_action = QAction("Terminée", self)
        completed_action.triggered.connect(lambda: self.update_task_status(task_id, "Terminée"))
        status_menu.addAction(completed_action)

        context_menu.addMenu(status_menu)

        # Ajouter l'action pour diviser la tâche
        split_action = QAction("✂️ Diviser la tâche", self)
        split_action.triggered.connect(lambda: self.split_task(task_id))
        context_menu.addAction(split_action)

        # Ajouter l'action de suppression
        delete_action = QAction("🗑️ Supprimer", self)
        delete_action.triggered.connect(lambda: self.delete_task(task_id))
        context_menu.addAction(delete_action)

        # Afficher le menu
        context_menu.exec_(self.task_table.viewport().mapToGlobal(position))

    def show_waiting_reason_dialog(self, task_id):
        """Affiche une boîte de dialogue pour saisir la raison d'attente"""
        from PyQt5.QtWidgets import QInputDialog
        reason, ok = QInputDialog.getMultiLineText(
            self,
            "Raison de l'attente",
            "Veuillez indiquer la raison pour laquelle cette tâche est en attente:",
            ""
        )

        if ok:
            self.update_task_status(task_id, "En attente", reason)

    def delete_task(self, task_id):
        """Supprime une tâche"""
        # Vérifier si nous sommes dans le bon onglet
        if not hasattr(self, 'task_table') or self.tab_widget.currentIndex() != 1:
            QMessageBox.warning(self, "Attention", "Veuillez sélectionner une tâche dans la liste des tâches.")
            return

        reply = QMessageBox.question(
            self, 'Confirmation',
            "Êtes-vous sûr de vouloir supprimer cette tâche ?",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                print(f"Tentative de suppression de la tâche {task_id}")  # Log
                
                # Vérifier si la tâche existe
                task = self.db.get_task(task_id)
                if not task:
                    raise Exception(f"La tâche {task_id} n'existe pas")

                # Tenter la suppression
                if self.db.delete_task(task_id):
                    # Vider la table des tâches
                    self.task_table.setRowCount(0)
                    # Recharger les tâches
                    self.load_tasks()
                    self.notification_manager.show_notification(
                        "Tâche supprimée",
                        f"La tâche {task_id} a été supprimée avec succès.",
                        "success"
                    )
                else:
                    raise Exception("La suppression a échoué")
            except Exception as e:
                print(f"Erreur lors de la suppression de la tâche {task_id} : {str(e)}")  # Log
                self.notification_manager.show_notification(
                    "Erreur",
                    f"Erreur lors de la suppression de la tâche {task_id} : {str(e)}",
                    "error"
                )

    def check_recurring_task(self, task_id):
        """Vérifie si la tâche est récurrente et crée la prochaine occurrence si nécessaire"""
        task = self.db.get_task(task_id)

        # Vérifier si c'est une tâche récurrente
        if task and len(task) > 9 and task[9]:  # is_recurring
            # Créer la prochaine occurrence
            new_task_id = self.db.create_next_recurring_task(task_id)

            if new_task_id:
                QMessageBox.information(
                    self,
                    "Tâche récurrente",
                    f"La tâche '{task[2]}' a été marquée comme terminée. Une nouvelle occurrence a été créée."
                )
                # Recharger les tâches pour afficher la nouvelle occurrence
                self.load_tasks()

    def print_tasks(self):
        printer = QPrinter(QPrinter.HighResolution)
        dialog = QPrintDialog(printer, self)
        if dialog.exec_() == QPrintDialog.Accepted:
            printer.setPageOrientation(QPageLayout.Landscape)
            # Créer un document pour l'impression
            document = QTextDocument()
            cursor = QTextCursor(document)

            # Créer un tableau HTML
            html = "<h1>Liste des tâches</h1>"
            html += "<table border='1' cellspacing='0' cellpadding='4' width='100%'>"
            html += "<tr bgcolor='#f1f2f6'>"
            headers = ["Titre", "Description", "Catégorie", "Affectation", "Priorité",
                      "Statut", "Date de début", "Date de fin", "Durée"]
            html += "".join([f"<th>{h}</th>" for h in headers])
            html += "</tr>"

            # Ajouter les données
            tasks = self.db.get_all_tasks()
            for task in tasks:
                category = self.db.get_category_by_id(task[1])
                person = self.db.get_person_by_id(task[7]) if task[7] else None

                start_date = QDateTime.fromString(task[4], 'yyyy-MM-dd HH:mm') if task[4] else None

                # Calculer la date de fin pour les tâches de maintenance
                end_date = ""
                if category and category[1] == "Maintenance" and task[7]:
                    start = start_date.toPyDateTime() if start_date else None
                    if start:
                        duration_hours = int(task[7]) if task[7] is not None else 0
                        days = int(duration_hours / 8)
                        remaining_hours = duration_hours % 8
                        end = start + timedelta(days=days, hours=remaining_hours)
                        end_date = end.strftime("%d/%m/%Y %H:%M")

                row = [
                    task[2],  # Titre
                    task[3],  # Description
                    category[1] if category else "",  # Catégorie
                    f"{person[1]} ({person[2]})" if person else "Non assigné",  # Affectation
                    task[5],  # Priorité
                    task[6],  # Statut
                    start_date.toString('dd/MM/yyyy HH:mm') if start_date else "",  # Date de début
                    end_date,  # Date de fin
                    f"{task[7]} heures" if task[7] else ""  # Durée
                ]

                html += "<tr>" + "".join([f"<td>{cell}</td>" for cell in row]) + "</tr>"

            html += "</table>"
            document.setHtml(html)
            document.print_(printer)

    def print_calendar(self):
        printer = QPrinter(QPrinter.HighResolution)
        dialog = QPrintDialog(printer, self)
        if dialog.exec_() == QPrintDialog.Accepted:
            # Capture le widget du calendrier
            calendar_tab = self.tab_widget.findChild(CalendarView)
            if calendar_tab:
                # Créer une image du calendrier
                pixmap = QPixmap(calendar_tab.size())
                calendar_tab.render(pixmap)

                # Créer un peintre pour l'impression
                painter = QPainter()
                painter.begin(printer)

                # Calculer le ratio pour ajuster l'image à la page
                rect = painter.viewport()
                size = pixmap.size()
                size.scale(rect.size(), Qt.KeepAspectRatio)
                painter.setViewport(rect.x(), rect.y(), size.width(), size.height())
                painter.setWindow(pixmap.rect())

                # Dessiner l'image
                painter.drawPixmap(0, 0, pixmap)
                painter.end()

    def print_dashboard(self):
        if not HAS_CHARTS:
            QMessageBox.information(self, "Information", "L'impression du tableau de bord est désactivée car le module PyQt5.QtChart n'est pas installé.")
            return

        printer = QPrinter(QPrinter.HighResolution)
        dialog = QPrintDialog(printer, self)
        if dialog.exec_() == QPrintDialog.Accepted:
            # Trouver l'onglet parent du tableau de bord
            dashboard_tab = self.tab_widget.widget(0)  # Premier onglet

            if isinstance(dashboard_tab, QTabWidget):
                # C'est un onglet parent, récupérer le sous-onglet actuel
                current_tab_index = dashboard_tab.currentIndex()
                dashboard = dashboard_tab.widget(current_tab_index)

                # Créer une image du tableau de bord
                pixmap = QPixmap(dashboard.size())
                dashboard.render(pixmap)

                # Créer un peintre pour l'impression
                painter = QPainter()
                painter.begin(printer)

                # Calculer le ratio pour ajuster l'image à la page
                rect = painter.viewport()
                size = pixmap.size()
                size.scale(rect.size(), Qt.KeepAspectRatio)
                painter.setViewport(rect.x(), rect.y(), size.width(), size.height())
                painter.setWindow(pixmap.rect())

                # Dessiner l'image
                painter.drawPixmap(0, 0, pixmap)
                painter.end()
            else:
                QMessageBox.warning(self, "Erreur", "Impossible de trouver le tableau de bord à imprimer.")

    def manage_equipment(self):
        """Ouvre la boîte de dialogue de gestion des équipements"""
        from gui.equipment_manager import EquipmentManagerDialog
        dialog = EquipmentManagerDialog(self, self.db)
        dialog.exec_()

    def manage_spare_parts(self):
        """Ouvre la boîte de dialogue de gestion des pièces de rechange"""
        dialog = SparePartsManagerDialog(self, self.db)
        dialog.exec_()
        # Recharger les alertes après la fermeture du dialogue
        self.load_spare_parts_alerts()

    def load_spare_parts_alerts(self):
        """Charge les pièces sous le seuil d'alerte"""
        if not hasattr(self, 'alerts_table'):
            return

        try:
            parts_below_threshold = self.db.get_spare_parts_below_threshold()

            self.alerts_table.setRowCount(len(parts_below_threshold))

            for row, part in enumerate(parts_below_threshold):
                try:
                    # Code
                    self.alerts_table.setItem(row, 0, QTableWidgetItem(part[1]))  # code

                    # Désignation
                    self.alerts_table.setItem(row, 1, QTableWidgetItem(part[2]))  # name

                    # Quantité actuelle
                    quantity_item = QTableWidgetItem(str(part[3]))  # quantity
                    quantity_item.setTextAlignment(Qt.AlignCenter)
                    quantity_item.setForeground(QBrush(QColor("#e74c3c")))  # Rouge
                    quantity_item.setFont(QFont("", -1, QFont.Bold))
                    self.alerts_table.setItem(row, 2, quantity_item)

                    # Seuil d'alerte
                    threshold_item = QTableWidgetItem(str(part[6]))  # min_threshold
                    threshold_item.setTextAlignment(Qt.AlignCenter)
                    self.alerts_table.setItem(row, 3, threshold_item)

                    # Manque
                    missing = part[6] - part[3]  # min_threshold - quantity
                    missing_item = QTableWidgetItem(str(missing))
                    missing_item.setTextAlignment(Qt.AlignCenter)
                    missing_item.setForeground(QBrush(QColor("#e74c3c")))  # Rouge
                    missing_item.setFont(QFont("", -1, QFont.Bold))
                    self.alerts_table.setItem(row, 4, missing_item)
                except (IndexError, TypeError) as e:
                    print(f"Erreur lors du traitement d'une pièce : {str(e)}")
                    continue
        except Exception as e:
            print(f"Erreur lors du chargement des alertes de pièces : {str(e)}")

        # Mettre à jour la barre de statut
        self.update_status_bar()

    def update_datetime(self):
        """Met à jour la date et l'heure dans la barre de statut"""
        current_datetime = QDateTime.currentDateTime()
        formatted_datetime = current_datetime.toString("dd/MM/yyyy HH:mm:ss")
        self.datetime_label.setText(formatted_datetime)

    def update_breadcrumb(self, index=None):
        """Met à jour le fil d'Ariane en fonction de l'onglet actuel"""
        if index is None:
            index = self.tab_widget.currentIndex()

        # Récupérer le nom de l'onglet principal
        section_name = self.tab_widget.tabText(index).strip()
        self.current_section_label.setText(section_name)

        # Vérifier si c'est un onglet parent avec des sous-onglets
        current_tab = self.tab_widget.widget(index)
        if isinstance(current_tab, QTabWidget):
            # C'est un onglet parent, récupérer le nom du sous-onglet actuel
            sub_index = current_tab.currentIndex()
            subsection_name = current_tab.tabText(sub_index).strip()

            # Afficher le sous-onglet dans le fil d'Ariane
            self.current_subsection_label.setText(subsection_name)
            self.current_subsection_label.setVisible(True)
            self.subsection_separator.setVisible(True)

            # Connecter le changement de sous-onglet
            if not hasattr(current_tab, '_breadcrumb_connected'):
                current_tab.currentChanged.connect(self.update_breadcrumb)
                current_tab._breadcrumb_connected = True
        else:
            # C'est un onglet simple, masquer le sous-onglet
            self.current_subsection_label.setVisible(False)
            self.subsection_separator.setVisible(False)

    def update_status_bar(self):
        """Met à jour les informations de la barre de statut"""
        # Mettre à jour le nombre de tâches
        try:
            tasks = self.db.get_all_tasks()
            tasks_in_progress = [t for t in tasks if t[6] == "En cours"]
            tasks_waiting = [t for t in tasks if t[6] == "En attente"]
            tasks_todo = [t for t in tasks if t[6] == "À faire"]

            self.tasks_count_label.setText(
                f"Tâches: {len(tasks)} | En cours: {len(tasks_in_progress)} | "
                f"En attente: {len(tasks_waiting)} | À faire: {len(tasks_todo)}"
            )
        except Exception as e:
            print(f"Erreur lors de la mise à jour du nombre de tâches : {str(e)}")
            self.tasks_count_label.setText("Tâches: N/A")

        # Mettre à jour le nombre d'alertes de stock
        try:
            parts_below_threshold = self.db.get_spare_parts_below_threshold()
            if parts_below_threshold:
                self.alerts_count_label.setText(f"⚠️ Alertes de stock: {len(parts_below_threshold)}")
                self.alerts_count_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
            else:
                self.alerts_count_label.setText("Alertes de stock: 0")
                self.alerts_count_label.setStyleSheet("")
        except Exception as e:
            print(f"Erreur lors de la mise à jour des alertes de stock : {str(e)}")
            self.alerts_count_label.setText("Alertes de stock: N/A")

        # Mettre à jour la date et l'heure
        self.update_datetime()

    def setup_shortcuts(self):
        """Configure les raccourcis clavier globaux"""
        # Raccourci pour ajouter une nouvelle tâche (Ctrl+N)
        new_task_shortcut = QShortcut(QKeySequence("Ctrl+N"), self)
        new_task_shortcut.activated.connect(self.add_task)

        # Raccourci pour la recherche avancée (Ctrl+F)
        search_shortcut = QShortcut(QKeySequence("Ctrl+F"), self)
        search_shortcut.activated.connect(self.show_advanced_search)

        # Raccourci pour retourner à l'accueil (Ctrl+H)
        home_shortcut = QShortcut(QKeySequence("Ctrl+H"), self)
        home_shortcut.activated.connect(self.return_to_home_wrapper)

        # Raccourci pour naviguer entre les onglets (Ctrl+Tab et Ctrl+Shift+Tab)
        next_tab_shortcut = QShortcut(QKeySequence("Ctrl+Tab"), self)
        next_tab_shortcut.activated.connect(self.next_tab)

        prev_tab_shortcut = QShortcut(QKeySequence("Ctrl+Shift+Tab"), self)
        prev_tab_shortcut.activated.connect(self.prev_tab)

        # Raccourci pour actualiser les données (F5)
        refresh_shortcut = QShortcut(QKeySequence("F5"), self)
        refresh_shortcut.activated.connect(self.refresh_data)

        # Raccourci pour l'aide (F1)
        help_shortcut = QShortcut(QKeySequence("F1"), self)
        help_shortcut.activated.connect(lambda: self.help_system.show_help())

    def return_to_home_wrapper(self):
        """Wrapper pour retourner à l'écran d'accueil"""
        # Trouver le parent SotramineApp
        parent = self.parent()
        while parent and not hasattr(parent, 'return_to_home'):
            parent = parent.parent()

        if parent and hasattr(parent, 'return_to_home'):
            parent.return_to_home()
        else:
            print("Impossible de trouver la méthode return_to_home dans les parents")

    def next_tab(self):
        """Passe à l'onglet suivant"""
        current_index = self.tab_widget.currentIndex()
        next_index = (current_index + 1) % self.tab_widget.count()
        self.tab_widget.setCurrentIndex(next_index)

    def prev_tab(self):
        """Passe à l'onglet précédent"""
        current_index = self.tab_widget.currentIndex()
        prev_index = (current_index - 1) % self.tab_widget.count()
        self.tab_widget.setCurrentIndex(prev_index)

    def refresh_data(self):
        """Actualise les données de l'application"""
        # Afficher un message de statut
        self.status_label.setText("Actualisation des données...")

        # Recharger les tâches
        self.load_tasks()

        # Recharger les alertes de pièces de rechange
        self.load_spare_parts_alerts()

        # Mettre à jour la barre de statut
        self.status_label.setText("Données actualisées")
        QTimer.singleShot(3000, lambda: self.status_label.setText("Prêt"))

        # Afficher une notification
        self.notification_manager.show_notification(
            "Actualisation",
            "Les données ont été actualisées avec succès.",
            "success"
        )

    def open_configuration_wrapper(self):
        """Wrapper pour ouvrir la configuration"""
        # Trouver le parent SotramineApp
        parent = self.parent()
        while parent and not hasattr(parent, 'open_configuration'):
            parent = parent.parent()

        if parent and hasattr(parent, 'open_configuration'):
            parent.open_configuration()
        else:
            print("Impossible de trouver la méthode open_configuration dans les parents")

    def show_favorites(self):
        """Affiche la boîte de dialogue des favoris"""
        dialog = self.favorites_manager.show_favorites_dialog()

    def populate_favorites_menu(self, menu):
        """Remplit le menu des favoris"""
        # Récupérer tous les favoris
        favorites = self.favorites_manager.get_favorites()

        if not favorites:
            # Aucun favori
            no_favorites_action = QAction("Aucun favori", self)
            no_favorites_action.setEnabled(False)
            menu.addAction(no_favorites_action)
            return

        # Grouper les favoris par type
        favorites_by_type = {}
        for favorite in favorites:
            if favorite.type not in favorites_by_type:
                favorites_by_type[favorite.type] = []
            favorites_by_type[favorite.type].append(favorite)

        # Ajouter les favoris au menu
        for type_name, type_favorites in favorites_by_type.items():
            # Créer un sous-menu pour ce type
            type_menu = QMenu(self.get_type_display_name(type_name), self)
            menu.addMenu(type_menu)

            # Ajouter les favoris de ce type
            for favorite in type_favorites:
                favorite_action = QAction(favorite.name, self)
                favorite_action.triggered.connect(lambda checked, f=favorite: self.open_favorite(f))
                type_menu.addAction(favorite_action)

    def get_type_display_name(self, type_name):
        """Récupère le nom d'affichage d'un type de favori"""
        type_names = {
            'task': "Tâches",
            'equipment': "Équipements",
            'person': "Personnel",
            'report': "Rapports"
        }
        return type_names.get(type_name, type_name.capitalize())

    def open_favorite(self, favorite):
        """Ouvre un favori"""
        if favorite.type == 'task':
            # Ouvrir une tâche
            task_id = favorite.data.get('id')
            if task_id:
                self.edit_task(task_id=task_id)
        elif favorite.type == 'equipment':
            # Ouvrir un équipement
            equipment_id = favorite.data.get('id')
            if equipment_id:
                self.view_equipment(equipment_id)
        elif favorite.type == 'person':
            # Ouvrir une personne
            person_id = favorite.data.get('id')
            if person_id:
                self.view_person(person_id)
        elif favorite.type == 'report':
            # Ouvrir un rapport
            report_type = favorite.data.get('type')
            if report_type == 'daily':
                self.export_daily()
            elif report_type == 'monthly':
                self.export_monthly()

    def add_to_favorites(self, name, type, data=None):
        """Ajoute un élément aux favoris"""
        success = self.favorites_manager.add_favorite(name, type, data)
        if success:
            self.notification_manager.show_notification(
                "Favori ajouté",
                f"'{name}' a été ajouté à vos favoris.",
                "success"
            )
        return success

    def remove_from_favorites(self, name, type):
        """Supprime un élément des favoris"""
        success = self.favorites_manager.remove_favorite(name, type)
        if success:
            self.notification_manager.show_notification(
                "Favori supprimé",
                f"'{name}' a été supprimé de vos favoris.",
                "info"
            )
        return success

    def show_about(self):
        """Affiche la boîte de dialogue À propos"""
        about_text = """
        <h1>SOTRAMINE PHOSPHATE</h1>
        <p>Version 1.0.0</p>
        <p>Système de Gestion de Maintenance</p>
        <p>&copy; 2025 SOTRAMINE PHOSPHATE - Tous droits réservés</p>
        <p>Cette application permet de gérer les tâches de maintenance, les équipements,
        les pièces de rechange et le personnel de l'entreprise SOTRAMINE PHOSPHATE.</p>
        """

        # Créer la boîte de dialogue
        about_dialog = QDialog(self)
        about_dialog.setWindowTitle("À propos")
        about_dialog.setMinimumSize(400, 300)

        # Layout principal
        layout = QVBoxLayout(about_dialog)

        # Logo
        logo_label = QLabel()
        logo_pixmap = QPixmap("resources/images/logo.png")
        if not logo_pixmap.isNull():
            logo_pixmap = logo_pixmap.scaled(200, 100, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            logo_label.setPixmap(logo_pixmap)
            logo_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(logo_label)

        # Texte
        text_label = QLabel(about_text)
        text_label.setTextFormat(Qt.RichText)
        text_label.setAlignment(Qt.AlignCenter)
        text_label.setWordWrap(True)
        layout.addWidget(text_label)

        # Bouton OK
        ok_button = QPushButton("OK")
        ok_button.clicked.connect(about_dialog.accept)
        layout.addWidget(ok_button, alignment=Qt.AlignCenter)

        # Afficher la boîte de dialogue
        about_dialog.exec_()

    def edit_task(self, row, _=None, task_id=None):
        # Vérifier si nous sommes dans le bon onglet
        if self.tab_widget.currentIndex() != 1:  # Onglet "Gestion des tâches"
            return

        # Si task_id n'est pas fourni, le récupérer depuis la ligne sélectionnée
        if task_id is None:
            task_id = self.task_table.item(row, 0).data(Qt.UserRole)

        # Récupérer les données de la tâche
        task = self.db.get_task(task_id)
        if not task:
            return

        print(f"Tâche trouvée : {task}")  # Debug

        # Récupérer toutes les catégories
        categories = self.db.get_all_categories()

        # Créer et afficher la boîte de dialogue
        dialog = TaskDialog(self, task=task, categories=categories, db=self.db)
        if dialog.exec_() == QDialog.Accepted:
            # Récupérer les données de la tâche
            task_data = dialog.get_task_data()
            print(f"Données de la tâche à mettre à jour : {task_data}")  # Debug

            try:
                # Mettre à jour la tâche
                self.db.update_task(
                    task_id=task_id,
                    category_id=task_data['category_id'],
                    title=task_data['title'],
                    description=task_data['description'],
                    due_date=task_data['due_date'],
                    priority=task_data['priority'],
                    status=task_data['status'],
                    assigned_to_ids=task_data['assigned_to_ids'],
                    estimated_time=task_data['estimated_time'],
                    is_recurring=task_data['is_recurring'],
                    recurrence_type=task_data['recurrence_type'],
                    recurrence_end_date=task_data['recurrence_end_date'],
                    is_maintenance_checklist=task_data['is_maintenance_checklist'],
                    waiting_reason=task_data['waiting_reason'],
                    equipment_id=task_data['equipment_id']
                )

                # Recharger les tâches
                self.load_tasks()

                # Afficher une notification de succès
                self.notification_manager.show_notification(
                    "Succès",
                    "La tâche a été mise à jour avec succès",
                    "success"
                )

            except Exception as e:
                print(f"Erreur lors de la mise à jour de la tâche : {str(e)}")  # Debug
                self.notification_manager.show_notification(
                    "Erreur",
                    f"Erreur lors de la modification de la tâche : {str(e)}",
                    "error"
                )


class ModernMainWindow(QMainWindow):
    """Nouvelle fenêtre principale moderne avec interface optimisée"""

    def __init__(self, db, excel_exporter):
        super().__init__()
        self.db = db
        self.excel_exporter = excel_exporter
        self.notification_manager = NotificationManager(self)
        self.help_system = HelpSystem(self)
        self.favorites_manager = FavoritesManager(self)

        # Gestionnaire d'actions centralisé
        self.action_manager = ActionManager(self)
        self.action_manager.action_triggered.connect(self.handle_action)

        # État de l'interface
        self.current_view = 'dashboard'
        self.content_widgets = {}

        self.setup_ui()
        self.load_initial_data()

    def setup_ui(self):
        """Configure l'interface utilisateur moderne"""
        self.setWindowTitle("SOTRAMINE PHOSPHATE - Système de Gestion de Maintenance")
        self.setMinimumSize(1400, 900)

        # Appliquer le style global
        self.setStyleSheet(APP_STYLE)

        # Widget central principal
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Layout principal horizontal
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Menu latéral
        self.sidebar = SidebarMenu(self)
        self.sidebar.menu_item_clicked.connect(self.navigate_to_section)
        main_layout.addWidget(self.sidebar)

        # Zone de contenu principal
        content_area = QWidget()
        content_layout = QVBoxLayout(content_area)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)

        # Barre d'outils contextuelle
        self.contextual_toolbar = ContextualToolbar(self)
        self.contextual_toolbar.action_triggered.connect(self.handle_toolbar_action)
        self.contextual_toolbar.search_requested.connect(self.handle_search)
        content_layout.addWidget(self.contextual_toolbar)

        # Zone de contenu avec onglets
        self.content_stack = QStackedWidget()
        content_layout.addWidget(self.content_stack)

        main_layout.addWidget(content_area)

        # Créer les vues de contenu
        self.create_content_views()

        # Configurer la vue initiale
        self.navigate_to_section('dashboard')

        # Barre de statut moderne
        self.setup_status_bar()

    def create_content_views(self):
        """Crée toutes les vues de contenu"""
        # Vue tableau de bord
        self.dashboard_view = self.create_dashboard_view()
        self.content_widgets['dashboard'] = self.dashboard_view
        self.content_stack.addWidget(self.dashboard_view)

        # Vue tâches
        self.tasks_view = self.create_tasks_view()
        self.content_widgets['tasks'] = self.tasks_view
        self.content_stack.addWidget(self.tasks_view)

        # Vue équipements
        self.equipment_view = self.create_equipment_view()
        self.content_widgets['equipment'] = self.equipment_view
        self.content_stack.addWidget(self.equipment_view)

        # Vue personnel
        self.personnel_view = self.create_personnel_view()
        self.content_widgets['personnel'] = self.personnel_view
        self.content_stack.addWidget(self.personnel_view)

        # Vue pièces de rechange
        self.spare_parts_view = self.create_spare_parts_view()
        self.content_widgets['spare_parts'] = self.spare_parts_view
        self.content_stack.addWidget(self.spare_parts_view)

        # Vue pointage
        self.attendance_view = self.create_attendance_view()
        self.content_widgets['attendance'] = self.attendance_view
        self.content_stack.addWidget(self.attendance_view)

        # Vue rapports
        self.reports_view = self.create_reports_view()
        self.content_widgets['reports'] = self.reports_view
        self.content_stack.addWidget(self.reports_view)

    def create_dashboard_view(self):
        """Crée la vue tableau de bord"""
        from gui.dashboard_view import DashboardView
        return DashboardView(self, self.db)

    def create_tasks_view(self):
        """Crée la vue de gestion des tâches"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Table des tâches (réutiliser la logique existante)
        self.task_table = QTableWidget()
        layout.addWidget(self.task_table)

        return widget

    def create_equipment_view(self):
        """Crée la vue de gestion des équipements"""
        return EquipmentViewTab(self, self.db)

    def create_personnel_view(self):
        """Crée la vue de gestion du personnel"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Table du personnel
        self.personnel_table = QTableWidget()
        layout.addWidget(self.personnel_table)

        return widget

    def create_spare_parts_view(self):
        """Crée la vue de gestion des pièces de rechange"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Table des pièces
        self.spare_parts_table = QTableWidget()
        layout.addWidget(self.spare_parts_table)

        return widget

    def create_attendance_view(self):
        """Crée la vue de gestion du pointage"""
        from gui.attendance_manager import AttendanceManagerDialog
        return AttendanceManagerDialog(self, self.db)

    def create_reports_view(self):
        """Crée la vue des rapports"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # Interface de génération de rapports
        reports_label = QLabel("Génération de rapports")
        reports_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 20px;")
        layout.addWidget(reports_label)

        return widget

    def navigate_to_section(self, section_id):
        """Navigue vers une section spécifique"""
        if section_id in self.content_widgets:
            self.current_view = section_id
            self.content_stack.setCurrentWidget(self.content_widgets[section_id])
            self.sidebar.set_active_item(section_id)

            # Mettre à jour la barre d'outils contextuelle
            section_titles = {
                'dashboard': '📊 Tableau de bord',
                'tasks': '📋 Gestion des tâches',
                'equipment': '🔌 Gestion des équipements',
                'personnel': '👥 Gestion du personnel',
                'spare_parts': '🔧 Pièces de rechange',
                'attendance': '📊 Pointage du personnel',
                'reports': '📄 Rapports et analyses'
            }

            title = section_titles.get(section_id, section_id.title())
            self.contextual_toolbar.set_context(section_id, title)

            # Charger les données si nécessaire
            self.load_section_data(section_id)

    def load_section_data(self, section_id):
        """Charge les données pour une section"""
        if section_id == 'tasks':
            self.load_tasks()
        elif section_id == 'personnel':
            self.load_personnel()
        elif section_id == 'spare_parts':
            self.load_spare_parts()

    def handle_action(self, action_id, data):
        """Gère les actions du gestionnaire d'actions"""
        if action_id == 'home':
            self.navigate_to_section('dashboard')
        elif action_id == 'new_task':
            self.add_task()
        elif action_id == 'search_tasks':
            # Activer la recherche
            pass
        elif action_id == 'manage_equipment':
            self.navigate_to_section('equipment')
        elif action_id == 'manage_personnel':
            self.navigate_to_section('personnel')
        elif action_id == 'manage_attendance':
            self.navigate_to_section('attendance')
        elif action_id == 'settings':
            self.open_settings()
        elif action_id == 'help':
            self.help_system.show_help()
        elif action_id == 'exit':
            self.close()

    def handle_toolbar_action(self, action_id, data):
        """Gère les actions de la barre d'outils contextuelle"""
        if action_id == 'new_task':
            self.add_task()
        elif action_id == 'new_equipment':
            self.add_equipment()
        elif action_id == 'new_person':
            self.add_person()
        elif action_id == 'new_spare_part':
            self.add_spare_part()
        elif action_id == 'refresh_dashboard':
            self.refresh_dashboard()
        elif action_id == 'export_tasks':
            self.export_tasks()

    def handle_search(self, search_text):
        """Gère la recherche dans la section actuelle"""
        if self.current_view == 'tasks':
            self.search_tasks(search_text)
        elif self.current_view == 'personnel':
            self.search_personnel(search_text)
        elif self.current_view == 'spare_parts':
            self.search_spare_parts(search_text)

    def setup_status_bar(self):
        """Configure la barre de statut moderne"""
        status_bar = self.statusBar()
        status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #f8f9fa;
                border-top: 1px solid #e9ecef;
                color: #6c757d;
                font-size: 12px;
            }
        """)

        # Statut principal
        self.status_label = QLabel("Prêt")
        status_bar.addWidget(self.status_label)

        # Informations sur les données
        status_bar.addPermanentWidget(QFrame())  # Séparateur
        self.data_info_label = QLabel()
        status_bar.addPermanentWidget(self.data_info_label)

        self.update_status_info()

    def update_status_info(self):
        """Met à jour les informations de statut"""
        try:
            task_count = len(self.db.get_all_tasks())
            person_count = len(self.db.get_all_persons())
            equipment_count = len(self.db.get_all_equipment())

            info_text = f"Tâches: {task_count} | Personnel: {person_count} | Équipements: {equipment_count}"
            self.data_info_label.setText(info_text)
        except Exception as e:
            self.data_info_label.setText("Erreur de chargement des données")

    def load_initial_data(self):
        """Charge les données initiales"""
        self.update_status_info()
        self.sidebar.update_status("Données chargées")

    # Méthodes de compatibilité avec l'ancienne interface
    def load_tasks(self):
        """Charge les tâches (méthode de compatibilité)"""
        pass

    def load_personnel(self):
        """Charge le personnel (méthode de compatibilité)"""
        pass

    def load_spare_parts(self):
        """Charge les pièces de rechange (méthode de compatibilité)"""
        pass

    def add_task(self):
        """Ajoute une nouvelle tâche (méthode de compatibilité)"""
        pass

    def add_equipment(self):
        """Ajoute un nouvel équipement (méthode de compatibilité)"""
        pass

    def add_person(self):
        """Ajoute une nouvelle personne (méthode de compatibilité)"""
        pass

    def add_spare_part(self):
        """Ajoute une nouvelle pièce de rechange (méthode de compatibilité)"""
        pass

    def refresh_dashboard(self):
        """Actualise le tableau de bord (méthode de compatibilité)"""
        pass

    def export_tasks(self):
        """Exporte les tâches (méthode de compatibilité)"""
        pass

    def search_tasks(self, text):
        """Recherche dans les tâches (méthode de compatibilité)"""
        pass

    def search_personnel(self, text):
        """Recherche dans le personnel (méthode de compatibilité)"""
        pass

    def search_spare_parts(self, text):
        """Recherche dans les pièces de rechange (méthode de compatibilité)"""
        pass

    def open_settings(self):
        """Ouvre les paramètres (méthode de compatibilité)"""
        pass