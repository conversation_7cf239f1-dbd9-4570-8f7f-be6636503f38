import pandas as pd
from datetime import datetime
import os
from openpyxl.styles import <PERSON><PERSON><PERSON><PERSON>, Font

# Vérifier si les graphiques sont disponibles
HAS_CHARTS = True
try:
    from openpyxl.chart import <PERSON><PERSON><PERSON>, Pie<PERSON>hart, Reference
except ImportError:
    HAS_CHARTS = False
    print("Module openpyxl.chart non disponible. Les graphiques seront désactivés.")

class ExcelExporter:
    def __init__(self, db):
        self.db = db
        self.export_dir = "export"
        os.makedirs(self.export_dir, exist_ok=True)

    def _add_charts(self, worksheet, data, start_row, title):
        # Vérifier si les graphiques sont disponibles
        if not HAS_CHARTS:
            return

        # Créer un graphique en barres
        chart = BarChart()
        chart.title = title
        chart.y_axis.title = 'Nombre de tâches'
        chart.x_axis.title = 'Catégories'

        data = Reference(worksheet, min_col=1, min_row=start_row,
                        max_row=start_row + len(data) - 1, max_col=2)
        cats = Reference(worksheet, min_col=1, min_row=start_row + 1,
                        max_row=start_row + len(data) - 1)

        chart.add_data(data, titles_from_data=True)
        chart.set_categories(cats)

        # Placer le graphique dans la feuille
        worksheet.add_chart(chart, f"D{start_row}")

    def export_daily_report(self, date=None):
        if date is None:
            date = datetime.now().strftime('%Y-%m-%d')

        # Récupérer toutes les tâches avec leurs catégories et personnes assignées
        self.db.cursor.execute("""
            SELECT t.*, c.name as category_name, c.color as category_color,
                   p.name as assigned_to_name, p.role as assigned_role
            FROM tasks t
            LEFT JOIN categories c ON t.category_id = c.id
            LEFT JOIN persons p ON t.assigned_to_id = p.id
            WHERE date(t.due_date) = date(?)
        """, (date,))
        tasks = self.db.cursor.fetchall()

        # Créer un DataFrame pour les tâches
        tasks_df = pd.DataFrame(tasks, columns=[
            'ID', 'Category ID', 'Title', 'Description', 'Due Date',
            'Priority', 'Status', 'Assigned To ID', 'Estimated Time',
            'Created At', 'Category', 'Category Color', 'Assigned To', 'Role'
        ])

        # Récupérer les sous-tâches pour chaque tâche
        subtasks_data = []
        for task_id in tasks_df['ID']:
            self.db.cursor.execute("""
                SELECT s.*, p.name as assigned_to_name, p.role as assigned_role
                FROM subtasks s
                LEFT JOIN persons p ON s.assigned_to_id = p.id
                WHERE s.task_id = ?
            """, (task_id,))
            subtasks = self.db.cursor.fetchall()
            for subtask in subtasks:
                subtasks_data.append({
                    'Task ID': task_id,
                    'Subtask ID': subtask[0],
                    'Subtask Title': subtask[2],
                    'Subtask Status': subtask[3],
                    'Assigned To': subtask[6],
                    'Role': subtask[7],
                    'Estimated Time': subtask[5]
                })

        subtasks_df = pd.DataFrame(subtasks_data)

        # Créer les statistiques
        stats = {
            'Par catégorie': tasks_df.groupby('Category')['ID'].count(),
            'Par statut': tasks_df.groupby('Status')['ID'].count(),
            'Par priorité': tasks_df.groupby('Priority')['ID'].count(),
        }

        # Créer le fichier Excel
        filename = os.path.join(self.export_dir, f"rapport_journalier_{date}.xlsx")
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # Écrire les tâches
            tasks_df.to_excel(writer, sheet_name='Tâches', index=False)

            # Écrire les sous-tâches
            if not subtasks_df.empty:
                subtasks_df.to_excel(writer, sheet_name='Sous-tâches', index=False)

            # Écrire les statistiques
            stats_df = pd.DataFrame()
            row = 0
            for title, data in stats.items():
                stats_df.loc[row, 'Statistique'] = title
                row += 1
                for index, value in data.items():
                    stats_df.loc[row, 'Catégorie'] = index
                    stats_df.loc[row, 'Nombre'] = value
                    row += 1
                row += 2  # Espace entre les sections

            # Ne pas écrire la feuille Statistiques si elle est vide
            if not stats_df.empty:
                stats_df.to_excel(writer, sheet_name='Statistiques', index=False)

            # Formater les feuilles
            workbook = writer.book

            # Formater la feuille des tâches
            worksheet = writer.sheets['Tâches']
            for idx, row in tasks_df.iterrows():
                if not pd.isna(row['Category Color']):
                    color = row['Category Color'].strip('#')
                    fill = PatternFill(start_color=color, end_color=color, fill_type='solid')
                    cell = worksheet.cell(row=idx+2, column=tasks_df.columns.get_loc('Category')+1)
                    cell.fill = fill
                    if color in ['ffffff', 'FFFFFF']:  # Pour les couleurs claires
                        cell.font = Font(color='000000')
                    else:
                        cell.font = Font(color='FFFFFF')

            # Ajuster les largeurs de colonnes
            for worksheet in writer.sheets.values():
                for column in worksheet.columns:
                    max_length = 0
                    column = [cell for cell in column]
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = (max_length + 2)
                    worksheet.column_dimensions[column[0].column_letter].width = adjusted_width

            # Ajouter les graphiques dans la feuille Statistiques
            if 'Statistiques' in writer.sheets:
                worksheet = writer.sheets['Statistiques']
                current_row = 1
                for title, data in stats.items():
                    self._add_charts(worksheet, data, current_row, title)
                    current_row += len(data) + 3

        return filename

    def export_monthly_report(self, year=None, month=None):
        if year is None:
            year = datetime.now().year
        if month is None:
            month = datetime.now().month

        # Même logique que export_daily_report mais avec la condition de date modifiée
        self.db.cursor.execute("""
            SELECT t.*, c.name as category_name, c.color as category_color,
                   p.name as assigned_to_name, p.role as assigned_role
            FROM tasks t
            LEFT JOIN categories c ON t.category_id = c.id
            LEFT JOIN persons p ON t.assigned_to_id = p.id
            WHERE strftime('%Y-%m', t.due_date) = ?
        """, (f"{year}-{month:02d}",))
        tasks = self.db.cursor.fetchall()

        # Créer un DataFrame pour les tâches
        tasks_df = pd.DataFrame(tasks, columns=[
            'ID', 'Category ID', 'Title', 'Description', 'Due Date',
            'Priority', 'Status', 'Assigned To ID', 'Estimated Time',
            'Created At', 'Category', 'Category Color', 'Assigned To', 'Role'
        ])

        # Récupérer les sous-tâches pour chaque tâche
        subtasks_data = []
        for task_id in tasks_df['ID']:
            self.db.cursor.execute("""
                SELECT s.*, p.name as assigned_to_name, p.role as assigned_role
                FROM subtasks s
                LEFT JOIN persons p ON s.assigned_to_id = p.id
                WHERE s.task_id = ?
            """, (task_id,))
            subtasks = self.db.cursor.fetchall()
            for subtask in subtasks:
                subtasks_data.append({
                    'Task ID': task_id,
                    'Subtask ID': subtask[0],
                    'Subtask Title': subtask[2],
                    'Subtask Status': subtask[3],
                    'Assigned To': subtask[6],
                    'Role': subtask[7],
                    'Estimated Time': subtask[5]
                })

        subtasks_df = pd.DataFrame(subtasks_data)

        # Créer les statistiques
        stats = {
            'Par catégorie': tasks_df.groupby('Category')['ID'].count(),
            'Par statut': tasks_df.groupby('Status')['ID'].count(),
            'Par priorité': tasks_df.groupby('Priority')['ID'].count(),
        }

        # Créer le fichier Excel
        filename = os.path.join(self.export_dir, f"rapport_mensuel_{year}_{month:02d}.xlsx")
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # Écrire les tâches
            tasks_df.to_excel(writer, sheet_name='Tâches', index=False)

            # Écrire les sous-tâches
            if not subtasks_df.empty:
                subtasks_df.to_excel(writer, sheet_name='Sous-tâches', index=False)

            # Écrire les statistiques
            stats_df = pd.DataFrame()
            row = 0
            for title, data in stats.items():
                stats_df.loc[row, 'Statistique'] = title
                row += 1
                for index, value in data.items():
                    stats_df.loc[row, 'Catégorie'] = index
                    stats_df.loc[row, 'Nombre'] = value
                    row += 1
                row += 2  # Espace entre les sections

            # Ne pas écrire la feuille Statistiques si elle est vide
            if not stats_df.empty:
                stats_df.to_excel(writer, sheet_name='Statistiques', index=False)

            # Formater les feuilles
            workbook = writer.book

            # Formater la feuille des tâches
            worksheet = writer.sheets['Tâches']
            for idx, row in tasks_df.iterrows():
                if not pd.isna(row['Category Color']):
                    color = row['Category Color'].strip('#')
                    fill = PatternFill(start_color=color, end_color=color, fill_type='solid')
                    cell = worksheet.cell(row=idx+2, column=tasks_df.columns.get_loc('Category')+1)
                    cell.fill = fill
                    if color in ['ffffff', 'FFFFFF']:  # Pour les couleurs claires
                        cell.font = Font(color='000000')
                    else:
                        cell.font = Font(color='FFFFFF')

            # Ajuster les largeurs de colonnes
            for worksheet in writer.sheets.values():
                for column in worksheet.columns:
                    max_length = 0
                    column = [cell for cell in column]
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = (max_length + 2)
                    worksheet.column_dimensions[column[0].column_letter].width = adjusted_width

            # Ajouter les graphiques dans la feuille Statistiques
            if 'Statistiques' in writer.sheets:
                worksheet = writer.sheets['Statistiques']
                current_row = 1
                for title, data in stats.items():
                    self._add_charts(worksheet, data, current_row, title)
                    current_row += len(data) + 3

        return filename

    def export_maintenance_report(self, start_date=None, end_date=None):
        """Exporte un rapport spécifique pour les tâches de maintenance avec les interventions et pièces de rechange"""
        if start_date is None:
            # Par défaut, début du mois en cours
            today = datetime.now()
            start_date = datetime(today.year, today.month, 1).strftime('%Y-%m-%d')

        if end_date is None:
            # Par défaut, date du jour
            end_date = datetime.now().strftime('%Y-%m-%d')

        # Récupérer les tâches de maintenance
        self.db.cursor.execute("""
            SELECT t.*, c.name as category_name, c.color as category_color
            FROM tasks t
            JOIN categories c ON t.category_id = c.id
            WHERE c.name = 'Maintenance'
            AND date(t.due_date) BETWEEN date(?) AND date(?)
            ORDER BY t.due_date
        """, (start_date, end_date))
        tasks = self.db.cursor.fetchall()

        # Créer un DataFrame pour les tâches
        tasks_df = pd.DataFrame(tasks, columns=[
            'ID', 'Category ID', 'Title', 'Description', 'Due Date',
            'Priority', 'Status', 'Assigned To ID', 'Estimated Time',
            'Created At', 'Is Recurring', 'Recurrence Type', 'Recurrence End Date',
            'Parent Task ID', 'Is Maintenance Checklist', 'Category', 'Category Color'
        ])

        # Récupérer les interventions
        interventions_data = []
        for task in tasks:
            task_id = task[0]

            # Récupérer les interventions pour cette tâche
            interventions = self.db.get_maintenance_interventions(task_id)

            for intervention in interventions:
                intervention_id = intervention[0]

                # Calculer la durée en format lisible
                duration = intervention[5] or 0  # duration en minutes
                hours = duration // 60
                minutes = duration % 60
                duration_text = f"{hours}h {minutes:02d}min" if hours > 0 else f"{minutes} min"

                # Récupérer les pièces de rechange
                parts = self.db.get_spare_parts(intervention_id)
                parts_text = ", ".join([f"{part[2]} (x{part[3]})" for part in parts])

                # Calculer le coût total des pièces
                total_cost = sum([(part[5] or 0) * part[3] for part in parts])

                interventions_data.append({
                    'Task ID': task_id,
                    'Task Title': task[2],  # title
                    'Due Date': task[4],  # due_date
                    'Intervention ID': intervention_id,
                    'Intervention Date': intervention[2],  # intervention_date
                    'Technician': intervention[6] or "Non assigné",  # technician_name
                    'Duration': duration_text,
                    'Duration (min)': duration,
                    'Details': intervention[3] or "",  # details
                    'Spare Parts': parts_text,
                    'Total Cost': f"{total_cost:.2f} €" if total_cost > 0 else ""
                })

                # Ajouter les pièces de rechange individuelles
                for part in parts:
                    interventions_data.append({
                        'Task ID': task_id,
                        'Task Title': task[2],  # title
                        'Intervention ID': intervention_id,
                        'Intervention Date': intervention[2],  # intervention_date
                        'Part ID': part[0],
                        'Part Name': part[2],  # name
                        'Quantity': part[3],  # quantity
                        'Reference': part[4] or "",  # reference
                        'Unit Cost': f"{part[5]:.2f} €" if part[5] else "",  # cost
                        'Total Part Cost': f"{(part[5] or 0) * part[3]:.2f} €" if part[5] else ""
                    })

        # Créer les DataFrames
        interventions_df = pd.DataFrame([d for d in interventions_data if 'Part ID' not in d])
        parts_df = pd.DataFrame([d for d in interventions_data if 'Part ID' in d])

        # Statistiques
        stats = {}

        # Statistiques des tâches
        task_stats = {}
        task_stats["Nombre total de tâches"] = len(tasks_df)

        # Par statut
        status_counts = tasks_df.groupby('Status')['ID'].count()
        for status, count in status_counts.items():
            task_stats[f"Statut: {status}"] = count

        stats["Statistiques des tâches"] = task_stats

        # Statistiques des interventions
        if not interventions_df.empty:
            intervention_stats = {}
            intervention_stats["Nombre total d'interventions"] = len(interventions_df)

            # Durée totale
            total_duration = interventions_df['Duration (min)'].sum()
            hours = total_duration // 60
            minutes = total_duration % 60
            intervention_stats["Durée totale"] = f"{hours}h {minutes:02d}min"

            # Interventions par technicien
            tech_counts = interventions_df.groupby('Technician').size()
            for tech, count in tech_counts.items():
                intervention_stats[f"Technicien: {tech}"] = count

            stats["Statistiques des interventions"] = intervention_stats

        # Statistiques des pièces
        if not parts_df.empty:
            parts_stats = {}
            parts_stats["Nombre total de pièces utilisées"] = parts_df['Quantity'].sum()

            # Pièces les plus utilisées
            part_counts = parts_df.groupby('Part Name')['Quantity'].sum()
            for part, count in part_counts.nlargest(5).items():
                parts_stats[f"Pièce: {part}"] = count

            stats["Statistiques des pièces"] = parts_stats

        # Créer le fichier Excel
        filename = os.path.join(self.export_dir, f"rapport_maintenance_{start_date}_to_{end_date}.xlsx")
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # Écrire les tâches
            tasks_df.to_excel(writer, sheet_name='Tâches de maintenance', index=False)

            # Écrire les interventions
            if not interventions_df.empty:
                # Supprimer la colonne Duration (min) qui est juste pour les calculs
                interventions_df = interventions_df.drop(columns=['Duration (min)'])
                interventions_df.to_excel(writer, sheet_name='Interventions', index=False)

            # Écrire les pièces de rechange
            if not parts_df.empty:
                parts_df.to_excel(writer, sheet_name='Pièces de rechange', index=False)

            # Écrire les statistiques
            stats_df = pd.DataFrame()
            row = 0

            for section_title, section_data in stats.items():
                stats_df.loc[row, 'Statistique'] = section_title
                row += 1

                for label, value in section_data.items():
                    stats_df.loc[row, 'Catégorie'] = label
                    stats_df.loc[row, 'Nombre'] = value
                    row += 1

                row += 1  # Espace entre les sections

            # Ne pas écrire la feuille Statistiques si elle est vide
            if not stats_df.empty:
                stats_df.to_excel(writer, sheet_name='Statistiques', index=False)

            # Formater les feuilles
            workbook = writer.book

            # Ajuster les largeurs de colonnes
            for worksheet in writer.sheets.values():
                for column in worksheet.columns:
                    max_length = 0
                    column = [cell for cell in column]
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = (max_length + 2)
                    worksheet.column_dimensions[column[0].column_letter].width = adjusted_width

            # Ajouter les graphiques dans la feuille Statistiques si disponibles
            if 'Statistiques' in writer.sheets and HAS_CHARTS:
                worksheet = writer.sheets['Statistiques']
                current_row = row + 2

                # Graphique pour les statuts des tâches
                if "Statistiques des tâches" in stats:
                    data = {k: v for k, v in stats["Statistiques des tâches"].items() if k.startswith("Statut:")}
                    if data:
                        self._add_charts(worksheet, data, current_row, "Tâches par statut")
                        current_row += len(data) + 10

                # Graphique pour les techniciens
                if "Statistiques des interventions" in stats:
                    data = {k: v for k, v in stats["Statistiques des interventions"].items() if k.startswith("Technicien:")}
                    if data:
                        self._add_charts(worksheet, data, current_row, "Interventions par technicien")
                        current_row += len(data) + 10

                # Graphique pour les pièces
                if "Statistiques des pièces" in stats:
                    data = {k: v for k, v in stats["Statistiques des pièces"].items() if k.startswith("Pièce:")}
                    if data:
                        self._add_charts(worksheet, data, current_row, "Pièces les plus utilisées")

        return filename

    def export_tasks_to_excel(self, filename=None):
        """Exporte toutes les tâches vers Excel"""
        if filename is None:
            filename = os.path.join(self.export_dir, f"taches_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
        
        # Récupérer toutes les tâches avec leurs détails
        self.db.cursor.execute("""
            SELECT t.id, t.category_id, t.title, t.description, t.due_date,
                   t.priority, t.status, t.assigned_to_id, t.estimated_time,
                   t.created_at, c.name as category_name, c.color as category_color,
                   p.name as assigned_to_name, p.role as assigned_role
            FROM tasks t
            LEFT JOIN categories c ON t.category_id = c.id
            LEFT JOIN persons p ON t.assigned_to_id = p.id
            ORDER BY t.due_date DESC
        """)
        tasks = self.db.cursor.fetchall()
        
        # Créer un DataFrame
        tasks_df = pd.DataFrame(tasks, columns=[
            'ID', 'Category ID', 'Title', 'Description', 'Due Date',
            'Priority', 'Status', 'Assigned To ID', 'Estimated Time',
            'Created At', 'Category', 'Category Color', 'Assigned To', 'Role'
        ])
        
        # Exporter vers Excel
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            tasks_df.to_excel(writer, sheet_name='Tâches', index=False)
            
            # Ajouter des statistiques
            stats_data = {
                'Statistique': [
                    'Total des tâches',
                    'Tâches en cours',
                    'Tâches terminées',
                    'Tâches en retard',
                    'Tâches de haute priorité'
                ],
                'Valeur': [
                    len(tasks_df),
                    len(tasks_df[tasks_df['Status'] == 'En cours']),
                    len(tasks_df[tasks_df['Status'] == 'Terminé']),
                    len(tasks_df[(tasks_df['Status'] != 'Terminé') & (pd.to_datetime(tasks_df['Due Date']) < pd.Timestamp.now())]),
                    len(tasks_df[tasks_df['Priority'] == 'haute'])
                ]
            }
            
            stats_df = pd.DataFrame(stats_data)
            stats_df.to_excel(writer, sheet_name='Statistiques', index=False)
        
        return filename

    def export_personnel_to_excel(self, filename=None):
        """Exporte le personnel vers Excel"""
        if filename is None:
            filename = os.path.join(self.export_dir, f"personnel_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
        
        # Récupérer tout le personnel
        self.db.cursor.execute("SELECT id, name, role FROM persons ORDER BY name")
        persons = self.db.cursor.fetchall()
        
        # Créer un DataFrame
        persons_df = pd.DataFrame(persons, columns=[
            'ID', 'Name', 'Role'
        ])
        
        # Exporter vers Excel
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            persons_df.to_excel(writer, sheet_name='Personnel', index=False)
            
            # Ajouter des statistiques
            stats_data = {
                'Statistique': [
                    'Total du personnel',
                    'Techniciens',
                    'Superviseurs',
                    'Managers'
                ],
                'Valeur': [
                    len(persons_df),
                    len(persons_df[persons_df['Role'] == 'Technicien']),
                    len(persons_df[persons_df['Role'] == 'Superviseur']),
                    len(persons_df[persons_df['Role'] == 'Manager'])
                ]
            }
            
            stats_df = pd.DataFrame(stats_data)
            stats_df.to_excel(writer, sheet_name='Statistiques', index=False)
        
        return filename

    def export_equipment_to_excel(self, filename=None):
        """Exporte les équipements vers Excel"""
        if filename is None:
            filename = os.path.join(self.export_dir, f"equipements_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
        
        # Récupérer tous les équipements
        self.db.cursor.execute("SELECT id, name, model, serial_number, manufacturer, purchase_date, installation_date, location, status, last_maintenance_date, next_maintenance_date, notes FROM equipment ORDER BY name")
        equipment = self.db.cursor.fetchall()
        
        # Créer un DataFrame
        equipment_df = pd.DataFrame(equipment, columns=[
            'ID', 'Name', 'Model', 'Serial Number', 'Manufacturer', 'Purchase Date', 'Installation Date', 'Location', 'Status', 'Last Maintenance Date', 'Next Maintenance Date', 'Notes'
        ])
        
        # Exporter vers Excel
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            equipment_df.to_excel(writer, sheet_name='Équipements', index=False)
            
            # Ajouter des statistiques
            stats_data = {
                'Statistique': [
                    'Total des équipements',
                    'Équipements opérationnels',
                    'Équipements en maintenance',
                    'Équipements hors service'
                ],
                'Valeur': [
                    len(equipment_df),
                    len(equipment_df[equipment_df['Status'] == 'En service']),
                    len(equipment_df[equipment_df['Status'] == 'En maintenance']),
                    len(equipment_df[equipment_df['Status'] == 'Hors service'])
                ]
            }
            
            stats_df = pd.DataFrame(stats_data)
            stats_df.to_excel(writer, sheet_name='Statistiques', index=False)
        
        return filename

    def export_spare_parts_to_excel(self, filename=None):
        """Exporte les pièces de rechange vers Excel"""
        if filename is None:
            filename = os.path.join(self.export_dir, f"pieces_rechange_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
        
        # Récupérer toutes les pièces de rechange
        self.db.cursor.execute("SELECT id, code, name, quantity, location, part_type, min_threshold, unit_price FROM spare_parts ORDER BY name")
        spare_parts = self.db.cursor.fetchall()
        
        # Créer un DataFrame
        spare_parts_df = pd.DataFrame(spare_parts, columns=[
            'ID', 'Code', 'Name', 'Quantity', 'Location', 'Part Type', 'Min Threshold', 'Unit Price'
        ])
        
        # Exporter vers Excel
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            spare_parts_df.to_excel(writer, sheet_name='Pièces de rechange', index=False)
            
            # Ajouter des statistiques
            stats_data = {
                'Statistique': [
                    'Total des pièces',
                    'Pièces en stock',
                    'Pièces en rupture',
                    'Valeur totale du stock',
                    'Pièces à commander'
                ],
                'Valeur': [
                    len(spare_parts_df),
                    len(spare_parts_df[spare_parts_df['Quantity'] > 0]),
                    len(spare_parts_df[spare_parts_df['Quantity'] == 0]),
                    spare_parts_df['Unit Price'].sum(),
                    len(spare_parts_df[spare_parts_df['Quantity'] <= spare_parts_df['Min Threshold']])
                ]
            }
            
            stats_df = pd.DataFrame(stats_data)
            stats_df.to_excel(writer, sheet_name='Statistiques', index=False)
        
        return filename

    def export_attendance_to_excel(self, filename=None, start_date=None, end_date=None):
        """Exporte les présences vers Excel"""
        if filename is None:
            filename = os.path.join(self.export_dir, f"presences_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
        
        if start_date is None:
            start_date = datetime.now().replace(day=1).strftime('%Y-%m-%d')
        if end_date is None:
            end_date = datetime.now().strftime('%Y-%m-%d')
        
        # Récupérer les présences
        self.db.cursor.execute("""
            SELECT a.id, a.person_id, a.date, a.status, a.overtime_hours, a.notes, 
                   p.name as person_name, p.role as person_role
            FROM attendance a
            LEFT JOIN persons p ON a.person_id = p.id
            WHERE date(a.date) BETWEEN date(?) AND date(?)
            ORDER BY a.date DESC
        """, (start_date, end_date))
        attendance = self.db.cursor.fetchall()
        
        # Créer un DataFrame
        attendance_df = pd.DataFrame(attendance, columns=[
            'ID', 'Person ID', 'Date', 'Status', 'Overtime Hours', 'Notes', 'Person Name', 'Person Role'
        ])
        
        # Exporter vers Excel
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            attendance_df.to_excel(writer, sheet_name='Présences', index=False)
            
            # Ajouter des statistiques
            if not attendance_df.empty:
                stats_data = {
                    'Statistique': [
                        'Total des jours',
                        'Heures supplémentaires totales',
                        'Présences complètes',
                        'Absences',
                        'Retards'
                    ],
                    'Valeur': [
                        len(attendance_df),
                        attendance_df['Overtime Hours'].sum(),
                        len(attendance_df[attendance_df['Status'] == 'Présent']),
                        len(attendance_df[attendance_df['Status'] == 'Absent']),
                        len(attendance_df[attendance_df['Status'] == 'Retard'])
                    ]
                }
            else:
                stats_data = {
                    'Statistique': ['Aucune donnée de présence'],
                    'Valeur': [0]
                }
            
            stats_df = pd.DataFrame(stats_data)
            stats_df.to_excel(writer, sheet_name='Statistiques', index=False)

        return filename