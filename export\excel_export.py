"""
Module d'export Excel pour SOTRAMINE PHOSPHATE
"""

import os
from datetime import datetime
from PyQt5.QtWidgets import QMessageBox, QFileDialog

try:
    import openpyxl
    from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
    from openpyxl.utils import get_column_letter
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False
    print("⚠️ openpyxl non disponible - Export CSV utilisé")

class ExcelExporter:
    """Classe pour l'export des données vers Excel avec formatage professionnel"""

    def __init__(self, database):
        self.db = database
        self.export_dir = "export"

        # Créer le dossier d'export s'il n'existe pas
        if not os.path.exists(self.export_dir):
            os.makedirs(self.export_dir)

    def _create_styled_workbook(self, title="Export SOTRAMINE PHOSPHATE"):
        """Crée un classeur Excel avec style professionnel"""
        if not EXCEL_AVAILABLE:
            return None

        wb = openpyxl.Workbook()
        ws = wb.active

        # Styles pour les headers
        self.header_font = Font(name='Calibri', size=12, bold=True, color='FFFFFF')
        self.header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
        self.header_alignment = Alignment(horizontal='center', vertical='center')
        self.header_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        # Styles pour les données
        self.data_font = Font(name='Calibri', size=11)
        self.data_alignment = Alignment(horizontal='left', vertical='center')
        self.data_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        # Style pour les cellules alternées
        self.alt_fill = PatternFill(start_color='F2F2F2', end_color='F2F2F2', fill_type='solid')

        return wb

    def _style_worksheet(self, ws, headers, data_rows):
        """Applique le style professionnel à une feuille"""
        if not EXCEL_AVAILABLE:
            return

        # Styler les headers
        for col_num, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col_num, value=header)
            cell.font = self.header_font
            cell.fill = self.header_fill
            cell.alignment = self.header_alignment
            cell.border = self.header_border

        # Styler les données
        for row_num, row_data in enumerate(data_rows, 2):
            for col_num, value in enumerate(row_data, 1):
                cell = ws.cell(row=row_num, column=col_num, value=value)
                cell.font = self.data_font
                cell.alignment = self.data_alignment
                cell.border = self.data_border

                # Alternance de couleurs
                if row_num % 2 == 0:
                    cell.fill = self.alt_fill

        # Ajuster la largeur des colonnes
        for col_num in range(1, len(headers) + 1):
            column_letter = get_column_letter(col_num)
            ws.column_dimensions[column_letter].width = 15

        # Figer la première ligne
        ws.freeze_panes = 'A2'
    
    def export_tasks_to_excel(self, parent=None):
        """Exporte les tâches vers Excel"""
        try:
            # Récupérer les données
            tasks = self.db.get_all_tasks()
            
            if not tasks:
                if parent:
                    QMessageBox.information(parent, "Export", "Aucune tâche à exporter")
                return False
            
            # Générer le nom de fichier
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"taches_{timestamp}.csv"
            filepath = os.path.join(self.export_dir, filename)
            
            # Écrire le fichier CSV
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['ID', 'Titre', 'Description', 'Statut', 'Priorité', 'Assigné à', 
                             'Date création', 'Date échéance', 'Catégorie']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for task in tasks:
                    writer.writerow({
                        'ID': task[0],
                        'Titre': task[1],
                        'Description': task[2] or '',
                        'Statut': task[3],
                        'Priorité': task[4],
                        'Assigné à': task[5] or '',
                        'Date création': task[6],
                        'Date échéance': task[7] or '',
                        'Catégorie': task[8] or ''
                    })
            
            if parent:
                QMessageBox.information(parent, "Export Réussi", 
                                      f"Tâches exportées vers :\n{filepath}")
            
            return True
            
        except Exception as e:
            if parent:
                QMessageBox.critical(parent, "Erreur d'Export", 
                                   f"Erreur lors de l'export des tâches :\n{str(e)}")
            return False
    
    def export_personnel_to_excel(self, parent=None):
        """Exporte le personnel vers Excel"""
        try:
            # Récupérer les données
            persons = self.db.get_all_persons()
            
            if not persons:
                if parent:
                    QMessageBox.information(parent, "Export", "Aucune personne à exporter")
                return False
            
            # Générer le nom de fichier
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"personnel_{timestamp}.csv"
            filepath = os.path.join(self.export_dir, filename)
            
            # Écrire le fichier CSV
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['ID', 'Nom', 'Prénom', 'Poste', 'Email', 'Téléphone', 
                             'Date embauche', 'Statut', 'Compétences']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for person in persons:
                    writer.writerow({
                        'ID': person[0],
                        'Nom': person[1],
                        'Prénom': person[2],
                        'Poste': person[3] or '',
                        'Email': person[4] or '',
                        'Téléphone': person[5] or '',
                        'Date embauche': person[6] or '',
                        'Statut': person[7] or '',
                        'Compétences': person[8] or ''
                    })
            
            if parent:
                QMessageBox.information(parent, "Export Réussi", 
                                      f"Personnel exporté vers :\n{filepath}")
            
            return True
            
        except Exception as e:
            if parent:
                QMessageBox.critical(parent, "Erreur d'Export", 
                                   f"Erreur lors de l'export du personnel :\n{str(e)}")
            return False
    
    def export_equipment_to_excel(self, parent=None):
        """Exporte les équipements vers Excel avec formatage professionnel"""
        try:
            # Récupérer les données
            equipment = self.db.get_all_equipment()

            if not equipment:
                if parent:
                    QMessageBox.information(parent, "Export", "Aucun équipement à exporter")
                return False

            # Générer le nom de fichier
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"equipements_{timestamp}.xlsx"
            filepath = os.path.join(self.export_dir, filename)

            if EXCEL_AVAILABLE:
                # Export Excel avec formatage
                wb = self._create_styled_workbook("Export Équipements SOTRAMINE PHOSPHATE")
                ws = wb.active
                ws.title = "Équipements"

                # Headers
                headers = ['ID', 'Code', 'Nom', 'Modèle', 'Numéro série', 'Fabricant',
                          'Date achat', 'Date installation', 'Localisation', 'Statut',
                          'Dernière maintenance', 'Prochaine maintenance', 'Notes']

                # Données
                data_rows = []
                for eq in equipment:
                    data_rows.append([
                        eq[0],                    # ID
                        eq[1] or '',             # Code
                        eq[2],                   # Nom
                        eq[3] or '',             # Modèle
                        eq[4] or '',             # Numéro série
                        eq[5] or '',             # Fabricant
                        eq[6] or '',             # Date achat
                        eq[7] or '',             # Date installation
                        eq[8] or '',             # Localisation
                        eq[9],                   # Statut
                        eq[10] or '',            # Dernière maintenance
                        eq[11] or '',            # Prochaine maintenance
                        eq[12] or ''             # Notes
                    ])

                # Appliquer le style
                self._style_worksheet(ws, headers, data_rows)

                # Ajuster les largeurs spécifiques
                ws.column_dimensions['A'].width = 8   # ID
                ws.column_dimensions['B'].width = 15  # Code
                ws.column_dimensions['C'].width = 25  # Nom
                ws.column_dimensions['D'].width = 20  # Modèle
                ws.column_dimensions['E'].width = 15  # Numéro série
                ws.column_dimensions['F'].width = 18  # Fabricant
                ws.column_dimensions['G'].width = 12  # Date achat
                ws.column_dimensions['H'].width = 12  # Date installation
                ws.column_dimensions['I'].width = 20  # Localisation
                ws.column_dimensions['J'].width = 12  # Statut
                ws.column_dimensions['K'].width = 15  # Dernière maintenance
                ws.column_dimensions['L'].width = 15  # Prochaine maintenance
                ws.column_dimensions['M'].width = 30  # Notes

                # Ajouter une feuille de résumé
                summary_ws = wb.create_sheet("Résumé")
                summary_ws.cell(row=1, column=1, value="Résumé des Équipements").font = Font(size=16, bold=True)
                summary_ws.cell(row=3, column=1, value="Total équipements :").font = Font(bold=True)
                summary_ws.cell(row=3, column=2, value=len(equipment))

                # Statistiques par statut
                statuts = {}
                for eq in equipment:
                    statut = eq[9] or 'Non défini'
                    statuts[statut] = statuts.get(statut, 0) + 1

                row = 5
                summary_ws.cell(row=row, column=1, value="Répartition par statut :").font = Font(bold=True)
                row += 1
                for statut, count in statuts.items():
                    summary_ws.cell(row=row, column=1, value=f"  • {statut} :")
                    summary_ws.cell(row=row, column=2, value=count)
                    row += 1

                # Date d'export
                summary_ws.cell(row=row + 2, column=1, value="Date d'export :").font = Font(bold=True)
                summary_ws.cell(row=row + 2, column=2, value=datetime.now().strftime("%d/%m/%Y %H:%M"))

                # Sauvegarder
                wb.save(filepath)

            else:
                # Fallback CSV si Excel non disponible
                filename = f"equipements_{timestamp}.csv"
                filepath = os.path.join(self.export_dir, filename)

                import csv
                with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                    fieldnames = ['ID', 'Code', 'Nom', 'Modèle', 'Numéro série', 'Fabricant',
                                 'Date achat', 'Date installation', 'Localisation', 'Statut',
                                 'Dernière maintenance', 'Prochaine maintenance', 'Notes']
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                    writer.writeheader()
                    for eq in equipment:
                        writer.writerow({
                            'ID': eq[0],
                            'Code': eq[1] or '',
                            'Nom': eq[2],
                            'Modèle': eq[3] or '',
                            'Numéro série': eq[4] or '',
                            'Fabricant': eq[5] or '',
                            'Date achat': eq[6] or '',
                            'Date installation': eq[7] or '',
                            'Localisation': eq[8] or '',
                            'Statut': eq[9],
                            'Dernière maintenance': eq[10] or '',
                            'Prochaine maintenance': eq[11] or '',
                            'Notes': eq[12] or ''
                        })

            if parent:
                file_type = "Excel (.xlsx)" if EXCEL_AVAILABLE else "CSV"
                QMessageBox.information(parent, "Export Réussi",
                                      f"Équipements exportés vers {file_type} :\n{filepath}")

            return True

        except Exception as e:
            if parent:
                QMessageBox.critical(parent, "Erreur d'Export",
                                   f"Erreur lors de l'export des équipements :\n{str(e)}")
            return False
    
    def export_spare_parts_to_excel(self, parent=None):
        """Exporte les pièces de rechange vers Excel"""
        try:
            # Récupérer les données
            spare_parts = self.db.get_all_spare_parts()
            
            if not spare_parts:
                if parent:
                    QMessageBox.information(parent, "Export", "Aucune pièce à exporter")
                return False
            
            # Générer le nom de fichier
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"pieces_rechange_{timestamp}.csv"
            filepath = os.path.join(self.export_dir, filename)
            
            # Écrire le fichier CSV
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['ID', 'Code', 'Nom', 'Quantité', 'Localisation', 'Type',
                             'Seuil alerte', 'Prix unitaire', 'Fournisseur', 'Notes']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for part in spare_parts:
                    writer.writerow({
                        'ID': part[0],
                        'Code': part[1],
                        'Nom': part[2],
                        'Quantité': part[3],
                        'Localisation': part[4] or '',
                        'Type': part[5] or '',
                        'Seuil alerte': part[6] or '',
                        'Prix unitaire': part[7] or '',
                        'Fournisseur': part[8] or '',
                        'Notes': part[9] or ''
                    })
            
            if parent:
                QMessageBox.information(parent, "Export Réussi", 
                                      f"Pièces de rechange exportées vers :\n{filepath}")
            
            return True
            
        except Exception as e:
            if parent:
                QMessageBox.critical(parent, "Erreur d'Export", 
                                   f"Erreur lors de l'export des pièces :\n{str(e)}")
            return False
    
    def export_attendance_to_excel(self, parent=None):
        """Exporte les données de pointage vers Excel"""
        try:
            # Récupérer les données de pointage
            attendance_data = self.db.get_all_attendance()
            
            if not attendance_data:
                if parent:
                    QMessageBox.information(parent, "Export", "Aucune donnée de pointage à exporter")
                return False
            
            # Générer le nom de fichier
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"pointage_{timestamp}.csv"
            filepath = os.path.join(self.export_dir, filename)
            
            # Écrire le fichier CSV
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['ID', 'Personne', 'Date', 'Statut', 'Heures travaillées', 'Notes']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for record in attendance_data:
                    writer.writerow({
                        'ID': record[0],
                        'Personne': record[1],
                        'Date': record[2],
                        'Statut': record[3],
                        'Heures travaillées': record[4] or '',
                        'Notes': record[5] or ''
                    })
            
            if parent:
                QMessageBox.information(parent, "Export Réussi", 
                                      f"Données de pointage exportées vers :\n{filepath}")
            
            return True
            
        except Exception as e:
            if parent:
                QMessageBox.critical(parent, "Erreur d'Export", 
                                   f"Erreur lors de l'export du pointage :\n{str(e)}")
            return False
    
    def export_all_data(self, parent=None):
        """Exporte toutes les données"""
        try:
            exports = [
                ("Tâches", self.export_tasks_to_excel),
                ("Personnel", self.export_personnel_to_excel),
                ("Équipements", self.export_equipment_to_excel),
                ("Pièces de rechange", self.export_spare_parts_to_excel),
                ("Pointage", self.export_attendance_to_excel)
            ]
            
            successful_exports = []
            failed_exports = []
            
            for export_name, export_func in exports:
                try:
                    if export_func(None):  # Pas de parent pour éviter les popups multiples
                        successful_exports.append(export_name)
                    else:
                        failed_exports.append(export_name)
                except Exception as e:
                    failed_exports.append(f"{export_name} ({str(e)})")
            
            # Message de résumé
            message = f"Export complet terminé :\n\n"
            message += f"✅ Réussis ({len(successful_exports)}) :\n"
            for export in successful_exports:
                message += f"  • {export}\n"
            
            if failed_exports:
                message += f"\n❌ Échecs ({len(failed_exports)}) :\n"
                for export in failed_exports:
                    message += f"  • {export}\n"
            
            message += f"\nFichiers sauvegardés dans : {self.export_dir}/"
            
            if parent:
                QMessageBox.information(parent, "Export Complet", message)
            
            return len(successful_exports) > 0
            
        except Exception as e:
            if parent:
                QMessageBox.critical(parent, "Erreur d'Export", 
                                   f"Erreur lors de l'export complet :\n{str(e)}")
            return False
    
    def get_export_directory(self):
        """Retourne le répertoire d'export"""
        return os.path.abspath(self.export_dir)
    
    def open_export_directory(self):
        """Ouvre le répertoire d'export dans l'explorateur"""
        try:
            import subprocess
            import platform
            
            export_path = self.get_export_directory()
            
            if platform.system() == "Windows":
                subprocess.run(["explorer", export_path])
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", export_path])
            else:  # Linux
                subprocess.run(["xdg-open", export_path])
            
            return True
            
        except Exception as e:
            print(f"Erreur ouverture répertoire : {str(e)}")
            return False
