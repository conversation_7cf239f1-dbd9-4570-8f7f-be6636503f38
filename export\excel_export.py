"""
Module d'export Excel pour SOTRAMINE PHOSPHATE
"""

import os
from datetime import datetime

class ExcelExporter:
    """Classe pour l'export Excel"""
    
    def __init__(self, db):
        self.db = db
    
    def export_equipment_list(self, file_path):
        """Exporte la liste des équipements vers Excel"""
        try:
            # Import optionnel d'openpyxl
            try:
                from openpyxl import Workbook
                from openpyxl.styles import Font, PatternFill
            except ImportError:
                print("Module openpyxl non disponible pour l'export Excel")
                return False
            
            # Créer un nouveau classeur
            wb = Workbook()
            ws = wb.active
            ws.title = "Équipements"
            
            # En-têtes
            headers = [
                "ID", "Code", "Nom", "Modèle", "N° Série", "Fabricant",
                "Date d'achat", "Date d'installation", "Localisation", "Statut", "Notes"
            ]
            
            # Ajouter les en-têtes avec style
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
            
            # Récupérer les équipements
            equipment_list = self.db.get_all_equipment()
            
            # Ajouter les données
            for row, equipment in enumerate(equipment_list, 2):
                for col, value in enumerate(equipment[:11], 1):  # Prendre les 11 premières colonnes
                    ws.cell(row=row, column=col, value=value)
            
            # Ajuster la largeur des colonnes
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width
            
            # Sauvegarder
            wb.save(file_path)
            return True
            
        except Exception as e:
            print(f"Erreur lors de l'export Excel : {str(e)}")
            return False
    
    def export_tasks_list(self, file_path):
        """Exporte la liste des tâches vers Excel"""
        try:
            # Import optionnel d'openpyxl
            try:
                from openpyxl import Workbook
                from openpyxl.styles import Font, PatternFill
            except ImportError:
                print("Module openpyxl non disponible pour l'export Excel")
                return False
            
            # Créer un nouveau classeur
            wb = Workbook()
            ws = wb.active
            ws.title = "Tâches"
            
            # En-têtes
            headers = [
                "ID", "Titre", "Description", "Statut", "Priorité",
                "Date de création", "Date d'échéance", "Assigné à"
            ]
            
            # Ajouter les en-têtes avec style
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
            
            # Récupérer les tâches
            tasks_list = self.db.get_all_tasks()
            
            # Ajouter les données
            for row, task in enumerate(tasks_list, 2):
                for col, value in enumerate(task[:8], 1):  # Prendre les 8 premières colonnes
                    ws.cell(row=row, column=col, value=value)
            
            # Ajuster la largeur des colonnes
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width
            
            # Sauvegarder
            wb.save(file_path)
            return True
            
        except Exception as e:
            print(f"Erreur lors de l'export Excel : {str(e)}")
            return False
    
    def export_spare_parts_list(self, file_path):
        """Exporte la liste des pièces de rechange vers Excel"""
        try:
            # Import optionnel d'openpyxl
            try:
                from openpyxl import Workbook
                from openpyxl.styles import Font, PatternFill
            except ImportError:
                print("Module openpyxl non disponible pour l'export Excel")
                return False
            
            # Créer un nouveau classeur
            wb = Workbook()
            ws = wb.active
            ws.title = "Pièces de Rechange"
            
            # En-têtes
            headers = [
                "ID", "Code", "Nom", "Quantité", "Localisation",
                "Type", "Seuil d'alerte", "Prix unitaire"
            ]
            
            # Ajouter les en-têtes avec style
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
            
            # Récupérer les pièces de rechange
            try:
                parts_list = self.db.get_all_spare_parts()
            except:
                parts_list = []
            
            # Ajouter les données
            for row, part in enumerate(parts_list, 2):
                for col, value in enumerate(part[:8], 1):  # Prendre les 8 premières colonnes
                    ws.cell(row=row, column=col, value=value)
            
            # Ajuster la largeur des colonnes
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width
            
            # Sauvegarder
            wb.save(file_path)
            return True
            
        except Exception as e:
            print(f"Erreur lors de l'export Excel : {str(e)}")
            return False
    
    def export_personnel_list(self, file_path):
        """Exporte la liste du personnel vers Excel"""
        try:
            # Import optionnel d'openpyxl
            try:
                from openpyxl import Workbook
                from openpyxl.styles import Font, PatternFill
            except ImportError:
                print("Module openpyxl non disponible pour l'export Excel")
                return False
            
            # Créer un nouveau classeur
            wb = Workbook()
            ws = wb.active
            ws.title = "Personnel"
            
            # En-têtes
            headers = [
                "ID", "Nom", "Prénom", "Poste", "Département",
                "Email", "Téléphone", "Date d'embauche"
            ]
            
            # Ajouter les en-têtes avec style
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
            
            # Récupérer le personnel
            try:
                personnel_list = self.db.get_all_persons()
            except:
                personnel_list = []
            
            # Ajouter les données
            for row, person in enumerate(personnel_list, 2):
                for col, value in enumerate(person[:8], 1):  # Prendre les 8 premières colonnes
                    ws.cell(row=row, column=col, value=value)
            
            # Ajuster la largeur des colonnes
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width
            
            # Sauvegarder
            wb.save(file_path)
            return True
            
        except Exception as e:
            print(f"Erreur lors de l'export Excel : {str(e)}")
            return False
