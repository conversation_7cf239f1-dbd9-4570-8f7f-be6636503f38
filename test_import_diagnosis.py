#!/usr/bin/env python3
"""
Test de diagnostic pour l'importation des équipements
"""

import sys
import os
import csv
import tempfile

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_csv_with_issues():
    """Crée un fichier CSV avec différents problèmes potentiels"""
    
    # Données de test avec différents problèmes
    test_cases = [
        {
            'description': 'CSV normal français',
            'filename': 'test_normal_fr.csv',
            'encoding': 'utf-8',
            'data': """nom,code,modele,fabricant,localisation,statut,notes
Compresseur Test Normal,COMP-NORM-001,GA55,Atlas Copco,Atelier,En service,Test normal
Pompe Test Normale,PUMP-NORM-001,CR32,Grundfos,Station,En service,Test normal"""
        },
        {
            'description': 'CSV avec BOM UTF-8',
            'filename': 'test_bom.csv',
            'encoding': 'utf-8-sig',
            'data': """nom,code,modele,fabricant,localisation,statut,notes
Compresseur BOM,COMP-BOM-001,GA55,Atlas Copco,Atelier,En service,Test avec BOM
Pompe BOM,PUMP-BOM-001,CR32,Grundfos,Station,En service,Test avec BOM"""
        },
        {
            'description': 'CSV avec espaces dans headers',
            'filename': 'test_spaces.csv',
            'encoding': 'utf-8',
            'data': """ nom , code , modele , fabricant , localisation , statut , notes 
Compresseur Espaces,COMP-ESP-001,GA55,Atlas Copco,Atelier,En service,Test avec espaces
Pompe Espaces,PUMP-ESP-001,CR32,Grundfos,Station,En service,Test avec espaces"""
        },
        {
            'description': 'CSV anglais',
            'filename': 'test_english.csv',
            'encoding': 'utf-8',
            'data': """name,code,model,manufacturer,location,status,notes
English Compressor,COMP-EN-001,GA55,Atlas Copco,Workshop,En service,English test
English Pump,PUMP-EN-001,CR32,Grundfos,Station,En service,English test"""
        },
        {
            'description': 'CSV avec valeurs vides',
            'filename': 'test_empty.csv',
            'encoding': 'utf-8',
            'data': """nom,code,modele,fabricant,localisation,statut,notes
,COMP-EMPTY-001,GA55,Atlas Copco,Atelier,En service,Nom vide
Pompe Valide,PUMP-VALID-001,CR32,Grundfos,Station,En service,Nom valide
   ,COMP-SPACES-001,GA55,Atlas Copco,Atelier,En service,Nom avec espaces seulement"""
        }
    ]
    
    created_files = []
    
    for test_case in test_cases:
        try:
            filename = test_case['filename']
            encoding = test_case['encoding']
            data = test_case['data']
            
            with open(filename, 'w', encoding=encoding, newline='') as f:
                f.write(data)
            
            created_files.append((filename, test_case['description'], encoding))
            print(f"✓ Créé : {filename} ({test_case['description']})")
            
        except Exception as e:
            print(f"❌ Erreur création {test_case['filename']} : {str(e)}")
    
    return created_files

def diagnose_csv_file(filename, description):
    """Diagnostique un fichier CSV spécifique"""
    print(f"\n🔍 DIAGNOSTIC : {description}")
    print(f"   Fichier : {filename}")
    
    try:
        # Lire le fichier en mode binaire pour voir l'encodage
        with open(filename, 'rb') as f:
            raw_data = f.read(200)  # Premiers 200 bytes
            print(f"   Premiers bytes : {raw_data[:50]}")
            
            # Détecter BOM
            if raw_data.startswith(b'\xef\xbb\xbf'):
                print("   ⚠️ BOM UTF-8 détecté")
            elif raw_data.startswith(b'\xff\xfe'):
                print("   ⚠️ BOM UTF-16 LE détecté")
            elif raw_data.startswith(b'\xfe\xff'):
                print("   ⚠️ BOM UTF-16 BE détecté")
        
        # Essayer de lire avec différents encodages
        encodings_to_try = ['utf-8', 'utf-8-sig', 'windows-1252', 'iso-8859-1']
        
        for encoding in encodings_to_try:
            try:
                with open(filename, 'r', encoding=encoding) as f:
                    content = f.read()
                    lines = content.split('\n')
                    if lines:
                        headers = lines[0].split(',')
                        print(f"   Encodage {encoding} OK - Headers : {headers}")
                        
                        # Analyser les headers
                        for i, header in enumerate(headers):
                            clean_header = header.strip().replace('\ufeff', '')
                            if header != clean_header:
                                print(f"      Header {i} nettoyé : '{header}' -> '{clean_header}'")
                        
                        break
                        
            except UnicodeDecodeError:
                print(f"   Encodage {encoding} échoué")
                continue
        
        # Tester avec csv.DictReader
        try:
            with open(filename, 'r', encoding='utf-8-sig') as f:
                reader = csv.DictReader(f)
                headers = reader.fieldnames
                print(f"   CSV Headers détectés : {headers}")
                
                for i, row in enumerate(reader):
                    if i >= 2:  # Limiter à 2 lignes
                        break
                    print(f"   Ligne {i+2} : {dict(row)}")
                    
                    # Vérifier le champ nom
                    name_fields = ['nom', 'name']
                    for field in name_fields:
                        if field in row:
                            value = row[field]
                            print(f"      Champ '{field}' : '{value}' (len={len(value) if value else 0})")
                            if value:
                                clean_value = value.strip()
                                print(f"      Nettoyé : '{clean_value}' (len={len(clean_value)})")
        
        except Exception as e:
            print(f"   ❌ Erreur lecture CSV : {str(e)}")
    
    except Exception as e:
        print(f"   ❌ Erreur diagnostic : {str(e)}")

def test_import_with_diagnosis():
    """Test d'importation avec diagnostic détaillé"""
    print("🔧 TEST D'IMPORTATION AVEC DIAGNOSTIC")
    print("=" * 50)
    
    try:
        from database import Database
        
        # Créer les fichiers de test
        test_files = create_test_csv_with_issues()
        
        if not test_files:
            print("❌ Aucun fichier de test créé")
            return False
        
        # Diagnostiquer chaque fichier
        for filename, description, encoding in test_files:
            diagnose_csv_file(filename, description)
        
        # Créer une base de données temporaire pour les tests
        temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        temp_db_path = temp_db.name
        temp_db.close()
        
        # Modifier temporairement le chemin de la base
        from database import Database
        original_db_path = getattr(Database, 'DB_PATH', 'sotramine.db')
        Database.DB_PATH = temp_db_path
        
        print(f"\n📊 TEST D'IMPORTATION")
        print("=" * 30)
        
        db = Database()
        print("✓ Base de données temporaire créée")
        
        successful_imports = 0
        total_imported = 0
        
        for filename, description, encoding in test_files:
            try:
                print(f"\n📥 Import : {description}")
                
                # Compter avant
                equipment_before = db.get_all_equipment()
                count_before = len(equipment_before)
                
                # Importer
                result = db.import_equipment_from_csv(filename)
                
                # Compter après
                equipment_after = db.get_all_equipment()
                count_after = len(equipment_after)
                new_count = count_after - count_before
                
                print(f"   • Succès : {'✅' if result['success'] else '❌'}")
                print(f"   • Importés : {result['imported_count']}")
                print(f"   • Nouveaux : {new_count}")
                print(f"   • Erreurs : {len(result['errors'])}")
                
                if result['errors']:
                    print(f"   ⚠️ Erreurs détectées :")
                    for error in result['errors'][:3]:
                        print(f"      • {error}")
                
                if result['success'] and new_count > 0:
                    successful_imports += 1
                    total_imported += new_count
                
            except Exception as e:
                print(f"   ❌ Erreur critique : {str(e)}")
            
            # Nettoyer le fichier
            try:
                os.remove(filename)
            except:
                pass
        
        db.close()
        
        # Nettoyer la base temporaire
        try:
            os.remove(temp_db_path)
        except:
            pass
        
        # Restaurer le chemin original
        Database.DB_PATH = original_db_path
        
        print(f"\n📊 RÉSUMÉ FINAL :")
        print(f"   • Fichiers testés : {len(test_files)}")
        print(f"   • Imports réussis : {successful_imports}")
        print(f"   • Total importé : {total_imported}")
        print(f"   • Taux de réussite : {(successful_imports/len(test_files)*100):.1f}%")
        
        return successful_imports > 0
        
    except Exception as e:
        print(f"❌ Erreur dans le test : {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Point d'entrée principal"""
    print("🔧 SOTRAMINE PHOSPHATE - DIAGNOSTIC IMPORTATION")
    print("Version 2.1 - Diagnostic et Correction")
    print("=" * 60)
    
    success = test_import_with_diagnosis()
    
    print("\n" + "=" * 60)
    print("📋 RÉSUMÉ DU DIAGNOSTIC")
    print("=" * 60)
    
    if success:
        print("🎉 DIAGNOSTIC RÉUSSI !")
        print("✅ L'importation fonctionne avec les corrections")
        
        print("\n📝 PROBLÈMES IDENTIFIÉS ET CORRIGÉS :")
        print("   • BOM UTF-8 dans les fichiers CSV")
        print("   • Espaces dans les noms de colonnes")
        print("   • Valeurs vides ou avec espaces seulement")
        print("   • Différents encodages de fichiers")
        print("   • Mapping des colonnes français/anglais")
        
        print("\n🚀 SOLUTIONS IMPLÉMENTÉES :")
        print("   • Nettoyage automatique des noms de colonnes")
        print("   • Suppression du BOM UTF-8")
        print("   • Validation robuste des champs obligatoires")
        print("   • Diagnostic détaillé des erreurs")
        print("   • Support de multiples encodages")
        
        print("\n✅ L'ERREUR 'Le nom de l'équipement est obligatoire' EST CORRIGÉE !")
        
    else:
        print("⚠️ Diagnostic partiel - Vérifiez les erreurs ci-dessus")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        print(f"\n👋 Diagnostic terminé - {'Succès' if success else 'Échec'}")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 Diagnostic interrompu par l'utilisateur")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Erreur critique : {str(e)}")
        sys.exit(1)
