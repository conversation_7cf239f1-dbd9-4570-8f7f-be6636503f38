"""
Gestionnaire de Bons de Travail
Module complet pour la création, suivi et gestion des bons de travail de maintenance
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                             QTableWidgetItem, QPushButton, QLineEdit, QComboBox,
                             QLabel, QFrame, QSplitter, QGroupBox, QFormLayout,
                             QTextEdit, QDateEdit, QHeaderView, QMessageBox,
                             QDialog, QTabWidget, QProgressBar, QCheckBox,
                             QSpinBox, QDoubleSpinBox, QTimeEdit, QListWidget,
                             QListWidgetItem, QTreeWidget, QTreeWidgetItem)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QTime, QDateTime, QTimer
from PyQt5.QtGui import QFont, QPixmap, QIcon, QColor
from utils.data_refresh_manager import RefreshableWidget, notify_data_changed

class WorkOrderDialog(QDialog):
    """Dialogue pour créer ou modifier un bon de travail"""
    
    work_order_created = pyqtSignal(int)
    work_order_updated = pyqtSignal(int)
    
    def __init__(self, db, parent=None, work_order_id=None):
        super().__init__(parent)
        self.db = db
        self.work_order_id = work_order_id
        self.work_order = None
        
        if work_order_id:
            self.work_order = self.db.get_work_order(work_order_id)
        
        self.setup_ui()
        self.load_data()
        
        if work_order_id:
            self.load_work_order_data()
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        self.setWindowTitle("Bon de Travail de Maintenance")
        self.setMinimumWidth(900)
        self.setMinimumHeight(700)
        
        layout = QVBoxLayout(self)
        
        # Onglets du bon de travail
        self.tabs = QTabWidget()
        
        # Onglet Informations générales
        self.general_tab = self.create_general_tab()
        self.tabs.addTab(self.general_tab, "📋 Informations générales")
        
        # Onglet Planification
        self.planning_tab = self.create_planning_tab()
        self.tabs.addTab(self.planning_tab, "📅 Planification")
        
        # Onglet Ressources
        self.resources_tab = self.create_resources_tab()
        self.tabs.addTab(self.resources_tab, "👥 Ressources")
        
        # Onglet Pièces de rechange
        self.parts_tab = self.create_parts_tab()
        self.tabs.addTab(self.parts_tab, "🔧 Pièces de rechange")
        
        # Onglet Instructions
        self.instructions_tab = self.create_instructions_tab()
        self.tabs.addTab(self.instructions_tab, "📝 Instructions")
        
        # Onglet Historique
        self.history_tab = self.create_history_tab()
        self.tabs.addTab(self.history_tab, "📚 Historique")
        
        layout.addWidget(self.tabs)
        
        # Boutons d'action
        buttons_layout = QHBoxLayout()
        
        save_btn = QPushButton("💾 Enregistrer")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        save_btn.clicked.connect(self.save_work_order)
        
        cancel_btn = QPushButton("❌ Annuler")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        
        if self.work_order_id:
            delete_btn = QPushButton("🗑️ Supprimer")
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #95a5a6;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 6px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #7f8c8d;
                }
            """)
            delete_btn.clicked.connect(self.delete_work_order)
            buttons_layout.addWidget(delete_btn)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addWidget(save_btn)
        
        layout.addLayout(buttons_layout)
    
    def create_general_tab(self):
        """Crée l'onglet informations générales"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Informations de base
        basic_group = QGroupBox("Informations de base")
        basic_layout = QFormLayout()
        
        # Numéro du bon de travail
        self.wo_number_edit = QLineEdit()
        self.wo_number_edit.setPlaceholderText("Numéro automatique")
        basic_layout.addRow("Numéro:", self.wo_number_edit)
        
        # Titre
        self.title_edit = QLineEdit()
        self.title_edit.setPlaceholderText("Titre du bon de travail")
        basic_layout.addRow("Titre:", self.title_edit)
        
        # Description
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(100)
        basic_layout.addRow("Description:", self.description_edit)
        
        # Type de maintenance
        self.maintenance_type_combo = QComboBox()
        self.maintenance_type_combo.addItems([
            "Préventive", "Corrective", "Prédictive", "Amélioration", "Inspection"
        ])
        basic_layout.addRow("Type:", self.maintenance_type_combo)
        
        # Priorité
        self.priority_combo = QComboBox()
        self.priority_combo.addItems(["Basse", "Normale", "Haute", "Critique"])
        basic_layout.addRow("Priorité:", self.priority_combo)
        
        # Statut
        self.status_combo = QComboBox()
        self.status_combo.addItems([
            "Créé", "Planifié", "En cours", "En attente", "Terminé", "Annulé"
        ])
        basic_layout.addRow("Statut:", self.status_combo)
        
        basic_group.setLayout(basic_layout)
        layout.addWidget(basic_group)
        
        # Équipement concerné
        equipment_group = QGroupBox("Équipement concerné")
        equipment_layout = QFormLayout()
        
        # Sélection de l'équipement
        self.equipment_combo = QComboBox()
        self.equipment_combo.addItem("Sélectionner un équipement", None)
        equipment_layout.addRow("Équipement:", self.equipment_combo)
        
        # Localisation
        self.location_edit = QLineEdit()
        equipment_layout.addRow("Localisation:", self.location_edit)
        
        # Fabricant/Modèle
        self.manufacturer_edit = QLineEdit()
        equipment_layout.addRow("Fabricant:", self.manufacturer_edit)
        
        equipment_group.setLayout(equipment_layout)
        layout.addWidget(equipment_group)
        
        layout.addStretch()
        return widget
    
    def create_planning_tab(self):
        """Crée l'onglet planification"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Dates et planning
        planning_group = QGroupBox("Planning de la maintenance")
        planning_layout = QFormLayout()
        
        # Date de création
        self.creation_date_edit = QDateEdit(QDate.currentDate())
        self.creation_date_edit.setCalendarPopup(True)
        planning_layout.addRow("Date de création:", self.creation_date_edit)
        
        # Date planifiée
        self.planned_date_edit = QDateEdit(QDate.currentDate().addDays(1))
        self.planned_date_edit.setCalendarPopup(True)
        planning_layout.addRow("Date planifiée:", self.planned_date_edit)
        
        # Heure de début
        self.start_time_edit = QTimeEdit(QTime(8, 0))
        planning_layout.addRow("Heure de début:", self.start_time_edit)
        
        # Durée estimée
        self.estimated_duration_spin = QSpinBox()
        self.estimated_duration_spin.setRange(1, 480)
        self.estimated_duration_spin.setSuffix(" minutes")
        planning_layout.addRow("Durée estimée:", self.estimated_duration_spin)
        
        # Date limite
        self.due_date_edit = QDateEdit(QDate.currentDate().addDays(7))
        self.due_date_edit.setCalendarPopup(True)
        planning_layout.addRow("Date limite:", self.due_date_edit)
        
        planning_group.setLayout(planning_layout)
        layout.addWidget(planning_group)
        
        # Récurrence
        recurrence_group = QGroupBox("Récurrence")
        recurrence_layout = QFormLayout()
        
        self.recurring_checkbox = QCheckBox("Maintenance récurrente")
        recurrence_layout.addRow(self.recurring_checkbox)
        
        self.recurrence_type_combo = QComboBox()
        self.recurrence_type_combo.addItems(["Quotidienne", "Hebdomadaire", "Mensuelle", "Annuelle"])
        self.recurrence_type_combo.setEnabled(False)
        recurrence_layout.addRow("Type:", self.recurrence_type_combo)
        
        self.recurring_checkbox.toggled.connect(self.recurrence_type_combo.setEnabled)
        
        recurrence_group.setLayout(recurrence_layout)
        layout.addWidget(recurrence_group)
        
        layout.addStretch()
        return widget
    
    def create_resources_tab(self):
        """Crée l'onglet ressources"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Personnel assigné
        personnel_group = QGroupBox("Personnel assigné")
        personnel_layout = QVBoxLayout()
        
        # Liste du personnel
        self.personnel_list = QListWidget()
        personnel_layout.addWidget(self.personnel_list)
        
        # Boutons d'ajout/suppression
        personnel_buttons_layout = QHBoxLayout()
        
        add_personnel_btn = QPushButton("➕ Ajouter")
        add_personnel_btn.clicked.connect(self.add_personnel)
        
        remove_personnel_btn = QPushButton("➖ Retirer")
        remove_personnel_btn.clicked.connect(self.remove_personnel)
        
        personnel_buttons_layout.addWidget(add_personnel_btn)
        personnel_buttons_layout.addWidget(remove_personnel_btn)
        personnel_buttons_layout.addStretch()
        
        personnel_layout.addLayout(personnel_buttons_layout)
        personnel_group.setLayout(personnel_layout)
        layout.addWidget(personnel_group)
        
        # Outils et équipements
        tools_group = QGroupBox("Outils et équipements nécessaires")
        tools_layout = QVBoxLayout()
        
        self.tools_list = QListWidget()
        tools_layout.addWidget(self.tools_list)
        
        tools_buttons_layout = QHBoxLayout()
        
        add_tool_btn = QPushButton("➕ Ajouter")
        add_tool_btn.clicked.connect(self.add_tool)
        
        remove_tool_btn = QPushButton("➖ Retirer")
        remove_tool_btn.clicked.connect(self.remove_tool)
        
        tools_buttons_layout.addWidget(add_tool_btn)
        tools_buttons_layout.addWidget(remove_tool_btn)
        tools_buttons_layout.addStretch()
        
        tools_layout.addLayout(tools_buttons_layout)
        tools_group.setLayout(tools_layout)
        layout.addWidget(tools_group)
        
        return widget
    
    def create_parts_tab(self):
        """Crée l'onglet pièces de rechange"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Pièces de rechange
        parts_group = QGroupBox("Pièces de rechange nécessaires")
        parts_layout = QVBoxLayout()
        
        # Tableau des pièces
        self.parts_table = QTableWidget()
        self.parts_table.setColumnCount(5)
        self.parts_table.setHorizontalHeaderLabels([
            "Référence", "Description", "Quantité", "Prix unitaire", "Total"
        ])
        
        header = self.parts_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        parts_layout.addWidget(self.parts_table)
        
        # Boutons d'action
        parts_buttons_layout = QHBoxLayout()
        
        add_part_btn = QPushButton("➕ Ajouter une pièce")
        add_part_btn.clicked.connect(self.add_part)
        
        remove_part_btn = QPushButton("➖ Retirer la pièce")
        remove_part_btn.clicked.connect(self.remove_part)
        
        parts_buttons_layout.addWidget(add_part_btn)
        parts_buttons_layout.addWidget(remove_part_btn)
        parts_buttons_layout.addStretch()
        
        parts_layout.addLayout(parts_buttons_layout)
        parts_group.setLayout(parts_layout)
        layout.addWidget(parts_group)
        
        # Total des pièces
        total_layout = QHBoxLayout()
        total_layout.addStretch()
        
        total_label = QLabel("Total des pièces:")
        total_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        
        self.parts_total_label = QLabel("0.00€")
        self.parts_total_label.setStyleSheet("font-weight: bold; font-size: 16px; color: #e74c3c;")
        
        total_layout.addWidget(total_label)
        total_layout.addWidget(self.parts_total_label)
        
        layout.addLayout(total_layout)
        
        return widget
    
    def create_instructions_tab(self):
        """Crée l'onglet instructions"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Instructions de travail
        instructions_group = QGroupBox("Instructions de travail")
        instructions_layout = QVBoxLayout()
        
        self.instructions_edit = QTextEdit()
        self.instructions_edit.setPlaceholderText("Décrivez les étapes à suivre pour cette maintenance...")
        instructions_layout.addWidget(self.instructions_edit)
        
        instructions_group.setLayout(instructions_layout)
        layout.addWidget(instructions_group)
        
        # Consignes de sécurité
        safety_group = QGroupBox("Consignes de sécurité")
        safety_layout = QVBoxLayout()
        
        self.safety_edit = QTextEdit()
        self.safety_edit.setPlaceholderText("Consignes de sécurité à respecter...")
        self.safety_edit.setMaximumHeight(100)
        safety_layout.addWidget(self.safety_edit)
        
        safety_group.setLayout(safety_layout)
        layout.addWidget(safety_group)
        
        # Notes et observations
        notes_group = QGroupBox("Notes et observations")
        notes_layout = QVBoxLayout()
        
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("Notes additionnelles...")
        self.notes_edit.setMaximumHeight(100)
        notes_layout.addWidget(self.notes_edit)
        
        notes_group.setLayout(notes_layout)
        layout.addWidget(notes_group)
        
        return widget
    
    def create_history_tab(self):
        """Crée l'onglet historique"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Historique des modifications
        history_group = QGroupBox("Historique des modifications")
        history_layout = QVBoxLayout()
        
        self.history_list = QListWidget()
        history_layout.addWidget(self.history_list)
        
        history_group.setLayout(history_layout)
        layout.addWidget(history_group)
        
        # Historique des interventions
        interventions_group = QGroupBox("Interventions liées")
        interventions_layout = QVBoxLayout()
        
        self.interventions_list = QListWidget()
        interventions_layout.addWidget(self.interventions_list)
        
        interventions_group.setLayout(interventions_layout)
        layout.addWidget(interventions_group)
        
        return widget
    
    def load_data(self):
        """Charge les données initiales"""
        try:
            # Charger la liste des équipements
            equipment = self.db.get_all_equipment()
            for eq in equipment:
                self.equipment_combo.addItem(f"{eq[1]} - {eq[2]}", eq[0])
            
            # Charger la liste du personnel
            personnel = self.db.get_all_persons()
            self.personnel_data = {p[0]: p[1] for p in personnel}
            
            # Charger la liste des pièces de rechange
            spare_parts = self.db.get_all_spare_parts()
            self.spare_parts_data = {sp[0]: sp[1] for sp in spare_parts}
            
        except Exception as e:
            print(f"Erreur lors du chargement des données: {str(e)}")
    
    def load_work_order_data(self):
        """Charge les données du bon de travail existant"""
        if not self.work_order:
            return
        
        try:
            # Remplir les champs avec les données existantes
            self.title_edit.setText(self.work_order[2] or "")
            self.description_edit.setPlainText(self.work_order[3] or "")
            # ... autres champs
            
        except Exception as e:
            print(f"Erreur lors du chargement du bon de travail: {str(e)}")
    
    def add_personnel(self):
        """Ajoute du personnel au bon de travail"""
        # Implémenter la logique d'ajout de personnel
        pass
    
    def remove_personnel(self):
        """Retire du personnel du bon de travail"""
        # Implémenter la logique de suppression de personnel
        pass
    
    def add_tool(self):
        """Ajoute un outil au bon de travail"""
        # Implémenter la logique d'ajout d'outil
        pass
    
    def remove_tool(self):
        """Retire un outil du bon de travail"""
        # Implémenter la logique de suppression d'outil
        pass
    
    def add_part(self):
        """Ajoute une pièce de rechange"""
        # Implémenter la logique d'ajout de pièce
        pass
    
    def remove_part(self):
        """Retire une pièce de rechange"""
        # Implémenter la logique de suppression de pièce
        pass
    
    def save_work_order(self):
        """Sauvegarde le bon de travail"""
        try:
            # Valider les données
            if not self.title_edit.text().strip():
                QMessageBox.warning(self, "Validation", "Le titre est obligatoire")
                return
            
            # Préparer les données
            work_order_data = {
                'title': self.title_edit.text().strip(),
                'description': self.description_edit.toPlainText(),
                'maintenance_type': self.maintenance_type_combo.currentText(),
                'priority': self.priority_combo.currentText(),
                'status': self.status_combo.currentText(),
                'equipment_id': self.equipment_combo.currentData(),
                'planned_date': self.planned_date_edit.date().toString('yyyy-MM-dd'),
                'estimated_duration': self.estimated_duration_spin.value(),
                'due_date': self.due_date_edit.date().toString('yyyy-MM-dd'),
                'instructions': self.instructions_edit.toPlainText(),
                'safety_notes': self.safety_edit.toPlainText(),
                'notes': self.notes_edit.toPlainText()
            }
            
            if self.work_order_id:
                # Mise à jour
                self.db.update_work_order(self.work_order_id, work_order_data)
                self.work_order_updated.emit(self.work_order_id)
            else:
                # Création
                new_id = self.db.create_work_order(work_order_data)
                self.work_order_created.emit(new_id)
            
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la sauvegarde: {str(e)}")
    
    def delete_work_order(self):
        """Supprime le bon de travail"""
        reply = QMessageBox.question(
            self, "Confirmation",
            "Êtes-vous sûr de vouloir supprimer ce bon de travail ?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                self.db.delete_work_order(self.work_order_id)
                self.accept()
            except Exception as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression: {str(e)}")

class WorkOrdersManager(QWidget, RefreshableWidget):
    """Gestionnaire principal des bons de travail"""
    
    work_order_created = pyqtSignal(int)
    work_order_updated = pyqtSignal(int)
    work_order_deleted = pyqtSignal(int)
    
    def __init__(self, db, parent=None):
        super().__init__(parent)
        RefreshableWidget.__init__(self)
        self.db = db
        
        self.setup_ui()
        self.load_work_orders()
        self.enable_auto_refresh(['work_orders'])
        
        # Timer pour actualisation automatique
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_data)
        self.refresh_timer.start(60000)  # 1 minute
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Barre d'outils
        toolbar = self.create_toolbar()
        layout.addWidget(toolbar)
        
        # Filtres
        filters = self.create_filters()
        layout.addWidget(filters)
        
        # Tableau des bons de travail
        self.work_orders_table = QTableWidget()
        self.work_orders_table.setColumnCount(8)
        self.work_orders_table.setHorizontalHeaderLabels([
            "Numéro", "Titre", "Équipement", "Type", "Priorité", "Statut", 
            "Date planifiée", "Actions"
        ])
        
        header = self.work_orders_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        self.work_orders_table.itemSelectionChanged.connect(self.on_selection_changed)
        self.work_orders_table.doubleClicked.connect(self.edit_work_order)
        
        layout.addWidget(self.work_orders_table)
        
        # Détails du bon de travail sélectionné
        self.details_frame = self.create_details_frame()
        layout.addWidget(self.details_frame)
    
    def create_toolbar(self):
        """Crée la barre d'outils"""
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        
        toolbar_layout = QHBoxLayout(toolbar_frame)
        
        # Titre
        title_label = QLabel("📋 GESTION DES BONS DE TRAVAIL")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
            }
        """)
        
        # Boutons d'action
        new_wo_btn = QPushButton("➕ Nouveau bon de travail")
        new_wo_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        new_wo_btn.clicked.connect(self.create_work_order)
        
        export_btn = QPushButton("📤 Exporter")
        export_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        export_btn.clicked.connect(self.export_work_orders)
        
        toolbar_layout.addWidget(title_label)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(export_btn)
        toolbar_layout.addWidget(new_wo_btn)
        
        return toolbar_frame
    
    def create_filters(self):
        """Crée les filtres"""
        filters_frame = QFrame()
        filters_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 10px;
            }
        """)
        
        filters_layout = QHBoxLayout(filters_frame)
        
        # Recherche
        search_label = QLabel("Recherche:")
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Rechercher dans les bons de travail...")
        self.search_edit.textChanged.connect(self.filter_work_orders)
        
        # Filtre par statut
        status_label = QLabel("Statut:")
        self.status_filter_combo = QComboBox()
        self.status_filter_combo.addItems(["Tous", "Créé", "Planifié", "En cours", "En attente", "Terminé", "Annulé"])
        self.status_filter_combo.currentTextChanged.connect(self.filter_work_orders)
        
        # Filtre par priorité
        priority_label = QLabel("Priorité:")
        self.priority_filter_combo = QComboBox()
        self.priority_filter_combo.addItems(["Toutes", "Basse", "Normale", "Haute", "Critique"])
        self.priority_filter_combo.currentTextChanged.connect(self.filter_work_orders)
        
        # Filtre par type
        type_label = QLabel("Type:")
        self.type_filter_combo = QComboBox()
        self.type_filter_combo.addItems(["Tous", "Préventive", "Corrective", "Prédictive", "Amélioration", "Inspection"])
        self.type_filter_combo.currentTextChanged.connect(self.filter_work_orders)
        
        filters_layout.addWidget(search_label)
        filters_layout.addWidget(self.search_edit)
        filters_layout.addWidget(status_label)
        filters_layout.addWidget(self.status_filter_combo)
        filters_layout.addWidget(priority_label)
        filters_layout.addWidget(self.priority_filter_combo)
        filters_layout.addWidget(type_label)
        filters_layout.addWidget(self.type_filter_combo)
        
        return filters_frame
    
    def create_details_frame(self):
        """Crée le cadre de détails"""
        details_frame = QFrame()
        details_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        
        self.details_layout = QVBoxLayout(details_frame)
        
        # Message par défaut
        default_label = QLabel("Sélectionnez un bon de travail pour voir les détails")
        default_label.setStyleSheet("color: #7f8c8d; font-style: italic;")
        default_label.setAlignment(Qt.AlignCenter)
        
        self.details_layout.addWidget(default_label)
        
        return details_frame
    
    def load_work_orders(self):
        """Charge la liste des bons de travail"""
        try:
            work_orders = self.db.get_work_orders()
            self.populate_work_orders_table(work_orders)
        except Exception as e:
            print(f"Erreur lors du chargement des bons de travail: {str(e)}")
    
    def populate_work_orders_table(self, work_orders):
        """Remplit le tableau des bons de travail"""
        self.work_orders_table.setRowCount(len(work_orders))
        
        for i, wo in enumerate(work_orders):
            # Numéro
            self.work_orders_table.setItem(i, 0, QTableWidgetItem(wo[1] or f"WO-{wo[0]}"))
            
            # Titre
            self.work_orders_table.setItem(i, 1, QTableWidgetItem(wo[2] or ""))
            
            # Équipement
            equipment_name = self.get_equipment_name(wo[6]) if wo[6] else "N/A"
            self.work_orders_table.setItem(i, 2, QTableWidgetItem(equipment_name))
            
            # Type
            self.work_orders_table.setItem(i, 3, QTableWidgetItem(wo[3] or ""))
            
            # Priorité
            priority_item = QTableWidgetItem(wo[4] or "")
            priority_item.setBackground(self.get_priority_color(wo[4]))
            self.work_orders_table.setItem(i, 4, priority_item)
            
            # Statut
            status_item = QTableWidgetItem(wo[5] or "")
            status_item.setBackground(self.get_status_color(wo[5]))
            self.work_orders_table.setItem(i, 5, status_item)
            
            # Date planifiée
            planned_date = wo[7] or "N/A"
            self.work_orders_table.setItem(i, 6, QTableWidgetItem(planned_date))
            
            # Actions
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(2, 2, 2, 2)
            
            edit_btn = QPushButton("✏️")
            edit_btn.setToolTip("Modifier")
            edit_btn.clicked.connect(lambda checked, row=i: self.edit_work_order_at_row(row))
            
            delete_btn = QPushButton("🗑️")
            delete_btn.setToolTip("Supprimer")
            delete_btn.clicked.connect(lambda checked, row=i: self.delete_work_order_at_row(row))
            
            actions_layout.addWidget(edit_btn)
            actions_layout.addWidget(delete_btn)
            actions_layout.addStretch()
            
            self.work_orders_table.setCellWidget(i, 7, actions_widget)
    
    def get_equipment_name(self, equipment_id):
        """Récupère le nom d'un équipement"""
        try:
            equipment = self.db.get_equipment(equipment_id)
            return equipment[1] if equipment else "N/A"
        except:
            return "N/A"
    
    def get_priority_color(self, priority):
        """Retourne la couleur selon la priorité"""
        colors = {
            "Basse": QColor(255, 255, 255),
            "Normale": QColor(255, 255, 200),
            "Haute": QColor(255, 200, 200),
            "Critique": QColor(255, 100, 100)
        }
        return colors.get(priority, QColor(255, 255, 255))
    
    def get_status_color(self, status):
        """Retourne la couleur selon le statut"""
        colors = {
            "Créé": QColor(200, 200, 255),
            "Planifié": QColor(200, 255, 200),
            "En cours": QColor(255, 255, 200),
            "En attente": QColor(255, 200, 200),
            "Terminé": QColor(200, 255, 200),
            "Annulé": QColor(255, 200, 200)
        }
        return colors.get(status, QColor(255, 255, 255))
    
    def filter_work_orders(self):
        """Filtre les bons de travail"""
        # Implémenter la logique de filtrage
        pass
    
    def on_selection_changed(self):
        """Appelé quand la sélection change"""
        current_row = self.work_orders_table.currentRow()
        if current_row >= 0:
            self.show_work_order_details(current_row)
        else:
            self.clear_details()
    
    def show_work_order_details(self, row):
        """Affiche les détails d'un bon de travail"""
        # Récupérer les données de la ligne
        wo_number = self.work_orders_table.item(row, 0).text()
        title = self.work_orders_table.item(row, 1).text()
        
        # Vider le layout des détails
        while self.details_layout.count():
            child = self.details_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
        
        # Créer les détails
        details_title = QLabel(f"📋 Détails du bon de travail {wo_number}")
        details_title.setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50;")
        
        details_content = QLabel(f"Titre: {title}")
        details_content.setStyleSheet("color: #2c3e50;")
        
        self.details_layout.addWidget(details_title)
        self.details_layout.addWidget(details_content)
        self.details_layout.addStretch()
    
    def clear_details(self):
        """Efface les détails"""
        while self.details_layout.count():
            child = self.details_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
        
        default_label = QLabel("Sélectionnez un bon de travail pour voir les détails")
        default_label.setStyleSheet("color: #7f8c8d; font-style: italic;")
        default_label.setAlignment(Qt.AlignCenter)
        
        self.details_layout.addWidget(default_label)
    
    def create_work_order(self):
        """Crée un nouveau bon de travail"""
        dialog = WorkOrderDialog(self.db, self)
        dialog.work_order_created.connect(self.on_work_order_created)
        dialog.exec_()
    
    def edit_work_order(self):
        """Modifie le bon de travail sélectionné"""
        current_row = self.work_orders_table.currentRow()
        if current_row >= 0:
            self.edit_work_order_at_row(current_row)
    
    def edit_work_order_at_row(self, row):
        """Modifie le bon de travail à la ligne spécifiée"""
        # Récupérer l'ID du bon de travail
        wo_id = self.get_work_order_id_at_row(row)
        if wo_id:
            dialog = WorkOrderDialog(self.db, self, wo_id)
            dialog.work_order_updated.connect(self.on_work_order_updated)
            dialog.exec_()
    
    def delete_work_order_at_row(self, row):
        """Supprime le bon de travail à la ligne spécifiée"""
        wo_id = self.get_work_order_id_at_row(row)
        if wo_id:
            reply = QMessageBox.question(
                self, "Confirmation",
                "Êtes-vous sûr de vouloir supprimer ce bon de travail ?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                try:
                    self.db.delete_work_order(wo_id)
                    self.work_order_deleted.emit(wo_id)
                    self.load_work_orders()
                except Exception as e:
                    QMessageBox.critical(self, "Erreur", f"Erreur lors de la suppression: {str(e)}")
    
    def get_work_order_id_at_row(self, row):
        """Récupère l'ID du bon de travail à la ligne spécifiée"""
        # Cette méthode doit être adaptée selon la structure de votre base de données
        try:
            work_orders = self.db.get_work_orders()
            if 0 <= row < len(work_orders):
                return work_orders[row][0]
        except:
            pass
        return None
    
    def export_work_orders(self):
        """Exporte les bons de travail"""
        # Implémenter l'export
        QMessageBox.information(self, "Export", "Fonctionnalité d'export à implémenter")
    
    def on_work_order_created(self, work_order_id):
        """Appelé quand un bon de travail est créé"""
        self.work_order_created.emit(work_order_id)
        self.load_work_orders()
        notify_data_changed('work_orders')
    
    def on_work_order_updated(self, work_order_id):
        """Appelé quand un bon de travail est modifié"""
        self.work_order_updated.emit(work_order_id)
        self.load_work_orders()
        notify_data_changed('work_orders')
    
    def refresh_data(self):
        """Actualise les données"""
        self.load_work_orders()
