#!/usr/bin/env python3
"""
SCRIPT DE LANCEMENT UNIQUE POUR SOTRAMINE PHOSPHATE
Version finale unifiée - Tous les modules implémentés
"""

import sys
import os

def main():
    """Lance l'application SOTRAMINE PHOSPHATE finale"""
    print("🚀 LANCEMENT SOTRAMINE PHOSPHATE")
    print("=" * 50)
    print("📱 Version Finale Unifiée")
    print("🎯 Tous les modules implémentés")
    print("=" * 50)
    
    try:
        # Importer et lancer l'application finale
        from sotramine_app_final import main as app_main
        app_main()
        
    except ImportError as e:
        print(f"❌ Erreur d'import : {str(e)}")
        print("🔧 Vérifiez que tous les modules sont présents")
        
        # Fallback vers l'application de base
        try:
            print("🔄 Tentative de lancement de l'application de base...")
            from app_launcher import main as fallback_main
            fallback_main()
        except Exception as fallback_error:
            print(f"❌ Erreur fallback : {str(fallback_error)}")
            print("💡 Vérifiez l'installation de PyQt5")
        
    except Exception as e:
        print(f"❌ Erreur lors du lancement : {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
