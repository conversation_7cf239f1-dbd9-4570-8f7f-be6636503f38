#!/usr/bin/env python3
"""
Test simple de lancement de l'application
"""

import sys
import os

# Ajouter le répertoire parent au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_app_launch():
    """Test de lancement de l'application"""
    print("🚀 TEST LANCEMENT APPLICATION SOTRAMINE PHOSPHATE")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from database import Database
        from export.excel_export import ExcelExporter
        import main
        
        # Créer une application Qt
        app = QApplication.instance()
        if not app:
            app = QApplication(sys.argv)
        
        print("✓ Application Qt créée")
        
        # Créer la base de données
        db = Database()
        print("✓ Base de données initialisée")
        
        # Créer l'exporteur Excel
        excel_exporter = ExcelExporter(db)
        print("✓ Exporteur Excel créé")
        
        # Créer l'application principale
        main_app = main.OptimizedSotramineApp(db, excel_exporter)
        print("✓ Application principale créée")
        
        # Vérifier les composants principaux
        if hasattr(main_app, 'stacked_widget'):
            print(f"✓ Interface créée avec {main_app.stacked_widget.count()} sections")
        
        if hasattr(main_app, 'sidebar_buttons'):
            print(f"✓ Menu latéral avec {len(main_app.sidebar_buttons)} boutons")
        
        # Vérifier le système d'actualisation
        if hasattr(main_app, 'refresh_manager'):
            print("✓ Système d'actualisation automatique activé")
        
        # Afficher l'application
        main_app.show()
        print("✓ Application affichée")
        
        # Vérifier que l'application est visible
        if main_app.isVisible():
            print("✅ APPLICATION LANCÉE AVEC SUCCÈS !")
            print("\n📋 SECTIONS DISPONIBLES :")
            
            sections = [
                "🏠 Accueil - Tableau de bord principal",
                "📊 Production - Tâches et rapports", 
                "🔧 Maintenance - Système complet (NOUVEAU)",
                "🔌 Équipements - Gestion du parc",
                "🔧 Pièces - Inventaire des pièces",
                "👥 Personnel - Gestion des équipes",
                "📊 Pointage - Suivi des présences",
                "⚙️ Paramètres - Configuration"
            ]
            
            for section in sections:
                print(f"   • {section}")
            
            print("\n🎉 L'application est prête à être utilisée !")
            print("✨ Nouveau système de maintenance professionnel intégré")
            
            return True
        else:
            print("❌ Application créée mais non visible")
            return False
        
    except Exception as e:
        print(f"❌ Erreur lors du lancement : {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Point d'entrée principal"""
    success = test_app_launch()
    
    if success:
        print("\n🎊 TEST DE LANCEMENT RÉUSSI !")
        print("✅ L'application SOTRAMINE PHOSPHATE est opérationnelle")
        print("\n🚀 POUR UTILISER L'APPLICATION :")
        print("   1. L'application est maintenant ouverte")
        print("   2. Naviguez dans le menu latéral")
        print("   3. Testez le nouveau système de maintenance")
        print("   4. Profitez de l'actualisation automatique")
    else:
        print("\n❌ ÉCHEC DU LANCEMENT")
        print("🔧 Vérifiez les erreurs ci-dessus")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        if success:
            # Garder l'application ouverte
            input("\n👋 Appuyez sur Entrée pour fermer l'application...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 Application fermée par l'utilisateur")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Erreur critique : {str(e)}")
        sys.exit(1)
