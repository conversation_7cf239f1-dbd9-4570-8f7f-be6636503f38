#!/usr/bin/env python3
"""
Test Minimal - Identification du problème d'import
"""

import sys
import os

# Ajouter le répertoire parent au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("🔍 Test d'import minimal")
print("=" * 30)

try:
    print("📦 Test 1: Import de PyQt5...")
    from PyQt5.QtWidgets import QWidget
    print("✅ PyQt5 importé avec succès")
except Exception as e:
    print(f"❌ Erreur PyQt5: {e}")
    sys.exit(1)

try:
    print("📦 Test 2: Import de database...")
    from database import Database
    print("✅ database importé avec succès")
except Exception as e:
    print(f"❌ Erreur database: {e}")
    sys.exit(1)

try:
    print("📦 Test 3: Import de gui...")
    import gui
    print("✅ gui importé avec succès")
except Exception as e:
    print(f"❌ Erreur gui: {e}")
    sys.exit(1)

try:
    print("📦 Test 4: Import de maintenance_menu...")
    from gui.maintenance_menu import MaintenanceMenu
    print("✅ maintenance_menu importé avec succès")
except Exception as e:
    print(f"❌ Erreur maintenance_menu: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

print("\n🎉 Tous les imports sont réussis !")
