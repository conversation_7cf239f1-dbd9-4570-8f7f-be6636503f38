#!/usr/bin/env python3
"""
Script de réinitialisation correct pour SOTRAMINE PHOSPHATE
Supprime le bon fichier de base de données (data/tasks.db)
"""

import os
import shutil
from datetime import datetime

def reset_application():
    """Réinitialise complètement l'application"""
    print("🔄 RÉINITIALISATION CORRECTE DE SOTRAMINE PHOSPHATE")
    print("=" * 60)
    
    # Demander confirmation
    print("⚠️ ATTENTION : Cette opération va supprimer TOUTES les données !")
    password = input("Entrez le mot de passe 'sotramine' pour confirmer : ")
    
    if password != 'sotramine':
        print("❌ Mot de passe incorrect - Opération annulée")
        return False
    
    # Créer une sauvegarde
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"backup_reset_{timestamp}"
    
    print(f"\n💾 CRÉATION DE SAUVEGARDE...")
    try:
        os.makedirs(backup_dir, exist_ok=True)
        
        # Sauvegarder le dossier data complet
        if os.path.exists('data'):
            shutil.copytree('data', os.path.join(backup_dir, 'data'))
            print(f"✓ Dossier data/ sauvegardé")
        
        # Sauvegarder le dossier export s'il existe
        if os.path.exists('export'):
            shutil.copytree('export', os.path.join(backup_dir, 'export'))
            print(f"✓ Dossier export/ sauvegardé")
        
        print(f"✅ Sauvegarde créée dans : {backup_dir}")
        
    except Exception as e:
        print(f"❌ Erreur sauvegarde : {str(e)}")
        response = input("Continuer sans sauvegarde ? (o/N) : ")
        if response.lower() not in ['o', 'oui']:
            return False
    
    # Supprimer les données
    print(f"\n🗑️ SUPPRESSION DES DONNÉES...")
    
    deleted_files = []
    
    # Supprimer tous les fichiers .db dans data/
    if os.path.exists('data'):
        for file in os.listdir('data'):
            if file.endswith('.db'):
                file_path = os.path.join('data', file)
                try:
                    size = os.path.getsize(file_path)
                    os.remove(file_path)
                    deleted_files.append(f"{file_path} ({size} bytes)")
                    print(f"✓ Supprimé : {file_path}")
                except Exception as e:
                    print(f"❌ Erreur suppression {file_path} : {str(e)}")
    
    # Supprimer le dossier export
    if os.path.exists('export'):
        try:
            shutil.rmtree('export')
            print(f"✓ Dossier export/ supprimé")
        except Exception as e:
            print(f"❌ Erreur suppression export/ : {str(e)}")
    
    # Supprimer d'autres fichiers possibles
    other_files = ['sotramine.db', 'database.db', 'app.db']
    for file in other_files:
        if os.path.exists(file):
            try:
                os.remove(file)
                print(f"✓ Supprimé : {file}")
                deleted_files.append(file)
            except Exception as e:
                print(f"❌ Erreur suppression {file} : {str(e)}")
    
    # Vérification
    print(f"\n✅ VÉRIFICATION...")
    
    remaining_db_files = []
    
    # Vérifier data/
    if os.path.exists('data'):
        for file in os.listdir('data'):
            if file.endswith('.db'):
                remaining_db_files.append(os.path.join('data', file))
    
    # Vérifier racine
    for file in ['sotramine.db', 'database.db', 'app.db']:
        if os.path.exists(file):
            remaining_db_files.append(file)
    
    if remaining_db_files:
        print(f"⚠️ Fichiers .db encore présents :")
        for file in remaining_db_files:
            print(f"   • {file}")
        return False
    else:
        print(f"✅ Tous les fichiers de base supprimés")
    
    # Résumé
    print(f"\n" + "=" * 60)
    print("📋 RÉSUMÉ DE LA RÉINITIALISATION")
    print("=" * 60)
    
    print(f"💾 Sauvegarde : {backup_dir}")
    print(f"🗑️ Fichiers supprimés : {len(deleted_files)}")
    for file in deleted_files:
        print(f"   • {file}")
    
    print(f"\n🎉 RÉINITIALISATION TERMINÉE AVEC SUCCÈS !")
    print(f"✅ Toutes les données ont été supprimées")
    print(f"🚀 Vous pouvez maintenant relancer l'application")
    
    print(f"\n▶️ COMMANDE POUR RELANCER :")
    print(f"   python main.py")
    
    print(f"\n💾 POUR RESTAURER (si nécessaire) :")
    print(f"   Copiez les fichiers .db depuis {backup_dir}/data/ vers data/")
    
    return True

def main():
    """Point d'entrée principal"""
    try:
        success = reset_application()
        if success:
            print(f"\n👋 Réinitialisation terminée avec succès")
        else:
            print(f"\n⚠️ Réinitialisation incomplète ou annulée")
    except KeyboardInterrupt:
        print(f"\n\n👋 Opération interrompue par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur critique : {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
