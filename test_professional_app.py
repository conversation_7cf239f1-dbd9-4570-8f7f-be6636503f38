#!/usr/bin/env python3
"""
TEST DE L'APPLICATION SOTRAMINE PHOSPHATE PROFESSIONNELLE
Script de test pour vérifier le bon fonctionnement de l'application
"""

import sys
import os
from datetime import datetime

def test_imports():
    """Test des imports nécessaires"""
    print("🔍 Test des imports...")
    
    try:
        import PyQt5
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont
        print("✅ PyQt5 importé avec succès")
        return True
    except ImportError as e:
        print(f"❌ Erreur d'import PyQt5: {e}")
        return False

def test_professional_module():
    """Test du module professionnel"""
    print("🔍 Test du module professionnel...")
    
    try:
        from sotramine_professional import SotramineProfessionalApp, ProfessionalTheme
        print("✅ Module professionnel importé avec succès")
        
        # Test de la classe thème
        theme = ProfessionalTheme()
        print(f"✅ Thème professionnel créé - Couleur primaire: {theme.PRIMARY}")
        
        return True
    except ImportError as e:
        print(f"❌ Erreur d'import module professionnel: {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur lors du test du module: {e}")
        return False

def test_application_creation():
    """Test de création de l'application"""
    print("🔍 Test de création de l'application...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from sotramine_professional import SotramineProfessionalApp
        
        # Créer l'application Qt (sans affichage)
        app = QApplication([])
        app.setQuitOnLastWindowClosed(False)
        
        # Créer la fenêtre principale
        window = SotramineProfessionalApp()
        print("✅ Application professionnelle créée avec succès")
        
        # Test des propriétés
        print(f"✅ Titre: {window.windowTitle()}")
        print(f"✅ Taille: {window.size().width()}x{window.size().height()}")
        
        # Nettoyer
        window.close()
        app.quit()
        
        return True
    except Exception as e:
        print(f"❌ Erreur lors de la création de l'application: {e}")
        return False

def test_theme_colors():
    """Test des couleurs du thème"""
    print("🔍 Test des couleurs du thème...")
    
    try:
        from sotramine_professional import ProfessionalTheme
        
        theme = ProfessionalTheme()
        
        # Vérifier les couleurs principales
        colors = {
            "PRIMARY": theme.PRIMARY,
            "SECONDARY": theme.SECONDARY,
            "ACCENT": theme.ACCENT,
            "SUCCESS": theme.SUCCESS,
            "WARNING": theme.WARNING,
            "DANGER": theme.DANGER,
            "INFO": theme.INFO
        }
        
        print("✅ Couleurs du thème professionnel:")
        for name, color in colors.items():
            print(f"   {name}: {color}")
        
        return True
    except Exception as e:
        print(f"❌ Erreur lors du test des couleurs: {e}")
        return False

def run_quick_test():
    """Lance un test rapide de l'application"""
    print("🚀 LANCEMENT TEST RAPIDE - APPLICATION PROFESSIONNELLE")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from sotramine_professional import SotramineProfessionalApp
        
        # Créer l'application
        app = QApplication([])
        
        # Créer et afficher brièvement la fenêtre
        window = SotramineProfessionalApp()
        window.show()
        
        print("✅ Application affichée avec succès")
        print("🎨 Interface professionnelle chargée")
        print("📊 Modules disponibles et fonctionnels")
        
        # Fermer après un court délai
        from PyQt5.QtCore import QTimer
        QTimer.singleShot(2000, app.quit)  # Fermer après 2 secondes
        
        # Lancer la boucle d'événements
        app.exec_()
        
        print("✅ Test rapide terminé avec succès")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du test rapide: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🧪 TESTS DE L'APPLICATION SOTRAMINE PHOSPHATE PROFESSIONNELLE")
    print("=" * 70)
    print(f"📅 Date: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("=" * 70)
    
    tests_results = []
    
    # Test 1: Imports
    tests_results.append(("Imports PyQt5", test_imports()))
    
    # Test 2: Module professionnel
    tests_results.append(("Module professionnel", test_professional_module()))
    
    # Test 3: Couleurs du thème
    tests_results.append(("Couleurs du thème", test_theme_colors()))
    
    # Test 4: Création de l'application
    tests_results.append(("Création application", test_application_creation()))
    
    print("\n" + "=" * 70)
    print("📊 RÉSULTATS DES TESTS")
    print("=" * 70)
    
    passed = 0
    total = len(tests_results)
    
    for test_name, result in tests_results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHEC"
        print(f"{test_name:.<30} {status}")
        if result:
            passed += 1
    
    print("=" * 70)
    print(f"📈 RÉSULTAT GLOBAL: {passed}/{total} tests réussis ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 TOUS LES TESTS SONT RÉUSSIS !")
        print("✅ L'application professionnelle est prête à être utilisée")
        
        # Proposer un test rapide
        print("\n🚀 Voulez-vous lancer un test rapide de l'interface ?")
        response = input("Tapez 'oui' pour lancer le test rapide: ").lower().strip()
        
        if response in ['oui', 'o', 'yes', 'y']:
            print("\n🎨 Lancement du test rapide de l'interface...")
            run_quick_test()
        
        return 0
    else:
        print("⚠️ Certains tests ont échoué")
        print("🔧 Vérifiez l'installation et les dépendances")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
