"""
Gestion de la maintenance préventive
Module professionnel pour la planification et le suivi de la maintenance préventive
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
                             QTableWidgetItem, QPushButton, QLineEdit, QComboBox,
                             QLabel, QFrame, QSplitter, QGroupBox, QFormLayout,
                             QTextEdit, QDateEdit, QHeaderView, QMessageBox,
                             QDialog, QTabWidget, QProgressBar, QCheckBox,
                             QSpinBox, QDoubleSpinBox, QCalendarWidget, QTimeEdit,
                             QListWidget, QListWidgetItem, QTreeWidget, QTreeWidgetItem)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QTime, QDateTime, QTimer
from PyQt5.QtGui import QFont, QPixmap, QIcon, QColor
from utils.data_refresh_manager import RefreshableWidget, notify_data_changed

class PreventiveMaintenance(QWidget, RefreshableWidget):
    """Gestionnaire de maintenance préventive"""
    
    maintenance_scheduled = pyqtSignal(int)
    maintenance_updated = pyqtSignal(int)
    
    def __init__(self, db, parent=None):
        super().__init__(parent)
        RefreshableWidget.__init__(self)
        self.db = db
        self.current_schedule = None
        
        self.setup_ui()
        self.load_data()
        self.enable_auto_refresh(['maintenance', 'equipment'])
        
        # Timer pour vérification des maintenances dues
        self.check_timer = QTimer()
        self.check_timer.timeout.connect(self.check_due_maintenance)
        self.check_timer.start(300000)  # Vérification toutes les 5 minutes
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Barre d'outils
        toolbar = self.create_preventive_toolbar()
        layout.addWidget(toolbar)
        
        # Onglets principaux
        self.main_tabs = QTabWidget()
        self.main_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #dcdde1;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f8f9fa;
                border: 1px solid #dcdde1;
                padding: 12px 20px;
                margin-right: 2px;
                font-weight: bold;
                min-width: 150px;
            }
            QTabBar::tab:selected {
                background-color: #9b59b6;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #8e44ad;
                color: white;
            }
        """)
        
        # Onglet Planification
        self.planning_tab = self.create_planning_tab()
        self.main_tabs.addTab(self.planning_tab, "📅 Planification")
        
        # Onglet Maintenances Dues
        self.due_tab = self.create_due_maintenance_tab()
        self.main_tabs.addTab(self.due_tab, "⚠️ Maintenances Dues")
        
        # Onglet Modèles de Maintenance
        self.templates_tab = self.create_templates_tab()
        self.main_tabs.addTab(self.templates_tab, "📋 Modèles")
        
        # Onglet Calendrier
        self.calendar_tab = self.create_calendar_tab()
        self.main_tabs.addTab(self.calendar_tab, "📆 Calendrier")
        
        layout.addWidget(self.main_tabs)
    
    def create_preventive_toolbar(self):
        """Crée la barre d'outils de maintenance préventive"""
        toolbar_frame = QFrame()
        toolbar_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        
        layout = QHBoxLayout(toolbar_frame)
        layout.setContentsMargins(15, 10, 15, 10)
        layout.setSpacing(15)
        
        # Indicateurs de statut
        status_label = QLabel("📊 Statut :")
        status_label.setStyleSheet("font-weight: bold; color: #495057;")
        layout.addWidget(status_label)
        
        # Compteurs
        self.due_count_label = QLabel("0 due(s)")
        self.due_count_label.setStyleSheet("""
            QLabel {
                background-color: #f8d7da;
                color: #721c24;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 13px;
            }
        """)
        layout.addWidget(self.due_count_label)
        
        self.upcoming_count_label = QLabel("0 prochaine(s)")
        self.upcoming_count_label.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                color: #856404;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 13px;
            }
        """)
        layout.addWidget(self.upcoming_count_label)
        
        self.scheduled_count_label = QLabel("0 planifiée(s)")
        self.scheduled_count_label.setStyleSheet("""
            QLabel {
                background-color: #d4edda;
                color: #155724;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 13px;
            }
        """)
        layout.addWidget(self.scheduled_count_label)
        
        layout.addStretch()
        
        # Boutons d'actions
        actions_layout = QHBoxLayout()
        actions_layout.setSpacing(8)
        
        # Bouton Nouvelle Planification
        new_schedule_btn = QPushButton("📅 Nouvelle Planification")
        new_schedule_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        new_schedule_btn.clicked.connect(self.create_maintenance_schedule)
        actions_layout.addWidget(new_schedule_btn)
        
        # Bouton Nouveau Modèle
        new_template_btn = QPushButton("📋 Nouveau Modèle")
        new_template_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        new_template_btn.clicked.connect(self.create_maintenance_template)
        actions_layout.addWidget(new_template_btn)
        
        # Bouton Vérifier Maintenances
        check_btn = QPushButton("🔍 Vérifier Maintenances")
        check_btn.setStyleSheet("""
            QPushButton {
                background-color: #ffc107;
                color: #212529;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
        """)
        check_btn.clicked.connect(self.check_due_maintenance)
        actions_layout.addWidget(check_btn)
        
        layout.addLayout(actions_layout)
        
        return toolbar_frame
    
    def create_planning_tab(self):
        """Crée l'onglet de planification"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Splitter pour diviser en deux parties
        splitter = QSplitter(Qt.Horizontal)
        
        # Panneau gauche : Liste des planifications
        left_panel = self.create_schedules_list_panel()
        splitter.addWidget(left_panel)
        
        # Panneau droit : Détails de la planification
        right_panel = self.create_schedule_details_panel()
        splitter.addWidget(right_panel)
        
        # Proportions du splitter
        splitter.setSizes([700, 400])
        layout.addWidget(splitter)
        
        return tab
    
    def create_schedules_list_panel(self):
        """Crée le panneau de liste des planifications"""
        panel = QFrame()
        panel.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }
        """)
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)
        
        # En-tête
        header_layout = QHBoxLayout()
        
        title_label = QLabel("📅 Planifications de Maintenance Préventive")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #495057;
            }
        """)
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Filtre par équipement
        equipment_filter_label = QLabel("🔌 Équipement :")
        equipment_filter_label.setStyleSheet("font-weight: bold; color: #495057;")
        header_layout.addWidget(equipment_filter_label)
        
        self.equipment_filter = QComboBox()
        self.equipment_filter.addItem("Tous")
        self.load_equipment_filter()
        self.equipment_filter.setStyleSheet("""
            QComboBox {
                padding: 6px 10px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 13px;
                min-width: 150px;
            }
        """)
        self.equipment_filter.currentTextChanged.connect(self.filter_schedules)
        header_layout.addWidget(self.equipment_filter)
        
        layout.addLayout(header_layout)
        
        # Table des planifications
        self.schedules_table = QTableWidget()
        self.schedules_table.setColumnCount(8)
        self.schedules_table.setHorizontalHeaderLabels([
            "ID", "Équipement", "Type", "Fréquence", "Dernière", 
            "Prochaine", "Statut", "Technicien"
        ])
        
        # Configuration de la table
        self.schedules_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.schedules_table.setSelectionMode(QTableWidget.SingleSelection)
        self.schedules_table.setAlternatingRowColors(True)
        self.schedules_table.setSortingEnabled(True)
        
        # Style de la table
        self.schedules_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #e3f2fd;
                border: none;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                padding: 10px;
                border: none;
                border-bottom: 2px solid #dee2e6;
                font-weight: bold;
                color: #495057;
            }
        """)
        
        # Ajuster les colonnes
        header = self.schedules_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # ID
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # Équipement
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Type
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Fréquence
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Dernière
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Prochaine
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Statut
        header.setSectionResizeMode(7, QHeaderView.ResizeToContents)  # Technicien
        
        # Connecter les signaux
        self.schedules_table.itemSelectionChanged.connect(self.on_schedule_selection_changed)
        self.schedules_table.itemDoubleClicked.connect(self.edit_schedule)
        
        layout.addWidget(self.schedules_table)
        
        return panel
    
    def create_schedule_details_panel(self):
        """Crée le panneau de détails de la planification"""
        panel = QFrame()
        panel.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }
        """)
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # En-tête des détails
        header_label = QLabel("📋 Détails de la Planification")
        header_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #495057;
                padding-bottom: 10px;
                border-bottom: 2px solid #9b59b6;
            }
        """)
        layout.addWidget(header_label)
        
        # Informations de base
        info_group = QGroupBox("ℹ️ Informations Générales")
        info_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #9b59b6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px;
                color: #9b59b6;
            }
        """)
        
        info_layout = QFormLayout(info_group)
        
        # Labels d'informations
        self.schedule_info_labels = {}
        
        info_fields = [
            ("equipment_name", "Équipement :"),
            ("maintenance_type", "Type de maintenance :"),
            ("frequency", "Fréquence :"),
            ("frequency_unit", "Unité :"),
            ("last_maintenance", "Dernière maintenance :"),
            ("next_maintenance", "Prochaine maintenance :"),
            ("assigned_technician", "Technicien assigné :"),
            ("status", "Statut :"),
            ("notes", "Notes :")
        ]
        
        for field_id, label_text in info_fields:
            label = QLabel(label_text)
            label.setStyleSheet("font-weight: bold; color: #495057;")
            
            if field_id == "notes":
                value_label = QLabel()
                value_label.setWordWrap(True)
                value_label.setMaximumHeight(60)
            else:
                value_label = QLabel()
            
            value_label.setStyleSheet("""
                QLabel {
                    background-color: white;
                    border: 1px solid #dee2e6;
                    border-radius: 4px;
                    padding: 8px;
                    color: #495057;
                }
            """)
            
            self.schedule_info_labels[field_id] = value_label
            info_layout.addRow(label, value_label)
        
        layout.addWidget(info_group)
        
        # Historique des maintenances
        history_group = QGroupBox("📋 Historique des Maintenances")
        history_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #17a2b8;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px;
                color: #17a2b8;
            }
        """)
        
        history_layout = QVBoxLayout(history_group)
        
        self.maintenance_history_table = QTableWidget()
        self.maintenance_history_table.setColumnCount(4)
        self.maintenance_history_table.setHorizontalHeaderLabels([
            "Date", "Type", "Technicien", "Statut"
        ])
        self.maintenance_history_table.setMaximumHeight(150)
        self.maintenance_history_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                padding: 8px;
                border: none;
                border-bottom: 1px solid #dee2e6;
                font-weight: bold;
                color: #495057;
            }
        """)
        
        history_layout.addWidget(self.maintenance_history_table)
        layout.addWidget(history_group)
        
        # Boutons d'actions
        actions_frame = QFrame()
        actions_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-radius: 6px;
                padding: 10px;
            }
        """)
        
        actions_layout = QHBoxLayout(actions_frame)
        actions_layout.setSpacing(10)
        
        # Bouton Modifier
        self.edit_schedule_btn = QPushButton("✏️ Modifier")
        self.edit_schedule_btn.setStyleSheet("""
            QPushButton {
                background-color: #ffc107;
                color: #212529;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
            QPushButton:disabled {
                background-color: #6c757d;
                color: white;
            }
        """)
        self.edit_schedule_btn.clicked.connect(self.edit_schedule)
        self.edit_schedule_btn.setEnabled(False)
        actions_layout.addWidget(self.edit_schedule_btn)
        
        # Bouton Exécuter Maintenance
        self.execute_btn = QPushButton("🔧 Exécuter Maintenance")
        self.execute_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
        self.execute_btn.clicked.connect(self.execute_maintenance)
        self.execute_btn.setEnabled(False)
        actions_layout.addWidget(self.execute_btn)
        
        actions_layout.addStretch()
        
        # Bouton Supprimer
        self.delete_schedule_btn = QPushButton("🗑️ Supprimer")
        self.delete_schedule_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
        self.delete_schedule_btn.clicked.connect(self.delete_schedule)
        self.delete_schedule_btn.setEnabled(False)
        actions_layout.addWidget(self.delete_schedule_btn)
        
        layout.addWidget(actions_frame)
        
        return panel

    def create_due_maintenance_tab(self):
        """Crée l'onglet des maintenances dues"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # En-tête avec alerte
        alert_frame = QFrame()
        alert_frame.setStyleSheet("""
            QFrame {
                background-color: #f8d7da;
                border: 1px solid #f5c6cb;
                border-radius: 8px;
                padding: 15px;
            }
        """)

        alert_layout = QHBoxLayout(alert_frame)

        alert_icon = QLabel("⚠️")
        alert_icon.setStyleSheet("font-size: 24px; color: #721c24;")
        alert_layout.addWidget(alert_icon)

        alert_text = QLabel("Maintenances Préventives Dues ou En Retard")
        alert_text.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #721c24;
            }
        """)
        alert_layout.addWidget(alert_text)

        alert_layout.addStretch()

        # Bouton Planifier Toutes
        schedule_all_btn = QPushButton("📅 Planifier Toutes")
        schedule_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        schedule_all_btn.clicked.connect(self.schedule_all_due_maintenance)
        alert_layout.addWidget(schedule_all_btn)

        layout.addWidget(alert_frame)

        # Table des maintenances dues
        self.due_maintenance_table = QTableWidget()
        self.due_maintenance_table.setColumnCount(7)
        self.due_maintenance_table.setHorizontalHeaderLabels([
            "Équipement", "Type", "Dernière", "Due Depuis",
            "Priorité", "Technicien", "Actions"
        ])

        # Configuration de la table
        self.due_maintenance_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.due_maintenance_table.setAlternatingRowColors(True)
        self.due_maintenance_table.setSortingEnabled(True)

        # Style de la table avec couleurs d'alerte
        self.due_maintenance_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #fff5f5;
                selection-background-color: #ffebee;
                border: 1px solid #f5c6cb;
                border-radius: 8px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f5c6cb;
            }
            QTableWidget::item:selected {
                background-color: #ffebee;
                color: #d32f2f;
            }
            QHeaderView::section {
                background-color: #f8d7da;
                padding: 10px;
                border: none;
                border-bottom: 2px solid #f5c6cb;
                font-weight: bold;
                color: #721c24;
            }
        """)

        # Ajuster les colonnes
        header = self.due_maintenance_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # Équipement
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Type
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Dernière
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Due Depuis
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Priorité
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Technicien
        header.setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Actions

        layout.addWidget(self.due_maintenance_table)

        return tab

    def create_templates_tab(self):
        """Crée l'onglet des modèles de maintenance"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Barre d'outils modèles
        templates_toolbar = QFrame()
        templates_toolbar.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 10px;
            }
        """)

        toolbar_layout = QHBoxLayout(templates_toolbar)
        toolbar_layout.setContentsMargins(15, 10, 15, 10)
        toolbar_layout.setSpacing(15)

        # Recherche
        search_label = QLabel("🔍 Recherche :")
        search_label.setStyleSheet("font-weight: bold; color: #495057;")
        toolbar_layout.addWidget(search_label)

        self.templates_search = QLineEdit()
        self.templates_search.setPlaceholderText("Nom du modèle, type...")
        self.templates_search.setStyleSheet("""
            QLineEdit {
                padding: 8px 12px;
                border: 1px solid #ced4da;
                border-radius: 4px;
                font-size: 14px;
                min-width: 250px;
            }
        """)
        self.templates_search.textChanged.connect(self.filter_templates)
        toolbar_layout.addWidget(self.templates_search)

        toolbar_layout.addStretch()

        # Bouton Nouveau Modèle
        new_template_btn = QPushButton("📋 Nouveau Modèle")
        new_template_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        new_template_btn.clicked.connect(self.create_maintenance_template)
        toolbar_layout.addWidget(new_template_btn)

        layout.addWidget(templates_toolbar)

        # Table des modèles
        self.templates_table = QTableWidget()
        self.templates_table.setColumnCount(6)
        self.templates_table.setHorizontalHeaderLabels([
            "Nom", "Type", "Fréquence", "Durée", "Utilisations", "Actions"
        ])

        # Configuration de la table
        self.templates_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.templates_table.setAlternatingRowColors(True)
        self.templates_table.setSortingEnabled(True)

        # Style de la table
        self.templates_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #dee2e6;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #e3f2fd;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                padding: 10px;
                border: none;
                border-bottom: 2px solid #dee2e6;
                font-weight: bold;
                color: #495057;
            }
        """)

        # Ajuster les colonnes
        header = self.templates_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # Nom
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Type
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Fréquence
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Durée
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Utilisations
        header.setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Actions

        layout.addWidget(self.templates_table)

        return tab
