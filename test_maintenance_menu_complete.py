#!/usr/bin/env python3
"""
Test du Menu de Maintenance Complet
Vérifie l'intégration de tous les modules de maintenance
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt

# Ajouter le répertoire parent au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_maintenance_menu():
    """Teste le menu de maintenance complet"""
    try:
        # Créer l'application
        app = QApplication(sys.argv)
        
        # Créer la fenêtre principale
        main_window = QMainWindow()
        main_window.setWindowTitle("Test - Menu de Maintenance Complet")
        main_window.setGeometry(100, 100, 1400, 900)
        
        # Widget central
        central_widget = QWidget()
        main_window.setCentralWidget(central_widget)
        
        # Layout principal
        layout = QVBoxLayout(central_widget)
        
        # Importer et créer le menu de maintenance
        try:
            from gui.maintenance_menu import MaintenanceMenu
            from database import Database
            
            # Créer une base de données de test
            db = Database(":memory:")
            
            # Créer le menu de maintenance
            maintenance_menu = MaintenanceMenu(db, central_widget)
            layout.addWidget(maintenance_menu)
            
            print("✅ Menu de maintenance créé avec succès")
            print("✅ Tous les modules sont intégrés")
            
        except ImportError as e:
            print(f"❌ Erreur d'import: {e}")
            return False
        except Exception as e:
            print(f"❌ Erreur lors de la création du menu: {e}")
            return False
        
        # Afficher la fenêtre
        main_window.show()
        
        print("\n🎯 Test réussi ! Le menu de maintenance est fonctionnel.")
        print("📋 Onglets disponibles:")
        print("   - 🔧 Interventions & Bons de Travail")
        print("   - 📅 Planification Préventive") 
        print("   - 📋 Bons de Travail")
        print("   - 📊 Historique & Indicateurs")
        print("   - 📈 Indicateurs Avancés")
        print("   - ✅ Checklists de Maintenance")
        print("\n🚀 L'application est prête à être utilisée !")
        
        # Lancer l'application
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ Erreur critique: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Test du Menu de Maintenance Complet")
    print("=" * 50)
    test_maintenance_menu()
