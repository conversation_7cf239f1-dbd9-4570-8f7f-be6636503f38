"""
Barre d'outils contextuelle qui s'adapte selon la section active
"""

from PyQt5.QtWidgets import (QToolBar, QWidget, QHBoxLayout, QVBoxLayout, 
                            QPushButton, QLabel, QComboBox, QLineEdit, 
                            QFrame, QSizePolicy, QSpacerItem, QButtonGroup,
                            QToolButton, QMenu)
from PyQt5.QtCore import Qt, pyqtSignal, QSize
from PyQt5.QtGui import QIcon, QFont
from gui.style import PRIMARY_COLOR, SECONDARY_COLOR, BACKGROUND_COLOR

class QuickActionButton(QPushButton):
    """Bouton d'action rapide avec style moderne"""
    
    def __init__(self, text, icon_name=None, tooltip=None, parent=None):
        super().__init__(text, parent)
        self.icon_name = icon_name
        self.setup_ui()
        
        if tooltip:
            self.setToolTip(tooltip)
    
    def setup_ui(self):
        """Configure l'apparence du bouton"""
        self.setMinimumHeight(36)
        self.setMinimumWidth(100)
        
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {PRIMARY_COLOR};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: 500;
                font-size: 13px;
            }}
            QPushButton:hover {{
                background-color: {PRIMARY_COLOR}dd;
                border: 2px solid {PRIMARY_COLOR};
            }}
            QPushButton:pressed {{
                background-color: {PRIMARY_COLOR}aa;
                border: 2px solid {PRIMARY_COLOR}88;
            }}
            QPushButton:disabled {{
                background-color: #bdc3c7;
                color: #7f8c8d;
            }}
        """)
        
        if self.icon_name:
            icon_path = f"resources/icons/{self.icon_name}.png"
            try:
                self.setIcon(QIcon(icon_path))
                self.setIconSize(QSize(16, 16))
            except:
                pass

class SearchWidget(QWidget):
    """Widget de recherche intégré"""
    
    search_requested = pyqtSignal(str)
    filter_requested = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """Configure l'interface de recherche"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)
        
        # Champ de recherche
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("🔍 Rechercher...")
        self.search_input.setMinimumWidth(200)
        self.search_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #e9ecef;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 13px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3498db;
                outline: none;
            }
        """)
        self.search_input.textChanged.connect(self.on_search_changed)
        layout.addWidget(self.search_input)
        
        # Bouton de filtre
        self.filter_button = QPushButton("🔽 Filtres")
        self.filter_button.setStyleSheet("""
            QPushButton {
                background-color: #f8f9fa;
                border: 2px solid #e9ecef;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #e9ecef;
                border-color: #dee2e6;
            }
        """)
        self.filter_button.clicked.connect(self.show_filter_menu)
        layout.addWidget(self.filter_button)
    
    def on_search_changed(self, text):
        """Émet le signal de recherche"""
        self.search_requested.emit(text)
    
    def show_filter_menu(self):
        """Affiche le menu de filtres"""
        # TODO: Implémenter le menu de filtres
        pass
    
    def clear_search(self):
        """Vide le champ de recherche"""
        self.search_input.clear()

class ContextualToolbar(QWidget):
    """Barre d'outils qui s'adapte au contexte"""
    
    # Signaux
    action_triggered = pyqtSignal(str, object)
    search_requested = pyqtSignal(str)
    filter_requested = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_context = None
        self.context_widgets = {}
        self.setup_ui()
    
    def setup_ui(self):
        """Configure l'interface de la barre d'outils"""
        self.setFixedHeight(60)
        self.setStyleSheet(f"""
            QWidget {{
                background-color: white;
                border-bottom: 1px solid #e9ecef;
            }}
        """)
        
        # Layout principal
        self.main_layout = QHBoxLayout(self)
        self.main_layout.setContentsMargins(16, 8, 16, 8)
        self.main_layout.setSpacing(16)
        
        # Zone de titre/contexte
        self.context_label = QLabel("Tableau de bord")
        self.context_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
            }
        """)
        self.main_layout.addWidget(self.context_label)
        
        # Espaceur flexible
        self.main_layout.addStretch()
        
        # Zone d'actions contextuelles
        self.actions_widget = QWidget()
        self.actions_layout = QHBoxLayout(self.actions_widget)
        self.actions_layout.setContentsMargins(0, 0, 0, 0)
        self.actions_layout.setSpacing(8)
        self.main_layout.addWidget(self.actions_widget)
        
        # Widget de recherche (masqué par défaut)
        self.search_widget = SearchWidget()
        self.search_widget.search_requested.connect(self.search_requested)
        self.search_widget.filter_requested.connect(self.filter_requested)
        self.search_widget.hide()
        self.main_layout.addWidget(self.search_widget)
    
    def set_context(self, context, title=None):
        """Change le contexte de la barre d'outils"""
        self.current_context = context
        
        if title:
            self.context_label.setText(title)
        
        # Nettoyer les actions actuelles
        self.clear_actions()
        
        # Masquer/afficher la recherche selon le contexte
        if context in ['tasks', 'equipment', 'personnel', 'spare_parts']:
            self.search_widget.show()
        else:
            self.search_widget.hide()
        
        # Ajouter les actions contextuelles
        self.add_context_actions(context)
    
    def clear_actions(self):
        """Supprime toutes les actions actuelles"""
        while self.actions_layout.count():
            child = self.actions_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
    
    def add_context_actions(self, context):
        """Ajoute les actions selon le contexte"""
        if context == 'dashboard':
            self.add_dashboard_actions()
        elif context == 'tasks':
            self.add_tasks_actions()
        elif context == 'equipment':
            self.add_equipment_actions()
        elif context == 'personnel':
            self.add_personnel_actions()
        elif context == 'spare_parts':
            self.add_spare_parts_actions()
        elif context == 'attendance':
            self.add_attendance_actions()
        elif context == 'reports':
            self.add_reports_actions()
    
    def add_dashboard_actions(self):
        """Actions pour le tableau de bord"""
        refresh_btn = QuickActionButton("🔄 Actualiser", "refresh", "Actualiser les données")
        refresh_btn.clicked.connect(lambda: self.action_triggered.emit('refresh_dashboard', None))
        self.actions_layout.addWidget(refresh_btn)
        
        export_btn = QuickActionButton("📊 Exporter", "export", "Exporter le tableau de bord")
        export_btn.clicked.connect(lambda: self.action_triggered.emit('export_dashboard', None))
        self.actions_layout.addWidget(export_btn)
    
    def add_tasks_actions(self):
        """Actions pour la gestion des tâches"""
        new_task_btn = QuickActionButton("✚ Nouvelle tâche", "add", "Créer une nouvelle tâche")
        new_task_btn.clicked.connect(lambda: self.action_triggered.emit('new_task', None))
        self.actions_layout.addWidget(new_task_btn)
        
        import_btn = QuickActionButton("📥 Importer", "import", "Importer des tâches")
        import_btn.clicked.connect(lambda: self.action_triggered.emit('import_tasks', None))
        self.actions_layout.addWidget(import_btn)
        
        export_btn = QuickActionButton("📤 Exporter", "export", "Exporter les tâches")
        export_btn.clicked.connect(lambda: self.action_triggered.emit('export_tasks', None))
        self.actions_layout.addWidget(export_btn)
    
    def add_equipment_actions(self):
        """Actions pour la gestion des équipements"""
        new_equipment_btn = QuickActionButton("✚ Nouvel équipement", "add", "Ajouter un équipement")
        new_equipment_btn.clicked.connect(lambda: self.action_triggered.emit('new_equipment', None))
        self.actions_layout.addWidget(new_equipment_btn)
        
        maintenance_btn = QuickActionButton("🔧 Maintenance", "maintenance", "Planifier une maintenance")
        maintenance_btn.clicked.connect(lambda: self.action_triggered.emit('schedule_maintenance', None))
        self.actions_layout.addWidget(maintenance_btn)
        
        import_btn = QuickActionButton("📥 Importer", "import", "Importer des équipements")
        import_btn.clicked.connect(lambda: self.action_triggered.emit('import_equipment', None))
        self.actions_layout.addWidget(import_btn)
        
        export_btn = QuickActionButton("📤 Exporter", "export", "Exporter les équipements")
        export_btn.clicked.connect(lambda: self.action_triggered.emit('export_equipment', None))
        self.actions_layout.addWidget(export_btn)
    
    def add_personnel_actions(self):
        """Actions pour la gestion du personnel"""
        new_person_btn = QuickActionButton("✚ Nouveau personnel", "add", "Ajouter une personne")
        new_person_btn.clicked.connect(lambda: self.action_triggered.emit('new_person', None))
        self.actions_layout.addWidget(new_person_btn)
        
        import_btn = QuickActionButton("📥 Importer", "import", "Importer du personnel")
        import_btn.clicked.connect(lambda: self.action_triggered.emit('import_personnel', None))
        self.actions_layout.addWidget(import_btn)
    
    def add_spare_parts_actions(self):
        """Actions pour la gestion des pièces de rechange"""
        new_part_btn = QuickActionButton("✚ Nouvelle pièce", "add", "Ajouter une pièce")
        new_part_btn.clicked.connect(lambda: self.action_triggered.emit('new_spare_part', None))
        self.actions_layout.addWidget(new_part_btn)
        
        inventory_btn = QuickActionButton("📦 Inventaire", "inventory", "Faire l'inventaire")
        inventory_btn.clicked.connect(lambda: self.action_triggered.emit('inventory_check', None))
        self.actions_layout.addWidget(inventory_btn)
    
    def add_attendance_actions(self):
        """Actions pour la gestion du pointage"""
        today_btn = QuickActionButton("📅 Aujourd'hui", "today", "Pointage d'aujourd'hui")
        today_btn.clicked.connect(lambda: self.action_triggered.emit('attendance_today', None))
        self.actions_layout.addWidget(today_btn)
        
        report_btn = QuickActionButton("📊 Rapport", "report", "Rapport de pointage")
        report_btn.clicked.connect(lambda: self.action_triggered.emit('attendance_report', None))
        self.actions_layout.addWidget(report_btn)
    
    def add_reports_actions(self):
        """Actions pour les rapports"""
        daily_btn = QuickActionButton("📄 Journalier", "daily", "Rapport journalier")
        daily_btn.clicked.connect(lambda: self.action_triggered.emit('daily_report', None))
        self.actions_layout.addWidget(daily_btn)
        
        monthly_btn = QuickActionButton("📅 Mensuel", "monthly", "Rapport mensuel")
        monthly_btn.clicked.connect(lambda: self.action_triggered.emit('monthly_report', None))
        self.actions_layout.addWidget(monthly_btn)
        
        custom_btn = QuickActionButton("🔧 Personnalisé", "custom", "Rapport personnalisé")
        custom_btn.clicked.connect(lambda: self.action_triggered.emit('custom_report', None))
        self.actions_layout.addWidget(custom_btn)
    
    def update_title(self, title):
        """Met à jour le titre affiché"""
        self.context_label.setText(title)
    
    def get_search_text(self):
        """Retourne le texte de recherche actuel"""
        return self.search_widget.search_input.text()
    
    def clear_search(self):
        """Vide le champ de recherche"""
        self.search_widget.clear_search()
