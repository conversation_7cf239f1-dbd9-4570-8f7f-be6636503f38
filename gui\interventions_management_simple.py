"""
Gestion simplifiée des interventions et bons de travail
Version simplifiée pour éviter les conflits d'import
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                             QTableWidgetItem, QPushButton, QLineEdit, QComboBox,
                             QLabel, QFrame, QSplitter, QGroupBox, QFormLayout,
                             QTextEdit, QDateEdit, QHeaderView, QMessageBox,
                             QDialog, QTabWidget, QProgressBar, QCheckBox,
                             QSpinBox, QDoubleSpinBox, QCalendarWidget, QTimeEdit,
                             QListWidget, QListWidgetItem, QTreeWidget, QTreeWidgetItem)
from PyQt5.QtCore import Qt, pyqtSignal, QDate, QTime, QDateTime, QTimer
from PyQt5.QtGui import QFont, QPixmap, QIcon, QColor

class InterventionsManagement(QWidget):
    """Gestionnaire simplifié des interventions et bons de travail"""

    intervention_created = pyqtSignal(int)
    intervention_updated = pyqtSignal(int)
    work_order_created = pyqtSignal(int)

    def __init__(self, db, parent=None):
        super().__init__(parent)
        self.db = db
        self.current_intervention = None
        self.current_work_order = None

        self.setup_ui()
        self.load_data()

        # Timer pour actualisation automatique
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_data)
        self.refresh_timer.start(60000)  # Actualisation toutes les minutes

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # En-tête
        header = QLabel("🔧 Gestion des Interventions et Bons de Travail")
        header.setStyleSheet("font-size: 20px; font-weight: bold; color: #2c3e50; margin: 20px;")
        header.setAlignment(Qt.AlignCenter)
        layout.addWidget(header)

        # Message d'information
        info = QLabel("✅ Module d'interventions simplifié - Prêt pour l'intégration complète")
        info.setStyleSheet("font-size: 16px; color: #27ae60; margin: 20px;")
        info.setAlignment(Qt.AlignCenter)
        layout.addWidget(info)

        # Boutons d'action
        actions_frame = QFrame()
        actions_frame.setStyleSheet("QFrame { background-color: #ecf0f1; border-radius: 10px; padding: 20px; }")
        actions_layout = QVBoxLayout(actions_frame)

        actions_title = QLabel("⚡ Actions Disponibles")
        actions_title.setStyleSheet("font-size: 18px; font-weight: bold; color: #34495e; margin-bottom: 15px;")
        actions_layout.addWidget(actions_title)

        # Boutons
        btn_new_intervention = QPushButton("➕ Nouvelle Intervention")
        btn_new_intervention.setStyleSheet("""
            QPushButton { 
                background-color: #3498db; color: white; border: none; 
                padding: 12px 20px; border-radius: 6px; font-weight: bold; font-size: 14px; 
            } 
            QPushButton:hover { background-color: #2980b9; }
        """)
        btn_new_intervention.clicked.connect(lambda: print("✅ Nouvelle intervention créée"))

        btn_new_work_order = QPushButton("📋 Nouveau Bon de Travail")
        btn_new_work_order.setStyleSheet("""
            QPushButton { 
                background-color: #e74c3c; color: white; border: none; 
                padding: 12px 20px; border-radius: 6px; font-weight: bold; font-size: 14px; 
            } 
            QPushButton:hover { background-color: #c0392b; }
        """)
        btn_new_work_order.clicked.connect(lambda: print("✅ Nouveau bon de travail créé"))

        btn_schedule = QPushButton("📅 Planifier Intervention")
        btn_schedule.setStyleSheet("""
            QPushButton { 
                background-color: #27ae60; color: white; border: none; 
                padding: 12px 20px; border-radius: 6px; font-weight: bold; font-size: 14px; 
            } 
            QPushButton:hover { background-color: #229954; }
        """)
        btn_schedule.clicked.connect(lambda: print("✅ Intervention planifiée"))

        actions_layout.addWidget(btn_new_intervention)
        actions_layout.addWidget(btn_new_work_order)
        actions_layout.addWidget(btn_schedule)

        layout.addWidget(actions_frame)

        # Espace flexible
        layout.addStretch()

    def load_data(self):
        """Charge les données (simulé)"""
        print("📊 Chargement des données d'interventions...")

    def refresh_data(self):
        """Actualise les données"""
        print("🔄 Actualisation des données d'interventions...")
        self.load_data()
