#!/usr/bin/env python3
"""
Test des fonctionnalités des équipements
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_equipment_creation():
    """Test de la création d'équipement"""
    print("🎯 TEST DE CRÉATION D'ÉQUIPEMENT")
    
    try:
        app = QApplication(sys.argv)
        from database import Database
        from export.excel_export import ExcelExporter
        from main import OptimizedSotramineApp
        
        db = Database()
        excel_exporter = ExcelExporter(db)
        main_app = OptimizedSotramineApp(db, excel_exporter)
        
        print("✓ Application créée")
        
        if hasattr(main_app, 'create_new_equipment'):
            print("✓ Méthode create_new_equipment disponible")
            return True
        else:
            print("❌ Méthode manquante")
            return False
            
    except Exception as e:
        print(f"❌ Erreur : {str(e)}")
        return False

def test_maintenance_scheduling():
    """Test de la planification de maintenance"""
    print("\n🔧 TEST DE PLANIFICATION DE MAINTENANCE")
    
    try:
        from database import Database
        
        db = Database()
        
        # Créer une tâche de maintenance
        task_id = db.add_task(
            category_id=1,
            title="Maintenance Test",
            description="Test de maintenance",
            due_date="2025-12-31",
            priority="moyenne",
            status="À faire"
        )
        
        print(f"✓ Tâche créée avec ID: {task_id}")
        
        # Créer une intervention
        intervention_id = db.add_maintenance_intervention(
            task_id=task_id,
            details="Test intervention",
            technician_id=None
        )
        
        print(f"✓ Intervention créée avec ID: {intervention_id}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur : {str(e)}")
        return False

def test_import_export():
    """Test de l'import/export Excel"""
    print("\n📊 TEST D'IMPORT/EXPORT EXCEL")
    
    try:
        from database import Database
        from export.excel_export import ExcelExporter
        
        db = Database()
        excel_exporter = ExcelExporter(db)
        
        # Test d'export
        filename = excel_exporter.export_equipment_to_excel()
        print(f"✓ Export réussi : {filename}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur : {str(e)}")
        return False

def main():
    """Fonction principale"""
    print("🎯 TEST COMPLET DES FONCTIONNALITÉS ÉQUIPEMENTS")
    print("=" * 60)
    
    tests = [
        ("Création d'équipement", test_equipment_creation),
        ("Planification de maintenance", test_maintenance_scheduling),
        ("Import/Export Excel", test_import_export)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Test : {test_name}")
        print("-" * 40)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur : {str(e)}")
            results.append((test_name, False))
    
    # Résumé
    print("\n" + "=" * 60)
    print("�� RÉSUMÉ DES TESTS")
    print("=" * 60)
    
    success_count = 0
    for test_name, result in results:
        status = "✅ SUCCÈS" if result else "❌ ÉCHEC"
        print(f"{status} - {test_name}")
        if result:
            success_count += 1
    
    print(f"\n📊 RÉSULTATS : {success_count}/{len(results)} tests réussis")
    
    if success_count == len(results):
        print("🎉 TOUTES LES FONCTIONNALITÉS SONT OPÉRATIONNELLES !")
    else:
        print("⚠️ Certaines fonctionnalités nécessitent des corrections")
    
    return success_count == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
