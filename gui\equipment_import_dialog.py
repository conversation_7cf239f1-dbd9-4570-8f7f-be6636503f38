"""
Dialog d'importation des équipements depuis un fichier CSV
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, 
                             QFileDialog, QTextEdit, QProgressBar, QGroupBox, QTableWidget,
                             QTableWidgetItem, QHeaderView, QMessageBox, QCheckBox)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont
import os

class EquipmentImportThread(QThread):
    """Thread pour l'importation des équipements en arrière-plan"""
    
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    import_completed = pyqtSignal(dict)
    
    def __init__(self, db, file_path, mapping=None):
        super().__init__()
        self.db = db
        self.file_path = file_path
        self.mapping = mapping
        self.is_excel = file_path.lower().endswith(('.xlsx', '.xls'))
    
    def run(self):
        """Exécute l'importation"""
        try:
            self.status_updated.emit("Démarrage de l'importation...")
            self.progress_updated.emit(10)

            # Lancer l'importation selon le type de fichier
            if self.is_excel:
                result = self.db.import_equipment_from_excel(self.file_path, self.mapping)
            else:
                result = self.db.import_equipment_from_csv(self.file_path, self.mapping)
            
            self.progress_updated.emit(100)
            self.status_updated.emit("Importation terminée")
            self.import_completed.emit(result)
            
        except Exception as e:
            error_result = {
                'success': False,
                'imported_count': 0,
                'errors': [f"Erreur critique: {str(e)}"],
                'total_rows': 0
            }
            self.import_completed.emit(error_result)

class EquipmentImportDialog(QDialog):
    """Dialog pour l'importation des équipements"""
    
    def __init__(self, parent=None, db=None):
        super().__init__(parent)
        self.db = db
        self.selected_file = None
        self.import_thread = None
        
        self.setup_ui()
        self.setup_connections()
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        self.setWindowTitle("📥 Importation des Équipements")
        self.setModal(True)
        self.resize(800, 600)

        # Layout principal
        layout = QVBoxLayout(self)

        # Titre
        title_label = QLabel("📥 Importation des Équipements depuis Excel/CSV")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # Section de sélection de fichier
        file_group = QGroupBox("📁 Sélection du Fichier")
        file_layout = QVBoxLayout(file_group)
        
        # Bouton de sélection de fichier
        file_button_layout = QHBoxLayout()
        self.select_file_btn = QPushButton("📂 Sélectionner un fichier Excel/CSV")
        self.select_file_btn.setMinimumHeight(40)
        file_button_layout.addWidget(self.select_file_btn)
        
        self.file_label = QLabel("Aucun fichier sélectionné")
        self.file_label.setStyleSheet("color: #666; font-style: italic;")
        file_button_layout.addWidget(self.file_label)
        
        file_layout.addLayout(file_button_layout)
        layout.addWidget(file_group)
        
        # Section de format
        format_group = QGroupBox("🗂️ Format du Fichier")
        format_layout = QVBoxLayout(format_group)
        
        format_info = QLabel("""
<b>Colonnes supportées (français/anglais) :</b><br>
• <b>nom/name</b> - Nom de l'équipement (obligatoire)<br>
• <b>code</b> - Code unique de l'équipement<br>
• <b>modele/model</b> - Modèle de l'équipement<br>
• <b>numero_serie/serial_number</b> - Numéro de série<br>
• <b>fabricant/manufacturer</b> - Fabricant<br>
• <b>date_achat/purchase_date</b> - Date d'achat<br>
• <b>date_installation/installation_date</b> - Date d'installation<br>
• <b>localisation/location</b> - Localisation<br>
• <b>statut/status</b> - Statut (En service, En maintenance, etc.)<br>
• <b>notes</b> - Notes et commentaires
        """)
        format_info.setWordWrap(True)
        format_layout.addWidget(format_info)
        
        layout.addWidget(format_group)
        
        # Section de prévisualisation
        preview_group = QGroupBox("👁️ Prévisualisation")
        preview_layout = QVBoxLayout(preview_group)
        
        self.preview_table = QTableWidget()
        self.preview_table.setMaximumHeight(150)
        preview_layout.addWidget(self.preview_table)
        
        layout.addWidget(preview_group)
        
        # Section de progression
        progress_group = QGroupBox("📊 Progression")
        progress_layout = QVBoxLayout(progress_group)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        progress_layout.addWidget(self.progress_bar)
        
        self.status_label = QLabel("Prêt pour l'importation")
        progress_layout.addWidget(self.status_label)
        
        layout.addWidget(progress_group)
        
        # Section de résultats
        self.results_group = QGroupBox("📋 Résultats de l'Importation")
        results_layout = QVBoxLayout(self.results_group)
        
        self.results_text = QTextEdit()
        self.results_text.setMaximumHeight(120)
        self.results_text.setReadOnly(True)
        results_layout.addWidget(self.results_text)
        
        self.results_group.setVisible(False)
        layout.addWidget(self.results_group)
        
        # Boutons
        buttons_layout = QHBoxLayout()
        
        self.import_btn = QPushButton("📥 Importer les Équipements")
        self.import_btn.setEnabled(False)
        self.import_btn.setMinimumHeight(40)
        self.import_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
        buttons_layout.addWidget(self.import_btn)
        
        self.close_btn = QPushButton("❌ Fermer")
        self.close_btn.setMinimumHeight(40)
        buttons_layout.addWidget(self.close_btn)
        
        layout.addLayout(buttons_layout)
    
    def setup_connections(self):
        """Configure les connexions des signaux"""
        self.select_file_btn.clicked.connect(self.select_file)
        self.import_btn.clicked.connect(self.start_import)
        self.close_btn.clicked.connect(self.reject)
    
    def select_file(self):
        """Sélectionne un fichier CSV"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Sélectionner un fichier d'équipements",
            "",
            "Fichiers Excel (*.xlsx *.xls);;Fichiers CSV (*.csv);;Tous les fichiers (*)"
        )
        
        if file_path:
            self.selected_file = file_path
            self.file_label.setText(os.path.basename(file_path))
            self.file_label.setStyleSheet("color: #28a745; font-weight: bold;")
            self.import_btn.setEnabled(True)
            
            # Prévisualiser le fichier
            self.preview_csv_file(file_path)
    
    def preview_csv_file(self, file_path):
        """Prévisualise le contenu du fichier CSV ou Excel"""
        try:
            is_excel = file_path.lower().endswith(('.xlsx', '.xls'))

            if is_excel:
                # Prévisualisation Excel
                try:
                    import openpyxl

                    wb = openpyxl.load_workbook(file_path, data_only=True)

                    # Utiliser la première feuille ou chercher une feuille "Équipements"
                    if "Équipements" in wb.sheetnames:
                        ws = wb["Équipements"]
                    elif "Equipment" in wb.sheetnames:
                        ws = wb["Equipment"]
                    else:
                        ws = wb.active

                    # Lire les headers
                    headers = []
                    for cell in ws[1]:
                        if cell.value:
                            headers.append(str(cell.value))
                        else:
                            headers.append('')

                    if headers:
                        self.preview_table.setColumnCount(len(headers))
                        self.preview_table.setHorizontalHeaderLabels(headers)

                        # Lire les 3 premières lignes de données
                        preview_rows = []
                        for row_num, row in enumerate(ws.iter_rows(min_row=2, max_row=4, values_only=True)):
                            if any(row):  # Ignorer les lignes vides
                                preview_rows.append([str(cell) if cell is not None else '' for cell in row])

                        self.preview_table.setRowCount(len(preview_rows))

                        for row_idx, row in enumerate(preview_rows):
                            for col_idx, cell in enumerate(row):
                                if col_idx < len(headers):
                                    item = QTableWidgetItem(str(cell)[:50])  # Limiter à 50 caractères
                                    self.preview_table.setItem(row_idx, col_idx, item)

                        # Compter le nombre total de lignes
                        total_rows = sum(1 for row in ws.iter_rows(min_row=2, values_only=True) if any(row))
                        self.status_label.setText(f"Fichier Excel chargé : {total_rows} ligne(s) de données (Feuille: {ws.title})")

                except ImportError:
                    self.status_label.setText("Module openpyxl non disponible pour la prévisualisation Excel")
                except Exception as e:
                    self.status_label.setText(f"Erreur prévisualisation Excel : {str(e)}")

            else:
                # Prévisualisation CSV
                import csv

                # Essayer différents encodages
                encodings_to_try = ['utf-8', 'utf-8-sig', 'windows-1252', 'iso-8859-1']

                for encoding in encodings_to_try:
                    try:
                        with open(file_path, 'r', encoding=encoding) as csvfile:
                            reader = csv.reader(csvfile)
                            rows = list(reader)
                            break
                    except UnicodeDecodeError:
                        continue
                else:
                    # Si aucun encodage ne fonctionne
                    with open(file_path, 'r', encoding='utf-8', errors='replace') as csvfile:
                        reader = csv.reader(csvfile)
                        rows = list(reader)

                if rows:
                    # Configurer le tableau de prévisualisation
                    headers = rows[0]
                    self.preview_table.setColumnCount(len(headers))
                    self.preview_table.setHorizontalHeaderLabels(headers)

                    # Afficher les 3 premières lignes de données
                    preview_rows = rows[1:4] if len(rows) > 1 else []
                    self.preview_table.setRowCount(len(preview_rows))

                    for row_idx, row in enumerate(preview_rows):
                        for col_idx, cell in enumerate(row):
                            if col_idx < len(headers):
                                item = QTableWidgetItem(str(cell)[:50])  # Limiter à 50 caractères
                                self.preview_table.setItem(row_idx, col_idx, item)

                    self.status_label.setText(f"Fichier CSV chargé : {len(rows)-1} ligne(s) de données")

            # Ajuster les colonnes
            self.preview_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeToContents)

        except Exception as e:
            self.status_label.setText(f"Erreur lors de la prévisualisation : {str(e)}")
    
    def start_import(self):
        """Démarre l'importation"""
        if not self.selected_file or not self.db:
            return
        
        # Désactiver le bouton d'importation
        self.import_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        
        # Créer et démarrer le thread d'importation
        self.import_thread = EquipmentImportThread(self.db, self.selected_file)
        self.import_thread.progress_updated.connect(self.progress_bar.setValue)
        self.import_thread.status_updated.connect(self.status_label.setText)
        self.import_thread.import_completed.connect(self.on_import_completed)
        self.import_thread.start()
    
    def on_import_completed(self, result):
        """Traite les résultats de l'importation"""
        self.progress_bar.setVisible(False)
        self.import_btn.setEnabled(True)
        self.results_group.setVisible(True)
        
        # Afficher les résultats
        results_text = f"""
📊 RÉSULTATS DE L'IMPORTATION :

✅ Succès : {'Oui' if result['success'] else 'Non'}
📈 Équipements importés : {result['imported_count']}
📋 Lignes traitées : {result['total_rows']}
⚠️ Erreurs : {len(result['errors'])}

"""
        
        if result['errors']:
            results_text += "🚨 ERREURS DÉTECTÉES :\n"
            for i, error in enumerate(result['errors'][:10], 1):
                results_text += f"{i}. {error}\n"
            
            if len(result['errors']) > 10:
                results_text += f"... et {len(result['errors']) - 10} autres erreurs\n"
        
        if result['imported_count'] > 0:
            results_text += f"\n🎉 {result['imported_count']} équipements ont été importés avec succès !"
        
        self.results_text.setPlainText(results_text)
        
        # Message de confirmation
        if result['success'] and result['imported_count'] > 0:
            QMessageBox.information(
                self,
                "Importation Réussie",
                f"✅ {result['imported_count']} équipements ont été importés avec succès !\n\n"
                f"Erreurs : {len(result['errors'])}"
            )
        elif result['errors']:
            QMessageBox.warning(
                self,
                "Importation avec Erreurs",
                f"⚠️ Importation terminée avec {len(result['errors'])} erreur(s).\n\n"
                f"Équipements importés : {result['imported_count']}\n"
                f"Consultez les détails ci-dessous."
            )
        
        # Mettre à jour le statut
        if result['imported_count'] > 0:
            self.status_label.setText(f"✅ Importation réussie : {result['imported_count']} équipements")
        else:
            self.status_label.setText("❌ Aucun équipement importé")
    
    def closeEvent(self, event):
        """Gère la fermeture du dialog"""
        if self.import_thread and self.import_thread.isRunning():
            self.import_thread.quit()
            self.import_thread.wait()
        event.accept()

def show_equipment_import_dialog(parent=None, db=None):
    """Affiche le dialog d'importation des équipements"""
    dialog = EquipmentImportDialog(parent, db)
    return dialog.exec_()

if __name__ == "__main__":
    import sys
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    # Test du dialog
    from database import Database
    db = Database()
    
    dialog = EquipmentImportDialog(None, db)
    dialog.show()
    
    sys.exit(app.exec_())
