"""
Dialog de réinitialisation de l'application avec protection par mot de passe
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, 
                             QLineEdit, QTextEdit, QGroupBox, QCheckBox, QMessageBox,
                             QProgressBar, QFrame)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPixmap, QIcon
import os
import shutil
import sqlite3

class ResetThread(QThread):
    """Thread pour la réinitialisation en arrière-plan"""
    
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    reset_completed = pyqtSignal(bool, str)
    
    def __init__(self, db, reset_options):
        super().__init__()
        self.db = db
        self.reset_options = reset_options
    
    def run(self):
        """Exécute la réinitialisation"""
        try:
            self.status_updated.emit("Démarrage de la réinitialisation...")
            self.progress_updated.emit(10)
            
            # Fermer la connexion à la base de données
            if self.db:
                self.db.close()
            
            self.progress_updated.emit(20)
            self.status_updated.emit("Sauvegarde des données critiques...")
            
            # Sauvegarder les données critiques si demandé
            backup_created = False
            if self.reset_options.get('create_backup', False):
                backup_created = self._create_backup()
            
            self.progress_updated.emit(40)
            self.status_updated.emit("Suppression des données...")
            
            # Réinitialiser selon les options
            if self.reset_options.get('reset_database', False):
                self._reset_database()
            
            self.progress_updated.emit(60)
            
            if self.reset_options.get('reset_exports', False):
                self._reset_exports()
            
            self.progress_updated.emit(80)
            
            if self.reset_options.get('reset_config', False):
                self._reset_config()
            
            self.progress_updated.emit(100)
            self.status_updated.emit("Réinitialisation terminée")
            
            message = "Réinitialisation terminée avec succès"
            if backup_created:
                message += "\nSauvegarde créée : backup_sotramine.db"
            
            self.reset_completed.emit(True, message)
            
        except Exception as e:
            self.reset_completed.emit(False, f"Erreur lors de la réinitialisation : {str(e)}")
    
    def _create_backup(self):
        """Crée une sauvegarde de la base de données"""
        try:
            if os.path.exists('sotramine.db'):
                backup_name = 'backup_sotramine.db'
                shutil.copy2('sotramine.db', backup_name)
                return True
        except Exception as e:
            print(f"Erreur sauvegarde : {str(e)}")
        return False
    
    def _reset_database(self):
        """Réinitialise la base de données"""
        try:
            if os.path.exists('sotramine.db'):
                os.remove('sotramine.db')
        except Exception as e:
            print(f"Erreur suppression DB : {str(e)}")
    
    def _reset_exports(self):
        """Supprime les fichiers d'export"""
        try:
            if os.path.exists('export'):
                shutil.rmtree('export')
        except Exception as e:
            print(f"Erreur suppression exports : {str(e)}")
    
    def _reset_config(self):
        """Réinitialise les fichiers de configuration"""
        try:
            config_files = ['config.ini', 'settings.json', 'user_preferences.json']
            for config_file in config_files:
                if os.path.exists(config_file):
                    os.remove(config_file)
        except Exception as e:
            print(f"Erreur suppression config : {str(e)}")

class ResetDialog(QDialog):
    """Dialog de réinitialisation de l'application"""
    
    CORRECT_PASSWORD = "sotramine"
    
    def __init__(self, parent=None, db=None):
        super().__init__(parent)
        self.db = db
        self.reset_thread = None
        self.password_verified = False
        
        self.setup_ui()
        self.setup_connections()
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        self.setWindowTitle("🔄 Réinitialisation de l'Application")
        self.setModal(True)
        self.resize(600, 700)
        
        # Layout principal
        layout = QVBoxLayout(self)
        
        # Titre avec icône d'avertissement
        title_layout = QHBoxLayout()
        
        title_label = QLabel("⚠️ Réinitialisation de SOTRAMINE PHOSPHATE")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #d32f2f; margin: 10px;")
        
        title_layout.addWidget(title_label)
        layout.addLayout(title_layout)
        
        # Avertissement
        warning_group = QGroupBox("⚠️ ATTENTION - OPÉRATION IRRÉVERSIBLE")
        warning_layout = QVBoxLayout(warning_group)
        warning_group.setStyleSheet("QGroupBox { color: #d32f2f; font-weight: bold; }")
        
        warning_text = QLabel("""
<b>Cette opération va supprimer définitivement des données !</b><br><br>
• <b>Base de données</b> : Toutes les tâches, équipements, personnel, etc.<br>
• <b>Fichiers d'export</b> : Tous les exports Excel/CSV générés<br>
• <b>Configuration</b> : Paramètres et préférences utilisateur<br><br>
<span style="color: #d32f2f;"><b>⚠️ CETTE ACTION NE PEUT PAS ÊTRE ANNULÉE !</b></span>
        """)
        warning_text.setWordWrap(True)
        warning_text.setStyleSheet("color: #333; background-color: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px;")
        warning_layout.addWidget(warning_text)
        
        layout.addWidget(warning_group)
        
        # Section mot de passe
        password_group = QGroupBox("🔐 Authentification Requise")
        password_layout = QVBoxLayout(password_group)
        
        password_info = QLabel("Entrez le mot de passe administrateur pour continuer :")
        password_info.setStyleSheet("font-weight: bold; margin-bottom: 10px;")
        password_layout.addWidget(password_info)
        
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setPlaceholderText("Mot de passe administrateur")
        self.password_input.setMinimumHeight(35)
        self.password_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #ddd;
                border-radius: 5px;
                padding: 8px;
                font-size: 12pt;
            }
            QLineEdit:focus {
                border-color: #4CAF50;
            }
        """)
        password_layout.addWidget(self.password_input)
        
        self.verify_btn = QPushButton("🔓 Vérifier le Mot de Passe")
        self.verify_btn.setMinimumHeight(40)
        self.verify_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
        """)
        password_layout.addWidget(self.verify_btn)
        
        self.password_status = QLabel("")
        self.password_status.setAlignment(Qt.AlignCenter)
        password_layout.addWidget(self.password_status)
        
        layout.addWidget(password_group)
        
        # Section options de réinitialisation
        self.options_group = QGroupBox("🔄 Options de Réinitialisation")
        options_layout = QVBoxLayout(self.options_group)
        self.options_group.setEnabled(False)
        
        self.reset_database_cb = QCheckBox("🗄️ Réinitialiser la base de données")
        self.reset_database_cb.setToolTip("Supprime toutes les données : tâches, équipements, personnel, etc.")
        self.reset_database_cb.setChecked(True)
        options_layout.addWidget(self.reset_database_cb)
        
        self.reset_exports_cb = QCheckBox("📊 Supprimer les fichiers d'export")
        self.reset_exports_cb.setToolTip("Supprime tous les fichiers Excel/CSV générés")
        self.reset_exports_cb.setChecked(True)
        options_layout.addWidget(self.reset_exports_cb)
        
        self.reset_config_cb = QCheckBox("⚙️ Réinitialiser la configuration")
        self.reset_config_cb.setToolTip("Supprime les paramètres et préférences utilisateur")
        self.reset_config_cb.setChecked(False)
        options_layout.addWidget(self.reset_config_cb)
        
        self.create_backup_cb = QCheckBox("💾 Créer une sauvegarde avant réinitialisation")
        self.create_backup_cb.setToolTip("Crée une copie de la base de données actuelle")
        self.create_backup_cb.setChecked(True)
        options_layout.addWidget(self.create_backup_cb)
        
        layout.addWidget(self.options_group)
        
        # Section de progression
        self.progress_group = QGroupBox("📊 Progression")
        progress_layout = QVBoxLayout(self.progress_group)
        self.progress_group.setVisible(False)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimumHeight(25)
        progress_layout.addWidget(self.progress_bar)
        
        self.status_label = QLabel("Prêt pour la réinitialisation")
        self.status_label.setAlignment(Qt.AlignCenter)
        progress_layout.addWidget(self.status_label)
        
        layout.addWidget(self.progress_group)
        
        # Boutons
        buttons_layout = QHBoxLayout()
        
        self.reset_btn = QPushButton("🔄 RÉINITIALISER L'APPLICATION")
        self.reset_btn.setEnabled(False)
        self.reset_btn.setMinimumHeight(45)
        self.reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12pt;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
            QPushButton:disabled {
                background-color: #bdbdbd;
            }
        """)
        buttons_layout.addWidget(self.reset_btn)
        
        self.cancel_btn = QPushButton("❌ Annuler")
        self.cancel_btn.setMinimumHeight(45)
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #757575;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #616161;
            }
        """)
        buttons_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(buttons_layout)
    
    def setup_connections(self):
        """Configure les connexions des signaux"""
        self.verify_btn.clicked.connect(self.verify_password)
        self.password_input.returnPressed.connect(self.verify_password)
        self.reset_btn.clicked.connect(self.confirm_reset)
        self.cancel_btn.clicked.connect(self.reject)
    
    def verify_password(self):
        """Vérifie le mot de passe"""
        entered_password = self.password_input.text()
        
        if entered_password == self.CORRECT_PASSWORD:
            self.password_verified = True
            self.password_status.setText("✅ Mot de passe correct")
            self.password_status.setStyleSheet("color: #4CAF50; font-weight: bold;")
            self.options_group.setEnabled(True)
            self.reset_btn.setEnabled(True)
            self.password_input.setEnabled(False)
            self.verify_btn.setEnabled(False)
        else:
            self.password_status.setText("❌ Mot de passe incorrect")
            self.password_status.setStyleSheet("color: #f44336; font-weight: bold;")
            self.password_input.clear()
            self.password_input.setFocus()
    
    def confirm_reset(self):
        """Demande confirmation avant réinitialisation"""
        if not self.password_verified:
            return
        
        # Compter les options sélectionnées
        selected_options = []
        if self.reset_database_cb.isChecked():
            selected_options.append("Base de données")
        if self.reset_exports_cb.isChecked():
            selected_options.append("Fichiers d'export")
        if self.reset_config_cb.isChecked():
            selected_options.append("Configuration")
        
        if not selected_options:
            QMessageBox.warning(self, "Aucune Option", 
                              "Veuillez sélectionner au moins une option de réinitialisation.")
            return
        
        # Confirmation finale
        options_text = "\n• ".join(selected_options)
        backup_text = "\n\n💾 Une sauvegarde sera créée." if self.create_backup_cb.isChecked() else ""
        
        reply = QMessageBox.question(
            self,
            "Confirmation Finale",
            f"⚠️ DERNIÈRE CONFIRMATION ⚠️\n\n"
            f"Vous êtes sur le point de réinitialiser :\n• {options_text}"
            f"{backup_text}\n\n"
            f"🚨 CETTE ACTION EST DÉFINITIVE ET IRRÉVERSIBLE !\n\n"
            f"Êtes-vous absolument certain de vouloir continuer ?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.start_reset()
    
    def start_reset(self):
        """Démarre la réinitialisation"""
        # Désactiver les contrôles
        self.reset_btn.setEnabled(False)
        self.options_group.setEnabled(False)
        self.progress_group.setVisible(True)
        
        # Préparer les options
        reset_options = {
            'reset_database': self.reset_database_cb.isChecked(),
            'reset_exports': self.reset_exports_cb.isChecked(),
            'reset_config': self.reset_config_cb.isChecked(),
            'create_backup': self.create_backup_cb.isChecked()
        }
        
        # Créer et démarrer le thread de réinitialisation
        self.reset_thread = ResetThread(self.db, reset_options)
        self.reset_thread.progress_updated.connect(self.progress_bar.setValue)
        self.reset_thread.status_updated.connect(self.status_label.setText)
        self.reset_thread.reset_completed.connect(self.on_reset_completed)
        self.reset_thread.start()
    
    def on_reset_completed(self, success, message):
        """Traite la fin de la réinitialisation"""
        if success:
            QMessageBox.information(
                self,
                "Réinitialisation Terminée",
                f"✅ {message}\n\n"
                f"L'application va maintenant redémarrer.\n"
                f"Toutes les fenêtres vont se fermer."
            )
            
            # Fermer l'application après un délai
            QTimer.singleShot(2000, self.close_application)
            
        else:
            QMessageBox.critical(
                self,
                "Erreur de Réinitialisation",
                f"❌ {message}\n\n"
                f"La réinitialisation n'a pas pu être terminée complètement."
            )
            
            # Réactiver les contrôles
            self.reset_btn.setEnabled(True)
            self.options_group.setEnabled(True)
    
    def close_application(self):
        """Ferme l'application"""
        self.accept()
        if self.parent():
            self.parent().close()
    
    def closeEvent(self, event):
        """Gère la fermeture du dialog"""
        if self.reset_thread and self.reset_thread.isRunning():
            reply = QMessageBox.question(
                self,
                "Réinitialisation en Cours",
                "Une réinitialisation est en cours.\n"
                "Êtes-vous sûr de vouloir annuler ?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                self.reset_thread.quit()
                self.reset_thread.wait()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()

def show_reset_dialog(parent=None, db=None):
    """Affiche le dialog de réinitialisation"""
    dialog = ResetDialog(parent, db)
    return dialog.exec_()

if __name__ == "__main__":
    import sys
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    dialog = ResetDialog()
    dialog.show()
    
    sys.exit(app.exec_())
