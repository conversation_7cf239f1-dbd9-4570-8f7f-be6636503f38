#!/usr/bin/env python3
"""
Test Simple du Menu de Maintenance
Vérifie l'import et la création des composants
"""

import sys
import os

# Ajouter le répertoire parent au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Teste les imports des modules de maintenance"""
    print("🧪 Test des imports des modules de maintenance")
    print("=" * 50)
    
    try:
        print("📦 Import de maintenance_menu...")
        from gui.maintenance_menu import MaintenanceMenu
        print("✅ maintenance_menu importé avec succès")
        
        print("📦 Import de maintenance_indicators...")
        from gui.maintenance_indicators import MaintenanceIndicators
        print("✅ maintenance_indicators importé avec succès")
        
        print("📦 Import de work_orders_manager...")
        from gui.work_orders_manager import WorkOrdersManager
        print("✅ work_orders_manager importé avec succès")
        
        print("📦 Import de interventions_management...")
        from gui.interventions_management import InterventionsManagement
        print("✅ interventions_management importé avec succès")
        
        print("📦 Import de preventive_maintenance...")
        from gui.preventive_maintenance import PreventiveMaintenance
        print("✅ preventive_maintenance importé avec succès")
        
        print("📦 Import de maintenance_history...")
        from gui.maintenance_history import MaintenanceHistoryView
        print("✅ maintenance_history importé avec succès")
        
        print("📦 Import de maintenance_checklist...")
        from gui.maintenance_checklist import MaintenanceChecklistView
        print("✅ maintenance_checklist importé avec succès")
        
        print("\n🎯 Tous les modules de maintenance sont importés avec succès !")
        return True
        
    except ImportError as e:
        print(f"❌ Erreur d'import: {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        return False

def test_database_connection():
    """Teste la connexion à la base de données"""
    print("\n🔌 Test de la connexion à la base de données")
    print("=" * 50)
    
    try:
        print("📦 Import de database...")
        from database import Database
        print("✅ database importé avec succès")
        
        print("🔌 Création d'une base de données en mémoire...")
        db = Database(":memory:")
        print("✅ Base de données créée avec succès")
        
        return db
        
    except ImportError as e:
        print(f"❌ Erreur d'import de database: {e}")
        return None
    except Exception as e:
        print(f"❌ Erreur de création de la base: {e}")
        return None

def test_component_creation(db):
    """Teste la création des composants"""
    print("\n🏗️ Test de la création des composants")
    print("=" * 50)
    
    try:
        print("🎛️ Création du menu de maintenance...")
        from gui.maintenance_menu import MaintenanceMenu
        maintenance_menu = MaintenanceMenu(db, None)
        print("✅ Menu de maintenance créé avec succès")
        
        print("📈 Création des indicateurs...")
        from gui.maintenance_indicators import MaintenanceIndicators
        indicators = MaintenanceIndicators(db, None)
        print("✅ Indicateurs créés avec succès")
        
        print("📋 Création du gestionnaire de bons de travail...")
        from gui.work_orders_manager import WorkOrdersManager
        work_orders = WorkOrdersManager(db, None)
        print("✅ Gestionnaire de bons de travail créé avec succès")
        
        print("\n🎯 Tous les composants sont créés avec succès !")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la création des composants: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Test Simple du Menu de Maintenance")
    print("=" * 60)
    
    # Test des imports
    if not test_imports():
        print("\n❌ Échec des tests d'import")
        sys.exit(1)
    
    # Test de la base de données
    db = test_database_connection()
    if not db:
        print("\n❌ Échec de la connexion à la base de données")
        sys.exit(1)
    
    # Test de création des composants
    if not test_component_creation(db):
        print("\n❌ Échec de la création des composants")
        sys.exit(1)
    
    print("\n🎉 TOUS LES TESTS SONT RÉUSSIS !")
    print("✅ Le menu de maintenance est prêt à être utilisé")
    print("🚀 Vous pouvez maintenant lancer l'application principale")
