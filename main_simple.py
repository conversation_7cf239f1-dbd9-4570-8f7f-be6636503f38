#!/usr/bin/env python3
"""
Application Todo List avec Menu de Maintenance Complet
Version simplifiée et fonctionnelle
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt

# Ajouter le répertoire parent au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """Fonction principale de l'application"""
    try:
        # Créer l'application PyQt5
        app = QApplication(sys.argv)
        app.setApplicationName("Todo List - Menu de Maintenance Complet")
        app.setApplicationVersion("1.0")
        
        # Créer la fenêtre principale
        main_window = QMainWindow()
        main_window.setWindowTitle("Todo List - Menu de Maintenance Complet")
        main_window.setGeometry(100, 100, 1400, 900)
        
        # Widget central
        central_widget = QWidget()
        main_window.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Importer et créer le menu de maintenance
        print("🔧 Import du menu de maintenance...")
        from gui.maintenance_menu import MaintenanceMenu
        
        print("🗄️ Création de la base de données...")
        from database import Database
        db = Database(":memory:")  # Base en mémoire pour le test
        
        print("🎛️ Création du menu de maintenance...")
        maintenance_menu = MaintenanceMenu(db, central_widget)
        layout.addWidget(maintenance_menu)
        
        # Afficher la fenêtre
        main_window.show()
        print("✅ Application lancée avec succès !")
        print("🎯 Menu de maintenance accessible dans l'onglet principal")
        
        # Lancer l'application
        return app.exec_()
        
    except ImportError as e:
        print(f"❌ Erreur d'import: {e}")
        print("🔧 Vérifiez que tous les modules sont présents")
        return 1
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    print("🚀 Lancement de l'application Todo List avec Menu de Maintenance")
    print("=" * 70)
    
    result = main()
    sys.exit(result)
