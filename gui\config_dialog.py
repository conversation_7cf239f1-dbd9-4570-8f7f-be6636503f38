"""
Module contenant la boîte de dialogue de configuration de l'application SOTRAMINE PHOSPHATE.
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                            QTabWidget, QWidget, QFormLayout, QLineEdit, QComboBox,
                            QCheckBox, QSpinBox, QColorDialog, QFileDialog, QGroupBox,
                            QMessageBox, QListWidget, QListWidgetItem, QToolButton)
from PyQt5.QtCore import Qt, QSettings
from PyQt5.QtGui import QIcon, QColor, QPixmap

class ConfigDialog(QDialog):
    """Boîte de dialogue de configuration de l'application"""
    def __init__(self, parent, db):
        super().__init__(parent)
        self.db = db
        self.settings = QSettings("SOTRAMINE", "PHOSPHATE")
        self.setup_ui()
        self.load_settings()

    def setup_ui(self):
        """Configure l'interface utilisateur"""
        self.setWindowTitle("Configuration de l'application")
        self.setMinimumSize(800, 600)
        self.setWindowFlags(self.windowFlags() | Qt.WindowMaximizeButtonHint)

        # Layout principal
        main_layout = QVBoxLayout(self)

        # Onglets de configuration
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)

        # Onglet Général
        general_tab = QWidget()
        general_layout = QVBoxLayout(general_tab)

        # Groupe Apparence
        appearance_group = QGroupBox("Apparence")
        appearance_layout = QFormLayout(appearance_group)

        # Thème
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["Clair", "Sombre", "Système"])
        appearance_layout.addRow("Thème:", self.theme_combo)

        # Couleur principale
        self.primary_color_btn = QPushButton()
        self.primary_color_btn.setFixedSize(30, 30)
        self.primary_color_btn.clicked.connect(self.choose_primary_color)
        appearance_layout.addRow("Couleur principale:", self.primary_color_btn)

        # Logo
        logo_layout = QHBoxLayout()
        self.logo_path_edit = QLineEdit()
        self.logo_path_edit.setReadOnly(True)
        logo_browse_btn = QPushButton("Parcourir...")
        logo_browse_btn.clicked.connect(self.choose_logo)
        logo_layout.addWidget(self.logo_path_edit)
        logo_layout.addWidget(logo_browse_btn)
        appearance_layout.addRow("Logo:", logo_layout)

        general_layout.addWidget(appearance_group)

        # Groupe Comportement
        behavior_group = QGroupBox("Comportement")
        behavior_layout = QFormLayout(behavior_group)

        # Démarrer en plein écran
        self.fullscreen_check = QCheckBox()
        behavior_layout.addRow("Démarrer en plein écran:", self.fullscreen_check)

        # Afficher l'écran d'accueil au démarrage
        self.show_welcome_check = QCheckBox()
        behavior_layout.addRow("Afficher l'écran d'accueil au démarrage:", self.show_welcome_check)

        # Délai de l'écran de démarrage
        self.splash_delay_spin = QSpinBox()
        self.splash_delay_spin.setRange(0, 5000)
        self.splash_delay_spin.setSingleStep(100)
        self.splash_delay_spin.setSuffix(" ms")
        behavior_layout.addRow("Délai de l'écran de démarrage:", self.splash_delay_spin)

        general_layout.addWidget(behavior_group)

        # Ajouter l'onglet Général
        self.tab_widget.addTab(general_tab, "Général")

        # Onglet Base de données
        database_tab = QWidget()
        database_layout = QVBoxLayout(database_tab)

        # Groupe Connexion
        connection_group = QGroupBox("Connexion à la base de données")
        connection_layout = QFormLayout(connection_group)

        # Chemin de la base de données
        db_path_layout = QHBoxLayout()
        self.db_path_edit = QLineEdit()
        self.db_path_edit.setReadOnly(True)
        db_browse_btn = QPushButton("Parcourir...")
        db_browse_btn.clicked.connect(self.choose_db_path)
        db_path_layout.addWidget(self.db_path_edit)
        db_path_layout.addWidget(db_browse_btn)
        connection_layout.addRow("Chemin de la base de données:", db_path_layout)

        database_layout.addWidget(connection_group)

        # Groupe Maintenance
        maintenance_group = QGroupBox("Maintenance")
        maintenance_layout = QVBoxLayout(maintenance_group)

        # Boutons de maintenance
        backup_btn = QPushButton("Sauvegarder la base de données")
        backup_btn.clicked.connect(self.backup_database)
        maintenance_layout.addWidget(backup_btn)

        restore_btn = QPushButton("Restaurer une sauvegarde")
        restore_btn.clicked.connect(self.restore_database)
        maintenance_layout.addWidget(restore_btn)

        optimize_btn = QPushButton("Optimiser la base de données")
        optimize_btn.clicked.connect(self.optimize_database)
        maintenance_layout.addWidget(optimize_btn)

        # Séparateur
        separator = QLabel()
        separator.setFixedHeight(1)
        separator.setStyleSheet("background-color: #ddd; margin: 10px 0;")
        maintenance_layout.addWidget(separator)

        # Section réinitialisation
        reset_info = QLabel("⚠️ <b>Réinitialisation de l'Application</b><br>"
                           "Supprime toutes les données et remet l'application à zéro.<br>"
                           "<span style='color: #d32f2f;'><b>Cette opération est irréversible !</b></span>")
        reset_info.setWordWrap(True)
        reset_info.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 5px;
                padding: 10px;
                margin: 5px 0;
            }
        """)
        maintenance_layout.addWidget(reset_info)

        # Bouton de réinitialisation
        reset_btn = QPushButton("🔄 Réinitialiser l'Application")
        reset_btn.setMinimumHeight(40)
        reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
            QPushButton:pressed {
                background-color: #b71c1c;
            }
        """)
        reset_btn.clicked.connect(self.show_reset_dialog)
        maintenance_layout.addWidget(reset_btn)

        database_layout.addWidget(maintenance_group)

        # Ajouter l'onglet Base de données
        self.tab_widget.addTab(database_tab, "Base de données")

        # Boutons de dialogue
        button_layout = QHBoxLayout()

        save_btn = QPushButton("Enregistrer")
        save_btn.clicked.connect(self.save_settings)
        button_layout.addWidget(save_btn)

        cancel_btn = QPushButton("Annuler")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)

        main_layout.addLayout(button_layout)

    def load_settings(self):
        """Charge les paramètres depuis QSettings"""
        # Apparence
        theme = self.settings.value("theme", "Clair")
        self.theme_combo.setCurrentText(theme)

        primary_color = self.settings.value("primary_color", "#3498db")
        self.primary_color_btn.setStyleSheet(f"background-color: {primary_color};")
        self.primary_color_btn.setProperty("color", primary_color)

        logo_path = self.settings.value("logo_path", "resources/images/logo.png")
        self.logo_path_edit.setText(logo_path)

        # Comportement
        fullscreen = self.settings.value("fullscreen", False, type=bool)
        self.fullscreen_check.setChecked(fullscreen)

        show_welcome = self.settings.value("show_welcome", True, type=bool)
        self.show_welcome_check.setChecked(show_welcome)

        splash_delay = self.settings.value("splash_delay", 1500, type=int)
        self.splash_delay_spin.setValue(splash_delay)

        # Base de données
        db_path = self.settings.value("db_path", "database.db")
        self.db_path_edit.setText(db_path)

    def save_settings(self):
        """Enregistre les paramètres dans QSettings"""
        # Apparence
        self.settings.setValue("theme", self.theme_combo.currentText())
        self.settings.setValue("primary_color", self.primary_color_btn.property("color"))
        self.settings.setValue("logo_path", self.logo_path_edit.text())

        # Comportement
        self.settings.setValue("fullscreen", self.fullscreen_check.isChecked())
        self.settings.setValue("show_welcome", self.show_welcome_check.isChecked())
        self.settings.setValue("splash_delay", self.splash_delay_spin.value())

        # Base de données
        self.settings.setValue("db_path", self.db_path_edit.text())

        QMessageBox.information(self, "Configuration", "Les paramètres ont été enregistrés avec succès.\nCertains changements prendront effet au prochain démarrage de l'application.")
        self.accept()

    def choose_primary_color(self):
        """Ouvre un sélecteur de couleur pour choisir la couleur principale"""
        current_color = QColor(self.primary_color_btn.property("color"))
        color = QColorDialog.getColor(current_color, self, "Choisir la couleur principale")

        if color.isValid():
            self.primary_color_btn.setStyleSheet(f"background-color: {color.name()};")
            self.primary_color_btn.setProperty("color", color.name())

    def choose_logo(self):
        """Ouvre un sélecteur de fichier pour choisir le logo"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Choisir un logo", "", "Images (*.png *.jpg *.jpeg *.bmp)"
        )

        if file_path:
            self.logo_path_edit.setText(file_path)

    def choose_db_path(self):
        """Ouvre un sélecteur de fichier pour choisir le chemin de la base de données"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Choisir une base de données", "", "Bases de données SQLite (*.db)"
        )

        if file_path:
            self.db_path_edit.setText(file_path)

    def backup_database(self):
        """Sauvegarde la base de données"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "Sauvegarder la base de données", "", "Bases de données SQLite (*.db)"
        )

        if file_path:
            try:
                import shutil
                shutil.copy2(self.db_path_edit.text(), file_path)
                QMessageBox.information(self, "Sauvegarde", "La base de données a été sauvegardée avec succès.")
            except Exception as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de la sauvegarde : {str(e)}")

    def restore_database(self):
        """Restaure une sauvegarde de la base de données"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Restaurer une sauvegarde", "", "Bases de données SQLite (*.db)"
        )

        if file_path:
            reply = QMessageBox.warning(
                self,
                "Restauration",
                "La restauration remplacera la base de données actuelle. Cette opération est irréversible.\n\nVoulez-vous continuer ?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                try:
                    import shutil
                    shutil.copy2(file_path, self.db_path_edit.text())
                    QMessageBox.information(self, "Restauration", "La base de données a été restaurée avec succès.\nVeuillez redémarrer l'application pour appliquer les changements.")
                except Exception as e:
                    QMessageBox.critical(self, "Erreur", f"Erreur lors de la restauration : {str(e)}")

    def optimize_database(self):
        """Optimise la base de données"""
        try:
            self.db.cursor.execute("VACUUM")
            self.db.conn.commit()
            QMessageBox.information(self, "Optimisation", "La base de données a été optimisée avec succès.")
        except Exception as e:
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'optimisation : {str(e)}")

    def add_theme_button(self, callback):
        """Ajoute un bouton pour changer de thème"""
        # Créer un groupe pour les thèmes
        theme_group = QGroupBox("Thèmes")
        theme_layout = QVBoxLayout(theme_group)

        # Bouton pour changer de thème
        theme_btn = QPushButton("Changer de thème")
        theme_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        theme_btn.clicked.connect(callback)
        theme_layout.addWidget(theme_btn)

        # Description
        description = QLabel("Personnalisez l'apparence de l'application en choisissant parmi différents thèmes.")
        description.setWordWrap(True)
        theme_layout.addWidget(description)

        # Ajouter le groupe au premier onglet (Général)
        general_tab = self.tab_widget.widget(0)
        general_layout = general_tab.layout()
        general_layout.addWidget(theme_group)

    def show_reset_dialog(self):
        """Affiche le dialog de réinitialisation de l'application"""
        try:
            from gui.reset_dialog import ResetDialog

            # Créer et afficher le dialog de réinitialisation
            reset_dialog = ResetDialog(self, self.db)
            result = reset_dialog.exec_()

            # Si la réinitialisation a été effectuée, fermer ce dialog aussi
            if result == QDialog.Accepted:
                # La réinitialisation a été effectuée, l'application va redémarrer
                self.accept()

        except Exception as e:
            QMessageBox.critical(
                self,
                "Erreur",
                f"Impossible d'ouvrir le dialog de réinitialisation :\n{str(e)}"
            )
