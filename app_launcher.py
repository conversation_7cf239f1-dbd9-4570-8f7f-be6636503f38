#!/usr/bin/env python3
"""
Lanceur d'application SOTRAMINE PHOSPHATE
Version simplifiée qui fonctionne
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QLabel, QPushButton, QFrame, QStackedWidget,
                             QSplitter, QMessageBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QIcon

class SotramineApp(QMainWindow):
    """Application principale SOTRAMINE PHOSPHATE"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_database()
    
    def setup_database(self):
        """Initialise la base de données"""
        try:
            from database import Database
            self.db = Database()
            print("✓ Base de données initialisée")
        except Exception as e:
            print(f"❌ Erreur base de données : {str(e)}")
            self.db = None
    
    def setup_ui(self):
        """Configure l'interface utilisateur"""
        self.setWindowTitle("SOTRAMINE PHOSPHATE - Système de Gestion Intégré")
        self.setGeometry(100, 100, 1400, 900)
        
        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Layout principal
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Splitter principal
        splitter = QSplitter(Qt.Horizontal)
        
        # Menu latéral
        sidebar = self.create_sidebar()
        splitter.addWidget(sidebar)
        
        # Zone de contenu principal
        self.content_area = self.create_content_area()
        splitter.addWidget(self.content_area)
        
        # Proportions du splitter
        splitter.setSizes([300, 1100])
        splitter.setCollapsible(0, False)
        splitter.setCollapsible(1, False)
        
        main_layout.addWidget(splitter)
        
        # Afficher l'accueil par défaut
        self.show_home()
    
    def create_sidebar(self):
        """Crée le menu latéral"""
        sidebar = QFrame()
        sidebar.setStyleSheet("""
            QFrame {
                background-color: #2c3e50;
                border-right: 1px solid #34495e;
            }
        """)
        sidebar.setFixedWidth(300)
        
        layout = QVBoxLayout(sidebar)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # Logo/Titre
        header = QFrame()
        header.setStyleSheet("""
            QFrame {
                background-color: #34495e;
                padding: 20px;
            }
        """)
        header.setFixedHeight(100)
        
        header_layout = QVBoxLayout(header)
        header_layout.setAlignment(Qt.AlignCenter)
        
        title_label = QLabel("SOTRAMINE")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 20px;
                font-weight: bold;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label)
        
        subtitle_label = QLabel("PHOSPHATE")
        subtitle_label.setStyleSheet("""
            QLabel {
                color: #3498db;
                font-size: 16px;
                font-weight: bold;
            }
        """)
        subtitle_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(subtitle_label)
        
        layout.addWidget(header)
        
        # Menu sections
        self.create_menu_sections(layout)
        
        layout.addStretch()
        
        return sidebar
    
    def create_menu_sections(self, layout):
        """Crée les sections du menu"""
        # Section PRODUCTION
        production_header = QLabel("📊 PRODUCTION")
        production_header.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                padding: 12px 16px 8px 16px;
                color: #e74c3c;
                background-color: transparent;
                border-bottom: 2px solid #e74c3c;
                margin: 8px 0 4px 0;
            }
        """)
        layout.addWidget(production_header)
        
        production_buttons = [
            ("🏠 Tableau de Bord", self.show_home),
            ("📋 Tâches", self.show_tasks),
            ("📄 Rapports", self.show_reports)
        ]
        
        for text, action in production_buttons:
            btn = self.create_menu_button(text, action)
            layout.addWidget(btn)
        
        # Espacement
        layout.addSpacing(16)
        
        # Section MAINTENANCE
        maintenance_header = QLabel("🔧 MAINTENANCE")
        maintenance_header.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                padding: 12px 16px 8px 16px;
                color: #f39c12;
                background-color: transparent;
                border-bottom: 2px solid #f39c12;
                margin: 8px 0 4px 0;
            }
        """)
        layout.addWidget(maintenance_header)
        
        maintenance_buttons = [
            ("🔌 Équipements", self.show_equipment),
            ("🔧 Pièces de Rechange", self.show_spare_parts)
        ]
        
        for text, action in maintenance_buttons:
            btn = self.create_menu_button(text, action)
            layout.addWidget(btn)
        
        # Espacement
        layout.addSpacing(16)
        
        # Section PERSONNEL
        personnel_header = QLabel("👥 PERSONNEL")
        personnel_header.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                padding: 12px 16px 8px 16px;
                color: #27ae60;
                background-color: transparent;
                border-bottom: 2px solid #27ae60;
                margin: 8px 0 4px 0;
            }
        """)
        layout.addWidget(personnel_header)
        
        personnel_buttons = [
            ("👤 Gestion Personnel", self.show_personnel),
            ("📊 Pointage", self.show_attendance)
        ]
        
        for text, action in personnel_buttons:
            btn = self.create_menu_button(text, action)
            layout.addWidget(btn)
    
    def create_menu_button(self, text, action):
        """Crée un bouton de menu"""
        btn = QPushButton(text)
        btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #ecf0f1;
                border: none;
                padding: 12px 20px;
                text-align: left;
                font-size: 14px;
                margin-left: 16px;
            }
            QPushButton:hover {
                background-color: #34495e;
                color: white;
            }
            QPushButton:pressed {
                background-color: #3498db;
            }
        """)
        btn.clicked.connect(action)
        return btn
    
    def create_content_area(self):
        """Crée la zone de contenu principal"""
        content = QStackedWidget()
        
        # Page d'accueil
        self.home_page = self.create_home_page()
        content.addWidget(self.home_page)
        
        return content
    
    def create_home_page(self):
        """Crée la page d'accueil"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(40, 40, 40, 40)
        layout.setSpacing(30)
        
        # Titre principal
        title = QLabel("Bienvenue dans SOTRAMINE PHOSPHATE")
        title.setStyleSheet("""
            QLabel {
                font-size: 36px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 20px;
                padding: 16px;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Sous-titre
        subtitle = QLabel("Système de Gestion de Maintenance Industrielle")
        subtitle.setStyleSheet("""
            QLabel {
                font-size: 20px;
                color: #7f8c8d;
                margin-bottom: 40px;
                padding: 8px;
                font-weight: 500;
            }
        """)
        subtitle.setAlignment(Qt.AlignCenter)
        layout.addWidget(subtitle)
        
        # Statistiques rapides
        stats_frame = self.create_stats_frame()
        layout.addWidget(stats_frame)
        
        # Actions rapides
        actions_frame = self.create_actions_frame()
        layout.addWidget(actions_frame)
        
        layout.addStretch()
        
        return page
    
    def create_stats_frame(self):
        """Crée le cadre des statistiques"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: #ecf0f1;
                border-radius: 10px;
                padding: 20px;
                margin: 20px 0;
            }
        """)
        
        layout = QHBoxLayout(frame)
        
        # Statistiques simulées
        stats = [
            ("📋", "Tâches", "0", "#3498db"),
            ("🔌", "Équipements", "0", "#e67e22"),
            ("👥", "Personnel", "0", "#27ae60"),
            ("📊", "Rapports", "0", "#9b59b6")
        ]
        
        for icon, label, value, color in stats:
            stat_widget = self.create_stat_widget(icon, label, value, color)
            layout.addWidget(stat_widget)
        
        return frame
    
    def create_stat_widget(self, icon, label, value, color):
        """Crée un widget de statistique"""
        widget = QFrame()
        widget.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 2px solid {color};
                border-radius: 8px;
                padding: 20px;
                margin: 10px;
                min-width: 150px;
            }}
        """)
        
        layout = QVBoxLayout(widget)
        layout.setAlignment(Qt.AlignCenter)
        
        # Icône et valeur
        icon_value = QLabel(f"{icon} {value}")
        icon_value.setStyleSheet(f"""
            QLabel {{
                font-size: 24px;
                font-weight: bold;
                color: {color};
                margin-bottom: 10px;
            }}
        """)
        icon_value.setAlignment(Qt.AlignCenter)
        layout.addWidget(icon_value)
        
        # Label
        label_widget = QLabel(label)
        label_widget.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #7f8c8d;
                font-weight: bold;
            }
        """)
        label_widget.setAlignment(Qt.AlignCenter)
        layout.addWidget(label_widget)
        
        return widget
    
    def create_actions_frame(self):
        """Crée le cadre des actions rapides"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        
        layout = QVBoxLayout(frame)
        
        title = QLabel("Actions Rapides")
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title)
        
        # Boutons d'actions
        actions_layout = QHBoxLayout()
        
        actions = [
            ("📋 Nouvelle Tâche", self.show_tasks, "#3498db"),
            ("🔌 Nouvel Équipement", self.show_equipment, "#e67e22"),
            ("📊 Voir Rapports", self.show_reports, "#9b59b6")
        ]
        
        for text, action, color in actions:
            btn = QPushButton(text)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color};
                    color: white;
                    border: none;
                    border-radius: 8px;
                    padding: 12px 20px;
                    font-size: 14px;
                    font-weight: bold;
                    min-width: 150px;
                }}
                QPushButton:hover {{
                    background-color: {color}dd;
                }}
                QPushButton:pressed {{
                    background-color: {color}bb;
                }}
            """)
            btn.clicked.connect(action)
            actions_layout.addWidget(btn)
        
        layout.addLayout(actions_layout)
        
        return frame
    
    # Méthodes de navigation
    def show_home(self):
        """Affiche la page d'accueil"""
        self.content_area.setCurrentWidget(self.home_page)
    
    def show_tasks(self):
        """Affiche la gestion des tâches"""
        QMessageBox.information(self, "Tâches", "Module de gestion des tâches\n\nFonctionnalité en cours de développement.")
    
    def show_equipment(self):
        """Affiche la gestion des équipements"""
        QMessageBox.information(self, "Équipements", "Module de gestion des équipements\n\nFonctionnalité en cours de développement.")
    
    def show_spare_parts(self):
        """Affiche la gestion des pièces"""
        QMessageBox.information(self, "Pièces de Rechange", "Module de gestion des pièces de rechange\n\nFonctionnalité en cours de développement.")
    
    def show_personnel(self):
        """Affiche la gestion du personnel"""
        QMessageBox.information(self, "Personnel", "Module de gestion du personnel\n\nFonctionnalité en cours de développement.")
    
    def show_attendance(self):
        """Affiche le pointage"""
        QMessageBox.information(self, "Pointage", "Module de pointage\n\nFonctionnalité en cours de développement.")
    
    def show_reports(self):
        """Affiche les rapports"""
        QMessageBox.information(self, "Rapports", "Module de rapports et analyses\n\nFonctionnalité en cours de développement.")

def main():
    """Point d'entrée principal"""
    print("🚀 LANCEMENT SOTRAMINE PHOSPHATE")
    print("=" * 40)
    
    # Créer l'application Qt
    app = QApplication(sys.argv)
    print("✓ Application Qt créée")
    
    # Créer la fenêtre principale
    window = SotramineApp()
    print("✓ Interface utilisateur créée")
    
    # Afficher la fenêtre
    window.show()
    print("✓ Application affichée")
    
    print("\n🎉 APPLICATION LANCÉE AVEC SUCCÈS !")
    print("📋 Menu organisé en 3 sections :")
    print("   📊 PRODUCTION : Tableau de bord, Tâches, Rapports")
    print("   🔧 MAINTENANCE : Équipements, Pièces de rechange")
    print("   👥 PERSONNEL : Gestion personnel, Pointage")
    print("\n✨ Interface moderne et professionnelle")
    print("🔧 Système de maintenance développé professionnellement")
    
    # Lancer la boucle d'événements
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
