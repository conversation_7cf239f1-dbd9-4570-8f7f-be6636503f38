import sys
import os
from PyQt5.QtWidgets import (QApplication, QSplashScreen, QMainWindow, QStackedWidget, QToolBar,
                             QAction, QProgressBar, QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                             QLabel, QFrame, QGridLayout, QMessageBox, QInputDialog)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import QPixmap, QFont, QPainter, QColor
from database import Database
from export.excel_export import ExcelExporter
from gui.main_window import MainWindow
from gui.welcome_screen import WelcomeScreen
from gui.icons import generate_all_icons
from gui.theme_manager import ThemeManager
from utils.logger import app_logger, perf_logger
from utils.performance import monitor_performance, time_operation
from utils.data_refresh_manager import get_refresh_manager, register_for_updates
from config import APP_CONFIG, DEFAULT_CATEGORIES

class DatabaseInitThread(QThread):
    """Thread pour l'initialisation asynchrone de la base de données"""
    progress_updated = pyqtSignal(str)
    initialization_complete = pyqtSignal(object)
    error_occurred = pyqtSignal(str)

    def run(self):
        try:
            self.progress_updated.emit("Initialisation de la base de données...")
            db = init_database()

            self.progress_updated.emit("Optimisation des performances...")
            # Optimiser la base de données au démarrage
            db.vacuum_database()

            self.progress_updated.emit("Vérification de l'intégrité...")
            # Vérifier les statistiques
            stats = db.get_database_stats()
            print(f"Statistiques de la base de données : {stats}")

            self.initialization_complete.emit(db)
        except Exception as e:
            self.error_occurred.emit(str(e))

@monitor_performance("init_database")
def init_database():
    """Initialise la base de données avec les catégories par défaut si nécessaire"""
    try:
        with time_operation("database_connection"):
            db = Database()

        # Vérifier si la base de données est vide
        with time_operation("check_categories"):
            categories = db.get_all_categories()
        
        if not categories:
            app_logger.info("Initialisation des catégories par défaut...")
            
            with time_operation("add_default_categories"):
                for name, color in DEFAULT_CATEGORIES:
                    try:
                        db.add_category(name, color)
                        app_logger.info(f"Catégorie '{name}' ajoutée avec succès")
                    except Exception as e:
                        app_logger.error(f"Erreur lors de l'ajout de la catégorie {name}: {str(e)}")
        else:
            app_logger.info(f"{len(categories)} catégories trouvées dans la base de données")

        return db
    except Exception as e:
        app_logger.critical(f"Erreur critique lors de l'initialisation de la base de données: {str(e)}")
        raise

class OptimizedSotramineApp(QMainWindow):
    """Application principale SOTRAMINE PHOSPHATE optimisée"""
    def __init__(self, db, excel_exporter):
        super().__init__()
        self.db = db
        self.excel_exporter = excel_exporter
        self.current_section = None

        # Initialiser le gestionnaire de thèmes
        self.theme_manager = ThemeManager(self)

        # Initialiser le gestionnaire d'actualisation
        self.refresh_manager = get_refresh_manager()

        # Gestionnaire d'actions centralisé pour éviter la duplication
        self.action_manager = self.create_action_manager()

        self.setup_ui()

        # Enregistrer pour les mises à jour automatiques
        register_for_updates(self)

        # Appliquer le thème
        self.theme_manager.apply_theme()

    def create_action_manager(self):
        """Crée le gestionnaire d'actions centralisé"""
        actions = {
            'home': {'text': '🏠 Accueil', 'tooltip': 'Retourner à l\'accueil', 'shortcut': 'Alt+Home'},
            'new_task': {'text': '✚ Nouvelle tâche', 'tooltip': 'Créer une nouvelle tâche', 'shortcut': 'Ctrl+N'},
            'search': {'text': '🔍 Rechercher', 'tooltip': 'Rechercher des éléments', 'shortcut': 'Ctrl+F'},
            'refresh': {'text': '🔄 Actualiser', 'tooltip': 'Actualiser les données', 'shortcut': 'F5'},
            'export': {'text': '📤 Exporter', 'tooltip': 'Exporter les données', 'shortcut': 'Ctrl+E'},
            'import': {'text': '📥 Importer', 'tooltip': 'Importer des données', 'shortcut': 'Ctrl+I'},
            'settings': {'text': '⚙️ Paramètres', 'tooltip': 'Ouvrir les paramètres', 'shortcut': 'Ctrl+,'},
            'help': {'text': '❓ Aide', 'tooltip': 'Afficher l\'aide', 'shortcut': 'F1'}
        }
        return actions

    def setup_ui(self):
        """Configure l'interface utilisateur principale optimisée"""
        self.setWindowTitle("SOTRAMINE PHOSPHATE - Système de Gestion de Maintenance")
        self.setMinimumSize(1400, 900)

        # Widget central principal
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Layout principal horizontal
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Créer le menu latéral optimisé
        self.sidebar = self.create_optimized_sidebar()
        main_layout.addWidget(self.sidebar)

        # Zone de contenu principal
        content_area = QWidget()
        content_layout = QVBoxLayout(content_area)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)

        # Barre d'outils contextuelle unifiée
        self.toolbar = self.create_unified_toolbar()
        content_layout.addWidget(self.toolbar)

        # Widget empilé pour les différentes sections
        self.stacked_widget = QStackedWidget()
        content_layout.addWidget(self.stacked_widget)

        main_layout.addWidget(content_area)

        # Créer les sections optimisées
        self.create_optimized_sections()

        # Barre de statut
        self.setup_status_bar()

        # Afficher l'accueil par défaut
        self.navigate_to_section('home')

    def create_optimized_sidebar(self):
        """Crée un menu latéral optimisé sans répétition"""
        sidebar = QWidget()
        sidebar.setFixedWidth(250)
        sidebar.setStyleSheet("""
            QWidget {
                background-color: #2c3e50;
                color: white;
            }
            QPushButton {
                background-color: transparent;
                border: none;
                padding: 12px 16px;
                text-align: left;
                font-size: 14px;
                color: white;
                border-radius: 6px;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #34495e;
            }
            QPushButton:checked {
                background-color: #3498db;
                font-weight: bold;
            }
        """)

        layout = QVBoxLayout(sidebar)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(4)

        # En-tête
        header = QLabel("SOTRAMINE PHOSPHATE")
        header.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                padding: 16px;
                background-color: #1976D2;
                border-radius: 8px;
                margin-bottom: 16px;
            }
        """)
        header.setAlignment(Qt.AlignCenter)
        layout.addWidget(header)

        # Menu organisé en trois grandes sections
        self.sidebar_buttons = {}

        # Section PRODUCTION
        production_header = QLabel("📊 PRODUCTION")
        production_header.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                padding: 12px 16px 8px 16px;
                color: #e74c3c;
                background-color: transparent;
                border-bottom: 2px solid #e74c3c;
                margin: 8px 0 4px 0;
            }
        """)
        layout.addWidget(production_header)

        production_sections = [
            ('home', '🏠 Tableau de Bord', 'Vue d\'ensemble de la production'),
            ('tasks', '📋 Tâches', 'Gestion des tâches de production'),
            ('reports', '📄 Rapports', 'Rapports de production et analyses')
        ]

        for section_id, title, description in production_sections:
            btn = QPushButton(title)
            btn.setToolTip(description)
            btn.setCheckable(True)
            btn.clicked.connect(lambda checked, sid=section_id: self.navigate_to_section(sid))
            btn.setStyleSheet(btn.styleSheet() + "QPushButton { margin-left: 16px; }")
            self.sidebar_buttons[section_id] = btn
            layout.addWidget(btn)

        # Espacement entre sections
        layout.addSpacing(16)

        # Section MAINTENANCE
        maintenance_header = QLabel("🔧 MAINTENANCE")
        maintenance_header.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                padding: 12px 16px 8px 16px;
                color: #f39c12;
                background-color: transparent;
                border-bottom: 2px solid #f39c12;
                margin: 8px 0 4px 0;
            }
        """)
        layout.addWidget(maintenance_header)

        maintenance_sections = [
            ('maintenance', '🔧 Centre de Maintenance', 'Système complet de maintenance industrielle'),
            ('equipment', '🔌 Équipements', 'Gestion du parc d\'équipements'),
            ('spare_parts', '🔧 Pièces de Rechange', 'Inventaire et gestion des pièces')
        ]

        for section_id, title, description in maintenance_sections:
            btn = QPushButton(title)
            btn.setToolTip(description)
            btn.setCheckable(True)
            btn.clicked.connect(lambda checked, sid=section_id: self.navigate_to_section(sid))
            btn.setStyleSheet(btn.styleSheet() + "QPushButton { margin-left: 16px; }")
            self.sidebar_buttons[section_id] = btn
            layout.addWidget(btn)

        # Espacement entre sections
        layout.addSpacing(16)

        # Section PERSONNEL
        personnel_header = QLabel("👥 PERSONNEL")
        personnel_header.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                padding: 12px 16px 8px 16px;
                color: #27ae60;
                background-color: transparent;
                border-bottom: 2px solid #27ae60;
                margin: 8px 0 4px 0;
            }
        """)
        layout.addWidget(personnel_header)

        personnel_sections = [
            ('personnel', '👤 Gestion Personnel', 'Gestion des employés'),
            ('attendance', '📊 Pointage', 'Suivi des présences et horaires')
        ]

        for section_id, title, description in personnel_sections:
            btn = QPushButton(title)
            btn.setToolTip(description)
            btn.setCheckable(True)
            btn.clicked.connect(lambda checked, sid=section_id: self.navigate_to_section(sid))
            btn.setStyleSheet(btn.styleSheet() + "QPushButton { margin-left: 16px; }")
            self.sidebar_buttons[section_id] = btn
            layout.addWidget(btn)

        # Espacement avant paramètres
        layout.addSpacing(24)

        # Section CONFIGURATION (séparée)
        config_header = QLabel("⚙️ CONFIGURATION")
        config_header.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                padding: 12px 16px 8px 16px;
                color: #95a5a6;
                background-color: transparent;
                border-bottom: 2px solid #95a5a6;
                margin: 8px 0 4px 0;
            }
        """)
        layout.addWidget(config_header)

        config_sections = [
            ('settings', '⚙️ Paramètres', 'Configuration de l\'application')
        ]

        for section_id, title, description in config_sections:
            btn = QPushButton(title)
            btn.setToolTip(description)
            btn.setCheckable(True)
            btn.clicked.connect(lambda checked, sid=section_id: self.navigate_to_section(sid))
            btn.setStyleSheet(btn.styleSheet() + "QPushButton { margin-left: 16px; }")
            self.sidebar_buttons[section_id] = btn
            layout.addWidget(btn)

        layout.addStretch()

        # Informations système en bas
        info_label = QLabel("Version 2.1")
        info_label.setStyleSheet("color: #bdc3c7; font-size: 12px; padding: 8px;")
        info_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(info_label)

        return sidebar

    def create_unified_toolbar(self):
        """Crée une barre d'outils unifiée sans répétition"""
        # Utiliser le gestionnaire d'actions centralisé pour éviter les duplications
        from gui.action_manager import ActionManager
        from gui.contextual_toolbar import ContextualToolbar

        # Créer une barre d'outils contextuelle moderne
        self.contextual_toolbar = ContextualToolbar(self)
        self.contextual_toolbar.action_triggered.connect(self.handle_toolbar_action)
        self.contextual_toolbar.search_requested.connect(self.handle_search)

        # Configurer le style unifié
        self.contextual_toolbar.setStyleSheet("""
            QToolBar {
                background-color: #ecf0f1;
                border: none;
                padding: 8px;
                spacing: 4px;
                min-height: 50px;
            }
            QToolButton {
                background-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                margin: 2px;
                font-size: 13px;
                min-width: 80px;
            }
            QToolButton:hover {
                background-color: #3498db;
                color: white;
                border-color: #2980b9;
            }
            QToolButton:pressed {
                background-color: #2980b9;
            }
            QLabel {
                font-weight: bold;
                color: #2c3e50;
                font-size: 16px;
                margin: 0 16px;
            }
        """)

        return self.contextual_toolbar

    def update_contextual_toolbar(self, section_id):
        """Met à jour la barre d'outils selon la section active (optimisé)"""
        # Utiliser la barre d'outils contextuelle centralisée
        if hasattr(self, 'contextual_toolbar'):
            # Mapping des sections vers les titres
            section_titles = {
                'home': '🏠 Accueil',
                'tasks': '📋 Gestion des tâches',
                'equipment': '🔌 Gestion des équipements',
                'spare_parts': '🔧 Pièces de rechange',
                'personnel': '👥 Gestion du personnel',
                'attendance': '📊 Pointage du personnel',
                'reports': '📄 Rapports et analyses',
                'settings': '⚙️ Paramètres'
            }

            title = section_titles.get(section_id, section_id.title())
            self.contextual_toolbar.set_context(section_id, title)

    def create_optimized_sections(self):
        """Crée les sections optimisées sans duplication d'interface"""
        # Section Accueil
        self.home_widget = self.create_home_section()
        self.stacked_widget.addWidget(self.home_widget)

        # Créer et ajouter la fenêtre principale existante (réutilisée)
        self.main_window = MainWindow(self.db, self.excel_exporter)
        self.stacked_widget.addWidget(self.main_window)

        # Section Maintenance Professionnelle
        self.maintenance_widget = self.create_maintenance_section()
        self.stacked_widget.addWidget(self.maintenance_widget)

        # Mapping des sections vers les onglets de la fenêtre principale
        self.section_mapping = {
            'home': 0,  # Widget d'accueil
            'maintenance': 2,  # Widget de maintenance professionnel
            'tasks': (1, 1, 0),  # (widget_index, tab_index, sub_tab_index)
            'equipment': (1, 2, 1),
            'spare_parts': (1, 2, 2),
            'personnel': (1, 3, 0),
            'attendance': (1, 3, 1),
            'reports': (1, 0, 0),
            'settings': 'dialog'
        }

    def create_home_section(self):
        """Crée une section d'accueil organisée par sections"""
        from gui.organized_home_widget import OrganizedHomeWidget

        # Créer le widget d'accueil organisé
        home_widget = OrganizedHomeWidget(self.db, self)

        # Connecter le signal de navigation
        home_widget.section_requested.connect(self.navigate_to_section)

        return home_widget

    def create_maintenance_section(self):
        """Crée la section de maintenance professionnelle"""
        from gui.maintenance_manager import MaintenanceManager

        # Créer le gestionnaire de maintenance professionnel
        maintenance_widget = MaintenanceManager(self.db, self)

        # Connecter les signaux si nécessaire
        maintenance_widget.section_requested.connect(self.navigate_to_section)

        return maintenance_widget

    def create_shortcut_button(self, section_id, title, description):
        """Crée un bouton de raccourci pour l'accueil (taille agrandie)"""
        btn = QPushButton()
        # Taille considérablement agrandie pour une meilleure visibilité
        btn.setFixedSize(420, 180)
        btn.setStyleSheet("""
            QPushButton {
                background-color: white;
                border: 3px solid #e9ecef;
                border-radius: 16px;
                padding: 24px;
                text-align: left;
                font-size: 16px;
                color: #2c3e50;
            }
            QPushButton:hover {
                border-color: #3498db;
                background-color: #f8f9ff;
            }
            QPushButton:pressed {
                background-color: #e3f2fd;
            }
        """)

        # Layout interne du bouton avec plus d'espace
        btn_layout = QVBoxLayout(btn)
        btn_layout.setContentsMargins(16, 16, 16, 16)
        btn_layout.setSpacing(12)

        # Titre plus grand et plus visible
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            font-weight: bold;
            font-size: 20px;
            color: #2c3e50;
            margin-bottom: 8px;
        """)
        btn_layout.addWidget(title_label)

        # Description plus lisible
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            color: #6c757d;
            font-size: 14px;
            line-height: 1.4;
        """)
        desc_label.setWordWrap(True)
        btn_layout.addWidget(desc_label)

        # Ajouter un peu d'espace en bas
        btn_layout.addStretch()

        btn.clicked.connect(lambda: self.navigate_to_section(section_id))
        return btn

    def setup_status_bar(self):
        """Configure la barre de statut"""
        status_bar = self.statusBar()
        status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #34495e;
                color: white;
                border-top: 1px solid #2c3e50;
                padding: 4px;
            }
        """)
        status_bar.showMessage("Prêt - SOTRAMINE PHOSPHATE v2.1")

    def navigate_to_section(self, section_id):
        """Navigation sélective - affiche seulement les éléments de la section choisie"""
        if section_id == self.current_section:
            return

        # Mettre à jour l'état des boutons du sidebar
        for btn_id, btn in self.sidebar_buttons.items():
            btn.setChecked(btn_id == section_id)

        # Mettre à jour la barre d'outils contextuelle
        self.update_contextual_toolbar(section_id)

        # Navigation sélective vers la section
        if section_id == 'home':
            self.stacked_widget.setCurrentIndex(0)
            self.statusBar().showMessage("Accueil - Vue d'ensemble du système")
            # Masquer tous les onglets de la fenêtre principale
            self.hide_all_main_window_tabs()
        elif section_id == 'settings':
            self.open_configuration()
            return
        else:
            # Afficher la fenêtre principale
            self.stacked_widget.setCurrentIndex(1)

            # Masquer tous les onglets d'abord
            self.hide_all_main_window_tabs()

            # Afficher seulement l'onglet spécifique à cette section
            self.show_specific_section_content(section_id)

            # Mettre à jour le message de statut
            status_messages = {
                'tasks': 'Gestion des tâches - Planification et suivi',
                'equipment': 'Gestion des équipements - Maintenance préventive',
                'spare_parts': 'Pièces de rechange - Inventaire et stock',
                'personnel': 'Gestion du personnel - Équipes et compétences',
                'attendance': 'Pointage - Suivi de présence',
                'reports': 'Rapports - Analyses et tableaux de bord'
            }
            self.statusBar().showMessage(status_messages.get(section_id, f"Section {section_id}"))

        self.current_section = section_id

    def hide_all_main_window_tabs(self):
        """Masque tous les onglets de la fenêtre principale et leurs barres d'outils"""
        if hasattr(self.main_window, 'tab_widget'):
            # Masquer tous les onglets
            tab_widget = self.main_window.tab_widget
            for i in range(tab_widget.count()):
                tab_widget.setTabVisible(i, False)

            # Masquer aussi la barre d'onglets elle-même
            tab_widget.tabBar().hide()

            # Masquer les barres d'outils répétitives de la fenêtre principale
            self.hide_main_window_toolbars()

    def show_specific_section_content(self, section_id):
        """Affiche seulement le contenu spécifique à la section sélectionnée (sans répétitions)"""
        if not hasattr(self.main_window, 'tab_widget'):
            return

        tab_widget = self.main_window.tab_widget

        # Mapping spécifique : section -> onglet à afficher
        section_tab_mapping = {
            'tasks': 1,        # Onglet "Gestion des tâches"
            'equipment': 2,    # Onglet "Maintenance" -> sous-onglet Équipements
            'spare_parts': 2,  # Onglet "Maintenance" -> sous-onglet Pièces
            'personnel': 3,    # Onglet "Ressources Humaines" -> sous-onglet Personnel
            'attendance': 3,   # Onglet "Ressources Humaines" -> sous-onglet Pointage
            'reports': 0       # Onglet "Tableau de bord"
        }

        # Afficher seulement l'onglet correspondant à la section
        if section_id in section_tab_mapping:
            tab_index = section_tab_mapping[section_id]

            # Rendre visible seulement cet onglet
            tab_widget.setTabVisible(tab_index, True)
            tab_widget.setCurrentIndex(tab_index)

            # Afficher la barre d'onglets seulement pour cet onglet
            tab_widget.tabBar().show()

            # Masquer les barres d'outils répétitives de cet onglet
            self.hide_tab_specific_toolbars(tab_index)

            # Gérer les sous-onglets spécifiques
            self.configure_sub_tabs_for_section(section_id, tab_index)

    def configure_sub_tabs_for_section(self, section_id, tab_index):
        """Configure les sous-onglets pour afficher seulement ce qui est pertinent"""
        if not hasattr(self.main_window, 'tab_widget'):
            return

        current_tab = self.main_window.tab_widget.widget(tab_index)

        if hasattr(current_tab, 'count') and hasattr(current_tab, 'setTabVisible'):
            # C'est un widget avec des sous-onglets

            # D'abord masquer tous les sous-onglets
            for i in range(current_tab.count()):
                current_tab.setTabVisible(i, False)

            # Afficher seulement le sous-onglet pertinent
            sub_tab_mapping = {
                'equipment': 1,    # Sous-onglet Équipements dans Maintenance
                'spare_parts': 2,  # Sous-onglet Pièces dans Maintenance
                'personnel': 0,    # Sous-onglet Personnel dans RH
                'attendance': 1    # Sous-onglet Pointage dans RH
            }

            if section_id in sub_tab_mapping:
                sub_tab_index = sub_tab_mapping[section_id]
                if sub_tab_index < current_tab.count():
                    current_tab.setTabVisible(sub_tab_index, True)
                    current_tab.setCurrentIndex(sub_tab_index)

    def handle_action(self, action_id):
        """Gestionnaire centralisé des actions (optimisé sans répétition)"""
        # Dictionnaire des actions pour éviter les répétitions de code
        action_handlers = {
            'home': lambda: self.navigate_to_section('home'),
            'search': self.show_search_dialog,
            'refresh': self.refresh_current_section,
            'new_task': self.create_new_task,
            'new_equipment': self.create_new_equipment,
            'new_person': self.create_new_person,
            'new_spare_part': self.create_new_spare_part,
            'schedule_maintenance': self.schedule_maintenance,
            'export': self.export_current_section,
            'export_equipment': self.export_current_section,
            'import': self.import_to_current_section,
            'import_equipment': self.import_equipment_dialog,
            'settings': self.open_configuration,
            'help': self.show_help
        }

        # Exécuter l'action si elle existe
        if action_id in action_handlers:
            action_handlers[action_id]()
        else:
            self.statusBar().showMessage(f"Action non implémentée : {action_id}", 3000)

    def handle_toolbar_action(self, action_id, data=None):
        """Gestionnaire des actions de la barre d'outils contextuelle"""
        # Rediriger vers le gestionnaire centralisé pour éviter la duplication
        self.handle_action(action_id)

    def handle_search(self, search_text):
        """Gestionnaire de recherche unifié"""
        # Recherche contextuelle selon la section active
        search_handlers = {
            'tasks': lambda text: self.search_in_section('tasks', text),
            'equipment': lambda text: self.search_in_section('equipment', text),
            'spare_parts': lambda text: self.search_in_section('spare_parts', text),
            'personnel': lambda text: self.search_in_section('personnel', text),
            'attendance': lambda text: self.search_in_section('attendance', text)
        }

        current_section = getattr(self, 'current_section', 'home')
        if current_section in search_handlers:
            search_handlers[current_section](search_text)
        else:
            self.show_global_search_dialog(search_text)

    def show_search_dialog(self):
        """Affiche une boîte de dialogue de recherche unifiée"""
        # Implémentation de la recherche globale
        pass

    def refresh_current_section(self):
        """Actualise la section courante"""
        if hasattr(self.main_window, 'refresh_current_tab'):
            self.main_window.refresh_current_tab()
        self.statusBar().showMessage("Données actualisées", 2000)

    def create_new_task(self):
        """Crée une nouvelle tâche"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QTextEdit, QComboBox, QDateEdit, QPushButton, QMessageBox
            from PyQt5.QtCore import QDate
            
            # Créer la boîte de dialogue
            dialog = QDialog(self)
            dialog.setWindowTitle("Nouvelle Tâche")
            dialog.setModal(True)
            dialog.resize(500, 400)
            
            # Layout principal
            layout = QVBoxLayout()
            
            # Titre
            title_layout = QHBoxLayout()
            title_label = QLabel("Titre:")
            title_edit = QLineEdit()
            title_layout.addWidget(title_label)
            title_layout.addWidget(title_edit)
            layout.addLayout(title_layout)
            
            # Description
            desc_label = QLabel("Description:")
            desc_edit = QTextEdit()
            desc_edit.setMaximumHeight(100)
            layout.addWidget(desc_label)
            layout.addWidget(desc_edit)
            
            # Date limite
            due_layout = QHBoxLayout()
            due_label = QLabel("Date limite:")
            due_edit = QDateEdit()
            due_edit.setDate(QDate.currentDate().addDays(7))  # Par défaut dans 7 jours
            due_edit.setCalendarPopup(True)
            due_layout.addWidget(due_label)
            due_layout.addWidget(due_edit)
            layout.addLayout(due_layout)
            
            # Priorité
            priority_layout = QHBoxLayout()
            priority_label = QLabel("Priorité:")
            priority_combo = QComboBox()
            priority_combo.addItems(["basse", "moyenne", "haute"])
            priority_combo.setCurrentText("moyenne")
            priority_layout.addWidget(priority_label)
            priority_layout.addWidget(priority_combo)
            layout.addLayout(priority_layout)
            
            # Catégorie
            category_layout = QHBoxLayout()
            category_label = QLabel("Catégorie:")
            category_combo = QComboBox()
            
            # Récupérer les catégories depuis la base de données
            self.db.cursor.execute("SELECT id, name FROM categories ORDER BY name")
            categories = self.db.cursor.fetchall()
            for cat_id, cat_name in categories:
                category_combo.addItem(cat_name, cat_id)
            
            category_layout.addWidget(category_label)
            category_layout.addWidget(category_combo)
            layout.addLayout(category_layout)
            
            # Affectation (Personne assignée)
            assignment_layout = QHBoxLayout()
            assignment_label = QLabel("Affecté à:")
            assignment_combo = QComboBox()
            assignment_combo.addItem("Non assigné", None)
            
            # Récupérer le personnel depuis la base de données
            self.db.cursor.execute("SELECT id, name, role FROM persons ORDER BY name")
            persons = self.db.cursor.fetchall()
            for person_id, person_name, person_role in persons:
                assignment_combo.addItem(f"{person_name} ({person_role})", person_id)
            
            assignment_layout.addWidget(assignment_label)
            assignment_layout.addWidget(assignment_combo)
            layout.addLayout(assignment_layout)
            
            # Zone de travail
            zone_layout = QHBoxLayout()
            zone_label = QLabel("Zone de travail:")
            zone_edit = QLineEdit()
            zone_edit.setPlaceholderText("Ex: Atelier mécanique, Zone 1, Bâtiment A...")
            zone_layout.addWidget(zone_label)
            zone_layout.addWidget(zone_edit)
            layout.addLayout(zone_layout)
            
            # Pièces de rechange utilisées
            parts_label = QLabel("Pièces de rechange utilisées:")
            layout.addWidget(parts_label)
            
            parts_layout = QVBoxLayout()
            
            # Liste des pièces sélectionnées
            parts_list_layout = QHBoxLayout()
            parts_list_label = QLabel("Pièces sélectionnées:")
            parts_list_layout.addWidget(parts_list_label)
            layout.addLayout(parts_list_layout)
            
            # Zone pour afficher les pièces sélectionnées
            parts_display = QTextEdit()
            parts_display.setMaximumHeight(80)
            parts_display.setReadOnly(True)
            parts_display.setPlaceholderText("Aucune pièce sélectionnée")
            layout.addWidget(parts_display)
            
            # Boutons pour gérer les pièces
            parts_buttons_layout = QHBoxLayout()
            
            add_part_button = QPushButton("Ajouter une pièce")
            clear_parts_button = QPushButton("Effacer la liste")
            
            parts_buttons_layout.addWidget(add_part_button)
            parts_buttons_layout.addWidget(clear_parts_button)
            layout.addLayout(parts_buttons_layout)
            
            # Stockage des pièces sélectionnées
            selected_parts = []
            
            def add_part():
                """Ouvre une boîte de dialogue pour sélectionner une pièce"""
                try:
                    from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QComboBox, QSpinBox, QPushButton
                    
                    part_dialog = QDialog(dialog)
                    part_dialog.setWindowTitle("Sélectionner une pièce")
                    part_dialog.setModal(True)
                    part_dialog.resize(400, 200)
                    
                    part_layout = QVBoxLayout()
                    
                    # Sélection de la pièce
                    part_select_layout = QHBoxLayout()
                    part_select_label = QLabel("Pièce:")
                    part_combo = QComboBox()
                    
                    # Récupérer les pièces de rechange
                    self.db.cursor.execute("SELECT id, name, code, quantity FROM spare_parts ORDER BY name")
                    spare_parts = self.db.cursor.fetchall()
                    
                    for part_id, part_name, part_code, part_quantity in spare_parts:
                        part_combo.addItem(f"{part_name} ({part_code}) - Stock: {part_quantity}", part_id)
                    
                    part_select_layout.addWidget(part_select_label)
                    part_select_layout.addWidget(part_combo)
                    part_layout.addLayout(part_select_layout)
                    
                    # Quantité
                    quantity_layout = QHBoxLayout()
                    quantity_label = QLabel("Quantité:")
                    quantity_spin = QSpinBox()
                    quantity_spin.setMinimum(1)
                    quantity_spin.setMaximum(100)
                    quantity_spin.setValue(1)
                    
                    quantity_layout.addWidget(quantity_label)
                    quantity_layout.addWidget(quantity_spin)
                    part_layout.addLayout(quantity_layout)
                    
                    # Boutons
                    button_layout = QHBoxLayout()
                    cancel_button = QPushButton("Annuler")
                    add_button = QPushButton("Ajouter")
                    
                    button_layout.addWidget(cancel_button)
                    button_layout.addWidget(add_button)
                    part_layout.addLayout(button_layout)
                    
                    part_dialog.setLayout(part_layout)
                    
                    # Connexions
                    cancel_button.clicked.connect(part_dialog.reject)
                    add_button.clicked.connect(part_dialog.accept)
                    
                    if part_dialog.exec_() == QDialog.Accepted:
                        part_id = part_combo.currentData()
                        part_name = part_combo.currentText().split(" (")[0]
                        quantity = quantity_spin.value()
                        
                        # Vérifier si la pièce n'est pas déjà sélectionnée
                        for existing_part in selected_parts:
                            if existing_part['id'] == part_id:
                                QMessageBox.warning(dialog, "Attention", "Cette pièce est déjà dans la liste")
                                return
                        
                        # Ajouter à la liste
                        selected_parts.append({
                            'id': part_id,
                            'name': part_name,
                            'quantity': quantity
                        })
                        
                        # Mettre à jour l'affichage
                        update_parts_display()
                        
                except Exception as e:
                    QMessageBox.critical(dialog, "Erreur", f"Erreur lors de l'ajout de la pièce: {str(e)}")
            
            def clear_parts():
                """Efface la liste des pièces sélectionnées"""
                selected_parts.clear()
                update_parts_display()
            
            def update_parts_display():
                """Met à jour l'affichage des pièces sélectionnées"""
                if not selected_parts:
                    parts_display.setPlainText("Aucune pièce sélectionnée")
                else:
                    parts_text = ""
                    for part in selected_parts:
                        parts_text += f"• {part['name']} (x{part['quantity']})\n"
                    parts_display.setPlainText(parts_text)
            
            # Connexions des boutons
            add_part_button.clicked.connect(add_part)
            clear_parts_button.clicked.connect(clear_parts)
            
            # Boutons
            button_layout = QHBoxLayout()
            cancel_button = QPushButton("Annuler")
            create_button = QPushButton("Créer")
            create_button.setDefault(True)
            
            button_layout.addWidget(cancel_button)
            button_layout.addWidget(create_button)
            layout.addLayout(button_layout)
            
            dialog.setLayout(layout)
            
            # Connexions
            cancel_button.clicked.connect(dialog.reject)
            create_button.clicked.connect(dialog.accept)
            
            # Validation
            def validate_and_create():
                title = title_edit.text().strip()
                if not title:
                    QMessageBox.warning(dialog, "Erreur", "Le titre est obligatoire")
                    return
                
                # Récupérer les valeurs
                description = desc_edit.toPlainText().strip()
                due_date = due_edit.date().toString("yyyy-MM-dd")
                priority = priority_combo.currentText()
                category_id = category_combo.currentData()
                assigned_to_id = assignment_combo.currentData()
                work_zone = zone_edit.text().strip()
                
                try:
                    # Créer la tâche dans la base de données
                    task_id = self.db.add_task(
                        category_id=category_id,
                        title=title,
                        description=description,
                        due_date=due_date,
                        priority=priority,
                        status="À faire",
                        assigned_to_ids=[assigned_to_id] if assigned_to_id else None
                    )
                    
                    # Ajouter la zone de travail à la description si spécifiée
                    if work_zone:
                        full_description = f"{description}\n\nZone de travail: {work_zone}"
                        # Mettre à jour la description de la tâche
                        self.db.cursor.execute(
                            "UPDATE tasks SET description = ? WHERE id = ?",
                            (full_description, task_id)
                        )
                        self.db.conn.commit()
                    
                    # Enregistrer les pièces de rechange utilisées
                    if selected_parts:
                        for part in selected_parts:
                            # Créer une intervention de maintenance pour cette tâche
                            intervention_id = self.db.add_maintenance_intervention(
                                task_id=task_id,
                                details=f"Utilisation de pièce: {part['name']} (x{part['quantity']})",
                                technician_id=assigned_to_id
                            )
                            
                            # Mettre à jour le stock de la pièce
                            self.db.cursor.execute("""
                                UPDATE spare_parts 
                                SET quantity = quantity - ? 
                                WHERE id = ?
                            """, (part['quantity'], part['id']))
                            
                            # Enregistrer le mouvement de stock
                            self.db.add_stock_movement(
                                part_id=part['id'],
                                movement_type='sortie',
                                quantity=part['quantity'],
                                reason=f"Utilisation pour tâche: {title}",
                                user_id=assigned_to_id
                            )
                        
                        self.db.conn.commit()
                    
                    QMessageBox.information(dialog, "Succès", "Tâche créée avec succès!")
                    dialog.accept()
                    
                    # Actualiser l'affichage si on est dans la section tâches
                    if self.current_section == 'tasks':
                        self.refresh_current_section()
                    
                except Exception as e:
                    QMessageBox.critical(dialog, "Erreur", f"Erreur lors de la création de la tâche: {str(e)}")
            
            create_button.clicked.disconnect()
            create_button.clicked.connect(validate_and_create)
            
            # Afficher la boîte de dialogue
            if dialog.exec_() == QDialog.Accepted:
                self.statusBar().showMessage("Nouvelle tâche créée", 3000)
                
        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la création de la tâche: {str(e)}")

    def export_current_section(self):
        """Exporte les données de la section courante"""
        try:
            if self.current_section:
                # Déterminer le type d'export selon la section courante
                export_methods = {
                    'tasks': self.excel_exporter.export_tasks_to_excel,
                    'personnel': self.excel_exporter.export_personnel_to_excel,
                    'equipment': self.excel_exporter.export_equipment_to_excel,
                    'spare_parts': self.excel_exporter.export_spare_parts_to_excel,
                    'attendance': self.excel_exporter.export_attendance_to_excel
                }
                
                if self.current_section in export_methods:
                    filename = export_methods[self.current_section]()
                    self.statusBar().showMessage(f"Export réussi : {filename}", 5000)
                else:
                    # Export général si la section n'est pas spécifique
                    filename = self.excel_exporter.export_daily_report()
                    self.statusBar().showMessage(f"Export général réussi : {filename}", 5000)
            else:
                # Export de toutes les données si aucune section n'est sélectionnée
                filename = self.excel_exporter.export_daily_report()
                self.statusBar().showMessage(f"Export complet réussi : {filename}", 5000)
                
        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "Erreur d'export", f"Erreur lors de l'export : {str(e)}")

    def import_to_current_section(self):
        """Importe des données vers la section courante"""
        try:
            from PyQt5.QtWidgets import QFileDialog, QMessageBox
            import pandas as pd
            
            # Ouvrir la boîte de dialogue de sélection de fichier
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "Sélectionner un fichier à importer",
                "",
                "Fichiers Excel (*.xlsx *.xls);;Fichiers CSV (*.csv);;Tous les fichiers (*)"
            )
            
            if file_path:
                # Détecter le type de fichier
                if file_path.endswith(('.xlsx', '.xls')):
                    # Importer depuis Excel
                    df = pd.read_excel(file_path)
                elif file_path.endswith('.csv'):
                    # Importer depuis CSV
                    df = pd.read_csv(file_path)
                else:
                    QMessageBox.warning(self, "Format non supporté", "Format de fichier non supporté")
                    return
                
                # Importer selon la section courante
                if self.current_section == 'tasks':
                    self.import_tasks_from_dataframe(df)
                elif self.current_section == 'personnel':
                    self.import_personnel_from_dataframe(df)
                elif self.current_section == 'equipment':
                    self.import_equipment_from_dataframe(df)
                elif self.current_section == 'spare_parts':
                    self.import_spare_parts_from_dataframe(df)
                else:
                    QMessageBox.information(self, "Import", f"Import de {len(df)} lignes depuis {file_path}")
                    
        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "Erreur d'import", f"Erreur lors de l'import : {str(e)}")

    def import_tasks_from_dataframe(self, df):
        """Importe des tâches depuis un DataFrame"""
        try:
            imported_count = 0
            for _, row in df.iterrows():
                # Mapper les colonnes du DataFrame aux champs de la base de données
                title = row.get('Title', row.get('Titre', ''))
                description = row.get('Description', '')
                due_date = row.get('Due Date', row.get('Date limite', ''))
                priority = row.get('Priority', row.get('Priorité', 'moyenne'))
                status = row.get('Status', row.get('Statut', 'À faire'))
                
                if title:  # Ne pas importer les tâches sans titre
                    # Utiliser la catégorie par défaut (ID 1) si aucune n'est spécifiée
                    category_id = 1
                    self.db.add_task(category_id, title, description, due_date, priority, status)
                    imported_count += 1
            
            self.statusBar().showMessage(f"Import réussi : {imported_count} tâches importées", 5000)
            
        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "Erreur d'import", f"Erreur lors de l'import des tâches : {str(e)}")

    def import_personnel_from_dataframe(self, df):
        """Importe du personnel depuis un DataFrame"""
        try:
            imported_count = 0
            for _, row in df.iterrows():
                name = row.get('Name', row.get('Nom', ''))
                role = row.get('Role', row.get('Rôle', 'Technicien'))
                department = row.get('Department', row.get('Département', ''))
                email = row.get('Email', '')
                phone = row.get('Phone', row.get('Téléphone', ''))
                
                if name:  # Ne pas importer les personnes sans nom
                    self.db.add_person(name, role)
                    imported_count += 1
            
            self.statusBar().showMessage(f"Import réussi : {imported_count} personnes importées", 5000)
            
        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "Erreur d'import", f"Erreur lors de l'import du personnel : {str(e)}")

    def import_equipment_from_dataframe(self, df):
        """Importe des équipements depuis un DataFrame"""
        try:
            from datetime import datetime

            def normalize_date(value):
                if value is None:
                    return None
                text = str(value).strip()
                if not text or text.lower() == 'nan':
                    return None
                # Essayer Timestamp -> datetime
                try:
                    if hasattr(value, 'to_pydatetime'):
                        return value.to_pydatetime().strftime('%Y-%m-%d')
                except Exception:
                    pass
                # Essayer parsing générique
                for fmt in ('%Y-%m-%d', '%d/%m/%Y', '%Y/%m/%d', '%m/%d/%Y'):
                    try:
                        return datetime.strptime(text[:10], fmt).strftime('%Y-%m-%d')
                    except Exception:
                        continue
                return text

            def to_none_if_empty(v):
                if v is None:
                    return None
                s = str(v).strip()
                return None if not s or s.lower() == 'nan' else s

            imported_count = 0
            for _, row in df.iterrows():
                name = row.get('Name', row.get('Nom', ''))
                if not str(name).strip():
                    continue  # nom obligatoire

                code = to_none_if_empty(row.get('Code', row.get('Code équipement', None)))
                model = to_none_if_empty(row.get('Model', row.get('Modèle', None)))
                serial_number = to_none_if_empty(row.get('Serial Number', row.get('Numéro de série', None)))
                manufacturer = to_none_if_empty(row.get('Manufacturer', row.get('Fabricant', None)))
                purchase_date = normalize_date(row.get("Purchase Date", row.get("Date d'achat", None)))
                installation_date = normalize_date(row.get('Installation Date', row.get("Date d'installation", None)))
                location = to_none_if_empty(row.get('Location', row.get('Emplacement', None)))

                raw_status = to_none_if_empty(row.get('Status', row.get('Statut', 'En service')))
                status_map = {
                    'opérationnel': 'En service',
                    'operational': 'En service',
                    'en service': 'En service',
                    'en maintenance': 'En maintenance',
                    'hors service': 'Hors service',
                    'en stock': 'En stock',
                }
                status = 'En service'
                if raw_status:
                    status = status_map.get(raw_status.lower(), 'En service')

                notes = to_none_if_empty(row.get('Notes', row.get('Description', None)))

                try:
                    self.db.add_equipment(
                        name=str(name).strip(),
                        code=code,
                        model=model,
                        serial_number=serial_number,
                        manufacturer=manufacturer,
                        purchase_date=purchase_date,
                        installation_date=installation_date,
                        location=location,
                        status=status,
                        notes=notes,
                    )
                    imported_count += 1
                except Exception as e:
                    # Continuer l'import même si une ligne échoue (ex: code dupliqué)
                    print(f"Erreur import équipement '{name}': {e}")
                    continue

            from PyQt5.QtWidgets import QMessageBox
            if imported_count > 0:
                self.statusBar().showMessage(f"Import réussi : {imported_count} équipements importés", 5000)
                QMessageBox.information(self, "Import équipements",
                                        f"Import terminé avec succès.\n\nÉquipements importés : {imported_count}")
                if self.current_section == 'equipment':
                    self.refresh_current_section()
            else:
                self.statusBar().showMessage("Aucun équipement importé. Vérifiez les colonnes du fichier.", 7000)
                QMessageBox.warning(self, "Import équipements",
                                   "Aucun équipement n'a été importé.\n\nVérifiez que votre fichier contient au moins la colonne 'Name' (ou 'Nom').")
            
        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "Erreur d'import", f"Erreur lors de l'import des équipements : {str(e)}")

    def import_spare_parts_from_dataframe(self, df):
        """Importe des pièces de rechange depuis un DataFrame"""
        try:
            imported_count = 0
            for _, row in df.iterrows():
                name = row.get('Name', row.get('Nom', ''))
                part_number = row.get('Part Number', row.get('Numéro de pièce', ''))
                category = row.get('Category', row.get('Catégorie', ''))
                quantity = row.get('Quantity', row.get('Quantité', 0))
                min_quantity = row.get('Min Quantity', row.get('Quantité minimum', 0))
                location = row.get('Location', row.get('Localisation', ''))
                supplier = row.get('Supplier', row.get('Fournisseur', ''))
                cost = row.get('Cost', row.get('Coût', 0))
                
                if name:  # Ne pas importer les pièces sans nom
                    self.db.add_spare_part(part_number, name, quantity, location, part_type=category, min_threshold=min_quantity, unit_price=cost)
                    imported_count += 1
            
            self.statusBar().showMessage(f"Import réussi : {imported_count} pièces importées", 5000)
            
        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "Erreur d'import", f"Erreur lors de l'import des pièces : {str(e)}")

    def import_equipment_dialog(self):
        """Ouvre le dialog d'importation des équipements"""
        try:
            from gui.equipment_import_dialog import EquipmentImportDialog
            from PyQt5.QtWidgets import QDialog

            dialog = EquipmentImportDialog(self, self.db)
            result = dialog.exec_()

            if result == QDialog.Accepted:
                # Actualiser la liste des équipements si nécessaire
                self.refresh_current_section()

        except Exception as e:
            QMessageBox.critical(
                self,
                "Erreur d'Importation",
                f"Impossible d'ouvrir le dialog d'importation des équipements.\n\n"
                f"Erreur : {str(e)}"
            )

    def show_help(self):
        """Affiche l'aide"""
        if hasattr(self.main_window, 'show_help'):
            self.main_window.show_help()

    # Méthodes optimisées pour éviter les répétitions
    def create_new_equipment(self):
        """Crée un nouvel équipement"""
        try:
            from gui.equipment_manager import EquipmentDialog
            from PyQt5.QtWidgets import QDialog

            dialog = EquipmentDialog(self, self.db)
            if dialog.exec_() == QDialog.Accepted:
                equipment_data = dialog.get_equipment_data()
                try:
                    self.db.add_equipment(**equipment_data)
                    self.statusBar().showMessage("Équipement ajouté avec succès", 3000)
                    
                    # Actualiser l'affichage si on est dans la section équipements
                    if self.current_section == 'equipment':
                        self.refresh_current_section()
                        
                except Exception as e:
                    from PyQt5.QtWidgets import QMessageBox
                    QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ajout de l'équipement : {str(e)}")
        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "Erreur", f"Erreur lors de l'ouverture du dialogue : {str(e)}")

    def schedule_maintenance(self):
        """Planifie une maintenance pour un équipement"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QComboBox, QDateEdit, QTextEdit, QPushButton, QMessageBox
            from PyQt5.QtCore import QDate
            
            # Créer la boîte de dialogue
            dialog = QDialog(self)
            dialog.setWindowTitle("🔧 Planifier une Maintenance")
            dialog.setModal(True)
            dialog.resize(500, 400)
            
            # Layout principal
            layout = QVBoxLayout()
            
            # Sélection de l'équipement
            equipment_layout = QHBoxLayout()
            equipment_label = QLabel("Équipement:")
            equipment_combo = QComboBox()
            equipment_combo.addItem("Sélectionner un équipement", None)
            
            # Récupérer tous les équipements
            equipment_list = self.db.get_all_equipment()
            for equipment in equipment_list:
                equipment_combo.addItem(f"{equipment[1]} ({equipment[2]})", equipment[0])
            
            equipment_layout.addWidget(equipment_label)
            equipment_layout.addWidget(equipment_combo)
            layout.addLayout(equipment_layout)
            
            # Type de maintenance
            type_layout = QHBoxLayout()
            type_label = QLabel("Type de maintenance:")
            type_combo = QComboBox()
            type_combo.addItems(["Préventive", "Corrective", "Prédictive", "Conditionnelle"])
            type_layout.addWidget(type_label)
            type_layout.addWidget(type_combo)
            layout.addLayout(type_layout)
            
            # Date prévue
            date_layout = QHBoxLayout()
            date_label = QLabel("Date prévue:")
            date_edit = QDateEdit()
            date_edit.setDate(QDate.currentDate().addDays(7))
            date_edit.setCalendarPopup(True)
            date_layout.addWidget(date_label)
            date_layout.addWidget(date_edit)
            layout.addLayout(date_layout)
            
            # Description
            desc_label = QLabel("Description:")
            desc_edit = QTextEdit()
            desc_edit.setMaximumHeight(100)
            desc_edit.setPlaceholderText("Décrivez la maintenance à effectuer...")
            layout.addWidget(desc_label)
            layout.addWidget(desc_edit)
            
            # Priorité
            priority_layout = QHBoxLayout()
            priority_label = QLabel("Priorité:")
            priority_combo = QComboBox()
            priority_combo.addItems(["Basse", "Moyenne", "Haute", "Critique"])
            priority_combo.setCurrentText("Moyenne")
            priority_layout.addWidget(priority_label)
            priority_layout.addWidget(priority_combo)
            layout.addLayout(priority_layout)
            
            # Boutons
            button_layout = QHBoxLayout()
            cancel_button = QPushButton("Annuler")
            schedule_button = QPushButton("Planifier")
            schedule_button.setDefault(True)
            
            button_layout.addWidget(cancel_button)
            button_layout.addWidget(schedule_button)
            layout.addLayout(button_layout)
            
            dialog.setLayout(layout)
            
            # Connexions
            cancel_button.clicked.connect(dialog.reject)
            
            def validate_and_schedule():
                equipment_id = equipment_combo.currentData()
                if not equipment_id:
                    QMessageBox.warning(dialog, "Erreur", "Veuillez sélectionner un équipement")
                    return
                
                description = desc_edit.toPlainText().strip()
                if not description:
                    QMessageBox.warning(dialog, "Erreur", "Veuillez saisir une description")
                    return
                
                try:
                    # Créer une tâche de maintenance
                    task_id = self.db.add_task(
                        category_id=1,  # Catégorie par défaut
                        title=f"Maintenance {type_combo.currentText()} - {equipment_combo.currentText().split(' (')[0]}",
                        description=description,
                        due_date=date_edit.date().toString("yyyy-MM-dd"),
                        priority=priority_combo.currentText().lower(),
                        status="À faire"
                    )
                    
                    # Créer une intervention de maintenance
                    self.db.add_maintenance_intervention(
                        task_id=task_id,
                        details=f"Maintenance planifiée: {description}",
                        technician_id=None
                    )
                    
                    QMessageBox.information(dialog, "Succès", "Maintenance planifiée avec succès!")
                    dialog.accept()
                    
                    # Actualiser l'affichage
                    if self.current_section == 'tasks':
                        self.refresh_current_section()
                        
                except Exception as e:
                    QMessageBox.critical(dialog, "Erreur", f"Erreur lors de la planification : {str(e)}")
            
            schedule_button.clicked.connect(validate_and_schedule)
            
            # Afficher la boîte de dialogue
            if dialog.exec_() == QDialog.Accepted:
                self.statusBar().showMessage("Maintenance planifiée", 3000)
                
        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "Erreur", f"Erreur lors de la planification de maintenance : {str(e)}")

    def create_new_person(self):
        """Crée une nouvelle personne"""
        if hasattr(self.main_window, 'add_person'):
            self.main_window.add_person()

    def create_new_spare_part(self):
        """Crée une nouvelle pièce de rechange"""
        if hasattr(self.main_window, 'add_spare_part'):
            self.main_window.add_spare_part()

    def search_in_section(self, section, search_text):
        """Recherche dans une section spécifique"""
        search_methods = {
            'tasks': 'search_tasks',
            'equipment': 'search_equipment',
            'spare_parts': 'search_spare_parts',
            'personnel': 'search_personnel',
            'attendance': 'search_attendance'
        }

        method_name = search_methods.get(section)
        if method_name and hasattr(self.main_window, method_name):
            getattr(self.main_window, method_name)(search_text)
        else:
            self.statusBar().showMessage(f"Recherche dans {section} : {search_text}", 3000)

    def show_global_search_dialog(self, initial_text=""):
        """Affiche une boîte de dialogue de recherche globale"""
        from PyQt5.QtWidgets import QInputDialog

        text, ok = QInputDialog.getText(
            self,
            'Recherche globale',
            'Rechercher dans toute l\'application:',
            text=initial_text
        )

        if ok and text:
            self.statusBar().showMessage(f"Recherche globale : {text}", 3000)
            # Ici on pourrait implémenter une recherche globale dans toutes les sections

    def navigate_to_menu(self, menu_name):
        """Navigue vers le menu sélectionné (compatibilité avec l'ancien système)"""
        # Rediriger vers la nouvelle méthode optimisée
        self.navigate_to_section(menu_name)

    def return_to_home(self):
        """Retourne à l'écran d'accueil et restaure tous les onglets"""
        # Restaurer tous les onglets avant de retourner à l'accueil
        self.restore_all_tabs()
        self.navigate_to_section('home')

    def hide_main_window_toolbars(self):
        """Masque les barres d'outils répétitives de la fenêtre principale"""
        if hasattr(self.main_window, 'findChildren'):
            # Masquer toutes les barres d'outils de la fenêtre principale
            toolbars = self.main_window.findChildren(QToolBar)
            for toolbar in toolbars:
                toolbar.hide()

            # Masquer les menus répétitifs
            if hasattr(self.main_window, 'menuBar'):
                menubar = self.main_window.menuBar()
                if menubar:
                    menubar.hide()

    def hide_tab_specific_toolbars(self, tab_index):
        """Masque les barres d'outils spécifiques à un onglet"""
        if hasattr(self.main_window, 'tab_widget'):
            tab_widget = self.main_window.tab_widget
            current_tab = tab_widget.widget(tab_index)

            if current_tab and hasattr(current_tab, 'findChildren'):
                # Masquer les barres d'outils dans cet onglet
                toolbars = current_tab.findChildren(QToolBar)
                for toolbar in toolbars:
                    toolbar.hide()

                # Masquer les boutons répétitifs
                buttons = current_tab.findChildren(QPushButton)
                for button in buttons:
                    # Masquer les boutons qui dupliquent les actions du menu latéral
                    button_text = button.text().lower()
                    if any(keyword in button_text for keyword in
                           ['nouveau', 'ajouter', 'créer', 'exporter', 'importer', 'rechercher']):
                        button.hide()

    def restore_all_tabs(self):
        """Restaure la visibilité de tous les onglets et leurs éléments"""
        if hasattr(self.main_window, 'tab_widget'):
            tab_widget = self.main_window.tab_widget

            # Restaurer tous les onglets
            for i in range(tab_widget.count()):
                tab_widget.setTabVisible(i, True)

            # Restaurer la barre d'onglets
            tab_widget.tabBar().show()

            # Restaurer tous les sous-onglets
            for i in range(tab_widget.count()):
                current_tab = tab_widget.widget(i)
                if hasattr(current_tab, 'count') and hasattr(current_tab, 'setTabVisible'):
                    for j in range(current_tab.count()):
                        current_tab.setTabVisible(j, True)

            # Restaurer les barres d'outils (optionnel - on peut les laisser masquées)
            # self.restore_main_window_toolbars()

    def open_configuration(self):
        """Ouvre la boîte de dialogue de configuration"""
        from gui.config_dialog import ConfigDialog
        dialog = ConfigDialog(self, self.db)

        # Ajouter un bouton pour changer de thème
        dialog.add_theme_button(self.change_theme)

        dialog.exec_()

    def change_theme(self):
        """Ouvre la boîte de dialogue de sélection de thème"""
        self.theme_manager.show_theme_dialog()

    def refresh_data(self):
        """Actualise toutes les données de l'interface"""
        try:
            print("🔄 Actualisation des données de l'interface principale...")

            # Actualiser la section courante si elle existe
            if hasattr(self, 'stacked_widget') and self.stacked_widget:
                current_widget = self.stacked_widget.currentWidget()
                if current_widget and hasattr(current_widget, 'refresh_data'):
                    current_widget.refresh_data()
                    print(f"✓ Section {current_widget.__class__.__name__} actualisée")

            # Actualiser les statistiques de l'accueil si visible
            if hasattr(self, 'home_widget') and self.home_widget:
                if hasattr(self.home_widget, 'update_statistics'):
                    self.home_widget.update_statistics()
                    print("✓ Statistiques d'accueil actualisées")

            # Actualiser la barre de statut
            if hasattr(self, 'status_bar'):
                self.status_bar.showMessage("Données actualisées", 2000)

            print("✅ Actualisation de l'interface terminée")

        except Exception as e:
            print(f"❌ Erreur lors de l'actualisation : {str(e)}")

def main():
    """Point d'entrée principal de l'application"""
    try:
        # Créer l'application Qt
        app = QApplication(sys.argv)
        app.setApplicationName("SOTRAMINE PHOSPHATE")
        app.setApplicationVersion("2.0")
        app.setStyle("Fusion")  # Style moderne et cohérent

        # Définir la police par défaut
        font = QFont("Segoe UI", 10)
        app.setFont(font)

        # Générer les icônes
        try:
            generate_all_icons()
        except Exception as e:
            print(f"Avertissement: Erreur lors de la génération des icônes: {str(e)}")

        # Créer un écran de démarrage
        splash_pixmap = create_splash_screen()
        splash = QSplashScreen(splash_pixmap)
        splash.show()
        splash.showMessage("Initialisation de la base de données...", Qt.AlignBottom | Qt.AlignCenter, Qt.white)

        # Traiter les événements pour afficher le splash screen
        app.processEvents()

        # Initialiser la base de données
        try:
            db = init_database()
            splash.showMessage("Création de l'exporteur Excel...", Qt.AlignBottom | Qt.AlignCenter, Qt.white)
            app.processEvents()
        except Exception as e:
            splash.close()
            QMessageBox.critical(None, "Erreur Critique", 
                               f"Impossible d'initialiser la base de données:\n{str(e)}")
            return 1

        # Créer l'exporteur Excel
        try:
            excel_exporter = ExcelExporter(db)
            splash.showMessage("Chargement de l'interface...", Qt.AlignBottom | Qt.AlignCenter, Qt.white)
            app.processEvents()
        except Exception as e:
            splash.close()
            QMessageBox.critical(None, "Erreur", 
                               f"Impossible de créer l'exporteur Excel:\n{str(e)}")
            return 1

        # Créer l'application principale optimisée
        try:
            main_app = OptimizedSotramineApp(db, excel_exporter)
            splash.showMessage("Finalisation...", Qt.AlignBottom | Qt.AlignCenter, Qt.white)
            app.processEvents()
        except Exception as e:
            splash.close()
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(None, "Erreur Critique",
                               f"Impossible de créer l'application principale:\n{str(e)}")
            return 1

        # Fermer l'écran de démarrage après un délai et afficher l'application
        def show_main_window():
            try:
                splash.finish(main_app)
                main_app.showMaximized()
            except Exception as e:
                print(f"Erreur lors de l'affichage de la fenêtre principale: {str(e)}")

        QTimer.singleShot(2000, show_main_window)

        # Lancer l'application
        return app.exec_()

    except Exception as e:
        print(f"Erreur critique dans main(): {str(e)}")
        try:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(None, "Erreur Critique",
                               f"Une erreur critique s'est produite:\n{str(e)}")
        except:
            pass
        return 1

def create_splash_screen():
    """Crée l'écran de démarrage de l'application"""
    splash_pixmap = QPixmap(500, 300)
    splash_pixmap.fill(Qt.white)

    # Essayer de charger le logo
    logo_path = "resources/images/logo.png"
    logo = QPixmap(logo_path)
    
    painter = QPainter(splash_pixmap)
    
    if not logo.isNull():
        # Dessiner le logo sur l'écran de démarrage
        logo_scaled = logo.scaled(300, 150, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        logo_x = (splash_pixmap.width() - logo_scaled.width()) // 2
        logo_y = (splash_pixmap.height() - logo_scaled.height()) // 2 - 30
        painter.drawPixmap(logo_x, logo_y, logo_scaled)

        # Ajouter le texte sous le logo
        painter.setPen(QColor("#1976D2"))
        painter.setFont(QFont("Arial", 16, QFont.Bold))
        text_rect = splash_pixmap.rect()
        text_rect.setTop(logo_y + logo_scaled.height() + 10)
        painter.drawText(text_rect, Qt.AlignHCenter, "Système de Gestion de Maintenance")
    else:
        # Dessiner le texte sur l'écran de démarrage si le logo n'est pas disponible
        painter.setPen(QColor("#1976D2"))
        painter.setFont(QFont("Arial", 24, QFont.Bold))
        painter.drawText(splash_pixmap.rect(), Qt.AlignCenter, "SOTRAMINE PHOSPHATE")
        
        # Ajouter un sous-titre
        painter.setFont(QFont("Arial", 12))
        text_rect = splash_pixmap.rect()
        text_rect.setTop(text_rect.center().y() + 30)
        painter.drawText(text_rect, Qt.AlignHCenter, "Système de Gestion de Maintenance")
    
    painter.end()
    return splash_pixmap

if __name__ == "__main__":
    main()