import sys
import os
from PyQt5.QtWidgets import (QApplication, QSplashScreen, QMainWindow, QStackedWidget, QToolBar,
                             QAction, QProgressBar, QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                             QLabel, QFrame, QGridLayout, QMessageBox)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import QPixmap, QFont, QPainter, QColor
from database import Database
from export.excel_export import ExcelExporter
from gui.main_window import MainWindow
from gui.welcome_screen import WelcomeScreen
from gui.icons import generate_all_icons
from gui.theme_manager import ThemeManager
from utils.logger import app_logger, perf_logger
from utils.performance import monitor_performance, time_operation
from config import APP_CONFIG, DEFAULT_CATEGORIES

class DatabaseInitThread(QThread):
    """Thread pour l'initialisation asynchrone de la base de données"""
    progress_updated = pyqtSignal(str)
    initialization_complete = pyqtSignal(object)
    error_occurred = pyqtSignal(str)

    def run(self):
        try:
            self.progress_updated.emit("Initialisation de la base de données...")
            db = init_database()

            self.progress_updated.emit("Optimisation des performances...")
            # Optimiser la base de données au démarrage
            db.vacuum_database()

            self.progress_updated.emit("Vérification de l'intégrité...")
            # Vérifier les statistiques
            stats = db.get_database_stats()
            print(f"Statistiques de la base de données : {stats}")

            self.initialization_complete.emit(db)
        except Exception as e:
            self.error_occurred.emit(str(e))

@monitor_performance("init_database")
def init_database():
    """Initialise la base de données avec les catégories par défaut si nécessaire"""
    try:
        with time_operation("database_connection"):
            db = Database()

        # Vérifier si la base de données est vide
        with time_operation("check_categories"):
            categories = db.get_all_categories()
        
        if not categories:
            app_logger.info("Initialisation des catégories par défaut...")
            
            with time_operation("add_default_categories"):
                for name, color in DEFAULT_CATEGORIES:
                    try:
                        db.add_category(name, color)
                        app_logger.info(f"Catégorie '{name}' ajoutée avec succès")
                    except Exception as e:
                        app_logger.error(f"Erreur lors de l'ajout de la catégorie {name}: {str(e)}")
        else:
            app_logger.info(f"{len(categories)} catégories trouvées dans la base de données")

        return db
    except Exception as e:
        app_logger.critical(f"Erreur critique lors de l'initialisation de la base de données: {str(e)}")
        raise

class OptimizedSotramineApp(QMainWindow):
    """Application principale SOTRAMINE PHOSPHATE optimisée"""
    def __init__(self, db, excel_exporter):
        super().__init__()
        self.db = db
        self.excel_exporter = excel_exporter
        self.current_section = None

        # Initialiser le gestionnaire de thèmes
        self.theme_manager = ThemeManager(self)

        # Gestionnaire d'actions centralisé pour éviter la duplication
        self.action_manager = self.create_action_manager()

        self.setup_ui()

        # Appliquer le thème
        self.theme_manager.apply_theme()

    def create_action_manager(self):
        """Crée le gestionnaire d'actions centralisé"""
        actions = {
            'home': {'text': '🏠 Accueil', 'tooltip': 'Retourner à l\'accueil', 'shortcut': 'Alt+Home'},
            'new_task': {'text': '✚ Nouvelle tâche', 'tooltip': 'Créer une nouvelle tâche', 'shortcut': 'Ctrl+N'},
            'search': {'text': '🔍 Rechercher', 'tooltip': 'Rechercher des éléments', 'shortcut': 'Ctrl+F'},
            'refresh': {'text': '🔄 Actualiser', 'tooltip': 'Actualiser les données', 'shortcut': 'F5'},
            'export': {'text': '📤 Exporter', 'tooltip': 'Exporter les données', 'shortcut': 'Ctrl+E'},
            'import': {'text': '📥 Importer', 'tooltip': 'Importer des données', 'shortcut': 'Ctrl+I'},
            'settings': {'text': '⚙️ Paramètres', 'tooltip': 'Ouvrir les paramètres', 'shortcut': 'Ctrl+,'},
            'help': {'text': '❓ Aide', 'tooltip': 'Afficher l\'aide', 'shortcut': 'F1'}
        }
        return actions

    def setup_ui(self):
        """Configure l'interface utilisateur principale optimisée"""
        self.setWindowTitle("SOTRAMINE PHOSPHATE - Système de Gestion de Maintenance")
        self.setMinimumSize(1400, 900)

        # Widget central principal
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Layout principal horizontal
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Créer le menu latéral optimisé
        self.sidebar = self.create_optimized_sidebar()
        main_layout.addWidget(self.sidebar)

        # Zone de contenu principal
        content_area = QWidget()
        content_layout = QVBoxLayout(content_area)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)

        # Barre d'outils contextuelle unifiée
        self.toolbar = self.create_unified_toolbar()
        content_layout.addWidget(self.toolbar)

        # Widget empilé pour les différentes sections
        self.stacked_widget = QStackedWidget()
        content_layout.addWidget(self.stacked_widget)

        main_layout.addWidget(content_area)

        # Créer les sections optimisées
        self.create_optimized_sections()

        # Barre de statut
        self.setup_status_bar()

        # Afficher l'accueil par défaut
        self.navigate_to_section('home')

    def create_optimized_sidebar(self):
        """Crée un menu latéral optimisé sans répétition"""
        sidebar = QWidget()
        sidebar.setFixedWidth(250)
        sidebar.setStyleSheet("""
            QWidget {
                background-color: #2c3e50;
                color: white;
            }
            QPushButton {
                background-color: transparent;
                border: none;
                padding: 12px 16px;
                text-align: left;
                font-size: 14px;
                color: white;
                border-radius: 6px;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #34495e;
            }
            QPushButton:checked {
                background-color: #3498db;
                font-weight: bold;
            }
        """)

        layout = QVBoxLayout(sidebar)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(4)

        # En-tête
        header = QLabel("SOTRAMINE PHOSPHATE")
        header.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                padding: 16px;
                background-color: #1976D2;
                border-radius: 8px;
                margin-bottom: 16px;
            }
        """)
        header.setAlignment(Qt.AlignCenter)
        layout.addWidget(header)

        # Sections principales (sans duplication)
        self.sidebar_buttons = {}
        sections = [
            ('home', '🏠 Accueil', 'Vue d\'ensemble et navigation'),
            ('tasks', '📋 Tâches', 'Gestion des tâches et projets'),
            ('equipment', '🔌 Équipements', 'Gestion des équipements'),
            ('spare_parts', '🔧 Pièces', 'Inventaire des pièces de rechange'),
            ('personnel', '👥 Personnel', 'Gestion du personnel'),
            ('attendance', '📊 Pointage', 'Suivi de présence'),
            ('reports', '📄 Rapports', 'Rapports et analyses'),
            ('settings', '⚙️ Paramètres', 'Configuration de l\'application')
        ]

        for section_id, title, description in sections:
            btn = QPushButton(title)
            btn.setToolTip(description)
            btn.setCheckable(True)
            btn.clicked.connect(lambda checked, sid=section_id: self.navigate_to_section(sid))
            self.sidebar_buttons[section_id] = btn
            layout.addWidget(btn)

        layout.addStretch()

        # Informations système en bas
        info_label = QLabel("Version 2.1")
        info_label.setStyleSheet("color: #bdc3c7; font-size: 12px; padding: 8px;")
        info_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(info_label)

        return sidebar

    def create_unified_toolbar(self):
        """Crée une barre d'outils unifiée qui s'adapte selon la section"""
        toolbar = QToolBar()
        toolbar.setStyleSheet("""
            QToolBar {
                background-color: #ecf0f1;
                border: none;
                padding: 8px;
                spacing: 4px;
            }
            QToolBar QToolButton {
                background-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 6px;
                padding: 8px 12px;
                margin: 2px;
                font-size: 13px;
            }
            QToolBar QToolButton:hover {
                background-color: #3498db;
                color: white;
                border-color: #2980b9;
            }
            QToolBar QToolButton:pressed {
                background-color: #2980b9;
            }
        """)

        # Actions communes (toujours visibles)
        common_actions = ['home', 'search', 'refresh', 'help']
        for action_id in common_actions:
            action_info = self.action_manager[action_id]
            action = toolbar.addAction(action_info['text'])
            action.setToolTip(action_info['tooltip'])
            if 'shortcut' in action_info:
                action.setShortcut(action_info['shortcut'])
            action.triggered.connect(lambda checked, aid=action_id: self.handle_action(aid))

        toolbar.addSeparator()

        # Zone pour les actions contextuelles (changent selon la section)
        self.contextual_actions_area = toolbar.addWidget(QWidget())

        toolbar.addSeparator()

        # Actions de configuration
        settings_action = toolbar.addAction(self.action_manager['settings']['text'])
        settings_action.setToolTip(self.action_manager['settings']['tooltip'])
        settings_action.setShortcut(self.action_manager['settings']['shortcut'])
        settings_action.triggered.connect(lambda: self.handle_action('settings'))

        return toolbar

    def update_contextual_toolbar(self, section_id):
        """Met à jour la barre d'outils selon la section active"""
        # Supprimer les actions contextuelles précédentes
        if hasattr(self, 'contextual_actions'):
            for action in self.contextual_actions:
                self.toolbar.removeAction(action)

        self.contextual_actions = []

        # Ajouter les actions spécifiques à la section
        contextual_actions = {
            'tasks': ['new_task', 'export', 'import'],
            'equipment': ['export', 'import'],
            'spare_parts': ['export', 'import'],
            'personnel': ['export', 'import'],
            'attendance': ['export'],
            'reports': ['export']
        }

        if section_id in contextual_actions:
            for action_id in contextual_actions[section_id]:
                action_info = self.action_manager[action_id]
                action = self.toolbar.addAction(action_info['text'])
                action.setToolTip(action_info['tooltip'])
                if 'shortcut' in action_info:
                    action.setShortcut(action_info['shortcut'])
                action.triggered.connect(lambda checked, aid=action_id: self.handle_action(aid))
                self.contextual_actions.append(action)

    def create_optimized_sections(self):
        """Crée les sections optimisées sans duplication d'interface"""
        # Section Accueil
        self.home_widget = self.create_home_section()
        self.stacked_widget.addWidget(self.home_widget)

        # Créer et ajouter la fenêtre principale existante (réutilisée)
        self.main_window = MainWindow(self.db, self.excel_exporter)
        self.stacked_widget.addWidget(self.main_window)

        # Mapping des sections vers les onglets de la fenêtre principale
        self.section_mapping = {
            'home': 0,  # Widget d'accueil
            'tasks': (1, 1, 0),  # (widget_index, tab_index, sub_tab_index)
            'equipment': (1, 2, 1),
            'spare_parts': (1, 2, 2),
            'personnel': (1, 3, 0),
            'attendance': (1, 3, 1),
            'reports': (1, 0, 0),
            'settings': 'dialog'
        }

    def create_home_section(self):
        """Crée une section d'accueil optimisée"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(40, 40, 40, 40)
        layout.setSpacing(30)

        # En-tête de bienvenue amélioré
        welcome_label = QLabel("Bienvenue dans SOTRAMINE PHOSPHATE")
        welcome_label.setStyleSheet("""
            QLabel {
                font-size: 36px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 20px;
                padding: 16px;
            }
        """)
        welcome_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(welcome_label)

        subtitle = QLabel("Système de Gestion de Maintenance Industrielle")
        subtitle.setStyleSheet("""
            QLabel {
                font-size: 20px;
                color: #7f8c8d;
                margin-bottom: 40px;
                padding: 8px;
                font-weight: 500;
            }
        """)
        subtitle.setAlignment(Qt.AlignCenter)
        layout.addWidget(subtitle)

        # Ajouter une ligne de séparation décorative
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setStyleSheet("""
            QFrame {
                color: #bdc3c7;
                background-color: #bdc3c7;
                height: 2px;
                margin: 20px 100px;
            }
        """)
        layout.addWidget(separator)

        # Grille de raccourcis rapides avec plus d'espace
        shortcuts_frame = QFrame()
        shortcuts_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f9fa;
                border-radius: 16px;
                padding: 32px;
                margin: 16px;
            }
        """)
        shortcuts_layout = QGridLayout(shortcuts_frame)
        # Espacement augmenté entre les boutons
        shortcuts_layout.setSpacing(24)
        shortcuts_layout.setContentsMargins(20, 20, 20, 20)

        # Raccourcis principaux
        shortcuts = [
            ('tasks', '📋 Gestion des Tâches', 'Créer et suivre les tâches de maintenance'),
            ('equipment', '🔌 Équipements', 'Gérer le parc d\'équipements'),
            ('spare_parts', '🔧 Pièces de Rechange', 'Inventaire et stock des pièces'),
            ('personnel', '👥 Personnel', 'Gestion des équipes'),
            ('attendance', '📊 Pointage', 'Suivi de présence quotidien'),
            ('reports', '📄 Rapports', 'Analyses et tableaux de bord')
        ]

        # Organiser en 2 colonnes au lieu de 3 pour les boutons plus grands
        for i, (section_id, title, description) in enumerate(shortcuts):
            shortcut_btn = self.create_shortcut_button(section_id, title, description)
            row, col = divmod(i, 2)  # 2 colonnes au lieu de 3
            shortcuts_layout.addWidget(shortcut_btn, row, col)

        # Centrer les boutons dans la grille
        shortcuts_layout.setAlignment(Qt.AlignCenter)

        layout.addWidget(shortcuts_frame)
        layout.addStretch()

        return widget

    def create_shortcut_button(self, section_id, title, description):
        """Crée un bouton de raccourci pour l'accueil (taille agrandie)"""
        btn = QPushButton()
        # Taille considérablement agrandie pour une meilleure visibilité
        btn.setFixedSize(420, 180)
        btn.setStyleSheet("""
            QPushButton {
                background-color: white;
                border: 3px solid #e9ecef;
                border-radius: 16px;
                padding: 24px;
                text-align: left;
                font-size: 16px;
                color: #2c3e50;
                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            }
            QPushButton:hover {
                border-color: #3498db;
                background-color: #f8f9ff;
                transform: translateY(-2px);
                box-shadow: 0 6px 12px rgba(52,152,219,0.2);
            }
            QPushButton:pressed {
                background-color: #e3f2fd;
                transform: translateY(0px);
            }
        """)

        # Layout interne du bouton avec plus d'espace
        btn_layout = QVBoxLayout(btn)
        btn_layout.setContentsMargins(16, 16, 16, 16)
        btn_layout.setSpacing(12)

        # Titre plus grand et plus visible
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            font-weight: bold;
            font-size: 20px;
            color: #2c3e50;
            margin-bottom: 8px;
        """)
        btn_layout.addWidget(title_label)

        # Description plus lisible
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            color: #6c757d;
            font-size: 14px;
            line-height: 1.4;
        """)
        desc_label.setWordWrap(True)
        btn_layout.addWidget(desc_label)

        # Ajouter un peu d'espace en bas
        btn_layout.addStretch()

        btn.clicked.connect(lambda: self.navigate_to_section(section_id))
        return btn

    def setup_status_bar(self):
        """Configure la barre de statut"""
        status_bar = self.statusBar()
        status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #34495e;
                color: white;
                border-top: 1px solid #2c3e50;
                padding: 4px;
            }
        """)
        status_bar.showMessage("Prêt - SOTRAMINE PHOSPHATE v2.1")

    def navigate_to_section(self, section_id):
        """Navigation optimisée vers une section"""
        if section_id == self.current_section:
            return

        # Mettre à jour l'état des boutons du sidebar
        for btn_id, btn in self.sidebar_buttons.items():
            btn.setChecked(btn_id == section_id)

        # Mettre à jour la barre d'outils contextuelle
        self.update_contextual_toolbar(section_id)

        # Navigation vers la section
        if section_id == 'home':
            self.stacked_widget.setCurrentIndex(0)
            self.statusBar().showMessage("Accueil - Vue d'ensemble du système")
        elif section_id == 'settings':
            self.open_configuration()
            return
        else:
            # Utiliser la fenêtre principale existante
            self.stacked_widget.setCurrentIndex(1)
            mapping = self.section_mapping.get(section_id)
            if mapping and len(mapping) == 3:
                widget_idx, tab_idx, sub_tab_idx = mapping
                if hasattr(self.main_window, 'tab_widget'):
                    self.main_window.tab_widget.setCurrentIndex(tab_idx)
                    # Gérer les sous-onglets
                    current_tab = self.main_window.tab_widget.widget(tab_idx)
                    if hasattr(current_tab, 'setCurrentIndex'):
                        current_tab.setCurrentIndex(sub_tab_idx)

            # Mettre à jour le message de statut
            status_messages = {
                'tasks': 'Gestion des tâches - Planification et suivi',
                'equipment': 'Gestion des équipements - Maintenance préventive',
                'spare_parts': 'Pièces de rechange - Inventaire et stock',
                'personnel': 'Gestion du personnel - Équipes et compétences',
                'attendance': 'Pointage - Suivi de présence',
                'reports': 'Rapports - Analyses et tableaux de bord'
            }
            self.statusBar().showMessage(status_messages.get(section_id, f"Section {section_id}"))

        self.current_section = section_id

    def handle_action(self, action_id):
        """Gestionnaire centralisé des actions"""
        if action_id == 'home':
            self.navigate_to_section('home')
        elif action_id == 'search':
            self.show_search_dialog()
        elif action_id == 'refresh':
            self.refresh_current_section()
        elif action_id == 'new_task':
            self.create_new_task()
        elif action_id == 'export':
            self.export_current_section()
        elif action_id == 'import':
            self.import_to_current_section()
        elif action_id == 'settings':
            self.open_configuration()
        elif action_id == 'help':
            self.show_help()

    def show_search_dialog(self):
        """Affiche une boîte de dialogue de recherche unifiée"""
        # Implémentation de la recherche globale
        pass

    def refresh_current_section(self):
        """Actualise la section courante"""
        if hasattr(self.main_window, 'refresh_current_tab'):
            self.main_window.refresh_current_tab()
        self.statusBar().showMessage("Données actualisées", 2000)

    def create_new_task(self):
        """Crée une nouvelle tâche"""
        if hasattr(self.main_window, 'create_new_task'):
            self.main_window.create_new_task()

    def export_current_section(self):
        """Exporte les données de la section courante"""
        if hasattr(self.main_window, 'export_current_data'):
            self.main_window.export_current_data()

    def import_to_current_section(self):
        """Importe des données vers la section courante"""
        if hasattr(self.main_window, 'import_data'):
            self.main_window.import_data()

    def show_help(self):
        """Affiche l'aide"""
        if hasattr(self.main_window, 'show_help'):
            self.main_window.show_help()

    def navigate_to_menu(self, menu_name):
        """Navigue vers le menu sélectionné (compatibilité avec l'ancien système)"""
        # Rediriger vers la nouvelle méthode optimisée
        self.navigate_to_section(menu_name)

    def return_to_home(self):
        """Retourne à l'écran d'accueil"""
        self.navigate_to_section('home')

    def open_configuration(self):
        """Ouvre la boîte de dialogue de configuration"""
        from gui.config_dialog import ConfigDialog
        dialog = ConfigDialog(self, self.db)

        # Ajouter un bouton pour changer de thème
        dialog.add_theme_button(self.change_theme)

        dialog.exec_()

    def change_theme(self):
        """Ouvre la boîte de dialogue de sélection de thème"""
        self.theme_manager.show_theme_dialog()

def main():
    """Point d'entrée principal de l'application"""
    try:
        # Créer l'application Qt
        app = QApplication(sys.argv)
        app.setApplicationName("SOTRAMINE PHOSPHATE")
        app.setApplicationVersion("2.0")
        app.setStyle("Fusion")  # Style moderne et cohérent

        # Définir la police par défaut
        font = QFont("Segoe UI", 10)
        app.setFont(font)

        # Générer les icônes
        try:
            generate_all_icons()
        except Exception as e:
            print(f"Avertissement: Erreur lors de la génération des icônes: {str(e)}")

        # Créer un écran de démarrage
        splash_pixmap = create_splash_screen()
        splash = QSplashScreen(splash_pixmap)
        splash.show()
        splash.showMessage("Initialisation de la base de données...", Qt.AlignBottom | Qt.AlignCenter, Qt.white)

        # Traiter les événements pour afficher le splash screen
        app.processEvents()

        # Initialiser la base de données
        try:
            db = init_database()
            splash.showMessage("Création de l'exporteur Excel...", Qt.AlignBottom | Qt.AlignCenter, Qt.white)
            app.processEvents()
        except Exception as e:
            splash.close()
            QMessageBox.critical(None, "Erreur Critique", 
                               f"Impossible d'initialiser la base de données:\n{str(e)}")
            return 1

        # Créer l'exporteur Excel
        try:
            excel_exporter = ExcelExporter(db)
            splash.showMessage("Chargement de l'interface...", Qt.AlignBottom | Qt.AlignCenter, Qt.white)
            app.processEvents()
        except Exception as e:
            splash.close()
            QMessageBox.critical(None, "Erreur", 
                               f"Impossible de créer l'exporteur Excel:\n{str(e)}")
            return 1

        # Créer l'application principale optimisée
        try:
            main_app = OptimizedSotramineApp(db, excel_exporter)
            splash.showMessage("Finalisation...", Qt.AlignBottom | Qt.AlignCenter, Qt.white)
            app.processEvents()
        except Exception as e:
            splash.close()
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(None, "Erreur Critique",
                               f"Impossible de créer l'application principale:\n{str(e)}")
            return 1

        # Fermer l'écran de démarrage après un délai et afficher l'application
        def show_main_window():
            try:
                splash.finish(main_app)
                main_app.showMaximized()
            except Exception as e:
                print(f"Erreur lors de l'affichage de la fenêtre principale: {str(e)}")

        QTimer.singleShot(2000, show_main_window)

        # Lancer l'application
        return app.exec_()

    except Exception as e:
        print(f"Erreur critique dans main(): {str(e)}")
        try:
            QMessageBox.critical(None, "Erreur Critique", 
                               f"Une erreur critique s'est produite:\n{str(e)}")
        except:
            pass
        return 1

def create_splash_screen():
    """Crée l'écran de démarrage de l'application"""
    splash_pixmap = QPixmap(500, 300)
    splash_pixmap.fill(Qt.white)

    # Essayer de charger le logo
    logo_path = "resources/images/logo.png"
    logo = QPixmap(logo_path)
    
    painter = QPainter(splash_pixmap)
    
    if not logo.isNull():
        # Dessiner le logo sur l'écran de démarrage
        logo_scaled = logo.scaled(300, 150, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        logo_x = (splash_pixmap.width() - logo_scaled.width()) // 2
        logo_y = (splash_pixmap.height() - logo_scaled.height()) // 2 - 30
        painter.drawPixmap(logo_x, logo_y, logo_scaled)

        # Ajouter le texte sous le logo
        painter.setPen(QColor("#1976D2"))
        painter.setFont(QFont("Arial", 16, QFont.Bold))
        text_rect = splash_pixmap.rect()
        text_rect.setTop(logo_y + logo_scaled.height() + 10)
        painter.drawText(text_rect, Qt.AlignHCenter, "Système de Gestion de Maintenance")
    else:
        # Dessiner le texte sur l'écran de démarrage si le logo n'est pas disponible
        painter.setPen(QColor("#1976D2"))
        painter.setFont(QFont("Arial", 24, QFont.Bold))
        painter.drawText(splash_pixmap.rect(), Qt.AlignCenter, "SOTRAMINE PHOSPHATE")
        
        # Ajouter un sous-titre
        painter.setFont(QFont("Arial", 12))
        text_rect = splash_pixmap.rect()
        text_rect.setTop(text_rect.center().y() + 30)
        painter.drawText(text_rect, Qt.AlignHCenter, "Système de Gestion de Maintenance")
    
    painter.end()
    return splash_pixmap

if __name__ == "__main__":
    main()